package om.rrtx.mobile.homemodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;

/**
 * <AUTHOR>
 */
public class PhoneDialog extends Dialog {

    private Context mContext;
    private TextView phoneTv;
    private TextView cancelTv;
    private String mobile;

    public PhoneDialog(@NonNull Context context,String phone) {
        super(context, R.style.transparentDialog);
        mContext = context;
        mobile = phone;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.home_dialog_phone);
        setCancelable(true);
        setCanceledOnTouchOutside(true);
        setLocation();
        initView();
        initListener();
    }

    private void setLocation() {
        Window window = getWindow();
        if (window != null) {
            window.setGravity(Gravity.BOTTOM);
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
            WL.width = ConstraintLayout.LayoutParams.MATCH_PARENT;
            window.setAttributes(WL);
        }
    }

    private void initView() {
        phoneTv = findViewById(R.id.phoneTv);
        cancelTv = findViewById(R.id.cancelTv);
        if (!TextUtils.isEmpty(mobile)){
            phoneTv.setText(mobile);
        }else{
            phoneTv.setText(mContext.getResources().getString(R.string.contact_label_service_phone));
        }
    }

    private void initListener() {
        phoneTv.setOnClickListener(mClickListener);
        cancelTv.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.phoneTv) {
                Intent intent = new Intent(Intent.ACTION_DIAL);
                Uri data = Uri.parse("tel:" + phoneTv.getText().toString());
                intent.setData(data);
                mContext.startActivity(intent);
            }
            dismiss();
        }
    };
}
