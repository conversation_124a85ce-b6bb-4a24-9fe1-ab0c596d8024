package om.rrtx.mobile.homemodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;

import java.util.concurrent.ExecutionException;

import cn.hutool.extra.qrcode.QrCodeException;
import cn.hutool.extra.qrcode.QrCodeUtil;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.BitmapUtils;
import om.rrtx.mobile.rrtxcommon1.utils.QRCodeEncoder;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.ThreadPoolManager;
import om.rrtx.mobile.rrtxcommon1.utils.ViThreadPoolManager;

/**
 * 用户二维码对话框
 */
public class UserQrCodeDialog extends Dialog {

    private String mQrCode;
    private Context mContext;
    private ImageView mQrCodeIv;
    private ImageView mCloseIv;
    private Handler mHandler = new Handler(Looper.getMainLooper());

    public UserQrCodeDialog(@NonNull Context context, String qrCode) {
        super(context, R.style.transparentDialog);
        mContext = context;
        mQrCode = qrCode;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.home_dialog_user_qr_code);

        setLocation();

        initView();

        initData();

        initListener();
    }

    /**
     * 初始化控件
     */
    private void initView() {
        mQrCodeIv = findViewById(R.id.qrCodeIv);
        mCloseIv = findViewById(R.id.closeIv);
    }

    private void initData() {

        ViThreadPoolManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                String userHeard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
                Bitmap bitmap;
                try {
                    bitmap = Glide.with(mContext).asBitmap().load(userHeard).centerCrop().into((int) mContext.getResources().getDimension(R.dimen.home_scan_qr_code_price_size),
                            (int) mContext.getResources().getDimension(R.dimen.home_scan_qr_code_price_size))
                            .get();
                } catch (Exception e) {
                    e.printStackTrace();
                    bitmap = BitmapFactory.decodeResource(mContext.getResources(), R.drawable.common_ic_head_default);
                }
                bitmap = BitmapUtils.getRoundedCornerBitmap(bitmap, mContext.getResources().getDimension(R.dimen.home_scan_qr_code_price_round_size));
                Bitmap qrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(mQrCode,
                        (int) mContext.getResources().getDimension(R.dimen.home_scan_qr_code_size),
                        mContext.getResources().getColor(R.color.color_131313),
                        mContext.getResources().getColor(R.color.color_FFFFFF),
                        bitmap);

                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        mQrCodeIv.setImageBitmap(qrCodeBitmap);
                    }
                });
            }
        });
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }

    private void initListener() {
        mCloseIv.setOnClickListener((view -> {
            dismiss();
        }));
    }
}
