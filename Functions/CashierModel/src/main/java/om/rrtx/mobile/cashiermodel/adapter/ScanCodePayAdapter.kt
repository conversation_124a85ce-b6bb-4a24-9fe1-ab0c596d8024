package om.rrtx.mobile.cashiermodel.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.cashiermodel.databinding.ItemScanCodePayBinding
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

class ScanCodePayAdapter :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var data: ArrayList<InfoItemBean> = arrayListOf()

    lateinit var context: Context
    lateinit var inflater: LayoutInflater

    fun setNewData(list: ArrayList<InfoItemBean>) {
        data = list
        notifyDataSetChanged()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
        inflater = LayoutInflater.from(context)
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val binding = ItemScanCodePayBinding.inflate(inflater, parent, false)
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemScanCodePayBinding>(holder.itemView) as ItemScanCodePayBinding

        val bean = data[position]
        binding.iconIv.setBackgroundResource(bean.iconId)
        binding.titleTv.text = bean.title
        binding.textTv.text = bean.text
        if (bean.type == 3) {
            binding.moreIv.visibility = View.GONE
        } else {
            binding.moreIv.visibility = View.VISIBLE
            object : CustomClickListener() {
                override fun onSingleClick(view: View?) {
                    //  跳转营销页面
                    bean.callBack!!()
                }
            }.apply {
                binding.moreIv.setOnClickListener(this)
                binding.textTv.setOnClickListener(this)
            }
        }
    }
}