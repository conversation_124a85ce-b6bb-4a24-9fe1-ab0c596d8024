package om.rrtx.mobile.cashiermodel.model;

import android.text.TextUtils;

import java.util.HashMap;
import java.util.Map;

import om.rrtx.mobile.cashiermodel.CashierService;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;

/**
 * <AUTHOR>
 */
public class CashierModel extends BaseLoader {
    private CashierService mCashierService;

    public CashierModel() {
        mCashierService = RetrofitServiceManager.getInstance().create(CashierService.class);
    }

    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mCashierService.requestPub(map)).subscribe(baseObserver);
    }


}
