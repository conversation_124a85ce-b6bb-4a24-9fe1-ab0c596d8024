package om.rrtx.mobile.cashiermodel.presenter;

import android.content.Context;

import om.rrtx.mobile.cashiermodel.model.CashierModel;
import om.rrtx.mobile.cashiermodel.view.ScanCashierView;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 */
public class ScanCashierPresenter extends BasePresenter<ScanCashierView> {

    private CashierModel mCashierModel;
    private Context mContext;

    public ScanCashierPresenter(Context context) {
        mCashierModel = new CashierModel();
        mContext = context;
    }

    /**
     * 查询支付订单信息
     *
     * @param orderSource 订单类型
     * @param payToken    订单的token
     */
    public void requestOrderInfo(String orderSource, String payToken) {
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        new CommonModel().requestOrderInfo(userId, orderSource, payToken, new BaseObserver<OrderInfoBean>(mContext) {
            @Override
            public void requestFail(String sResMsg) {
                if (getView() != null) {
                    getView().orderInfoFail(sResMsg);
                }
            }

            @Override
            public void requestSuccess(OrderInfoBean orderInfoBean) {
                if (getView() != null) {
                    getView().orderInfoSuccess(orderInfoBean);
                }
            }
        });
    }

}
