package om.rrtx.mobile.cashiermodel.activity;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import om.rrtx.mobile.cashiermodel.CashierConstants;
import om.rrtx.mobile.cashiermodel.ICashierAidlInterface;
import om.rrtx.mobile.cashiermodel.R;
import om.rrtx.mobile.cashiermodel.R2;
import om.rrtx.mobile.cashiermodel.presenter.ScanCashierPresenter;
import om.rrtx.mobile.rrtxcommon1.utils.AppUtils;
import om.rrtx.mobile.cashiermodel.view.ScanCashierView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.XWalletPayBean;
import om.rrtx.mobile.functioncommon.dialog.CashierDetailsBottomFragment;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收银台页面
 */
@Route(path = ARouterPath.Cashier.CashierActivity)
public class CashierActivity extends BaseSuperActivity<ScanCashierView, ScanCashierPresenter> implements ScanCashierView {

    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.heardIv)
    CircleImageView mHeardIv;
    @BindView(R2.id.userNameTv)
    TextView mUserNameTv;
    private CashierDetailsBottomFragment mOrderInfoDialog;
    private boolean mIsSuccess;
    private ICashierAidlInterface mICashierAidlInterface;
    private XWalletPayBean mXWalletPayBean;
    private CashierManager mCashierManager;
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mICashierAidlInterface = ICashierAidlInterface.Stub.asInterface(service);
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

        }
    };


    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String xWallerPayBeanJson = getIntent().getStringExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON);
        if (!TextUtils.isEmpty(xWallerPayBeanJson)) {
            SharedPreferencesUtils.setParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, xWallerPayBeanJson);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        String xWallerPayBeanJson = intent.getStringExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON);
        if (!TextUtils.isEmpty(xWallerPayBeanJson)) {
            SharedPreferencesUtils.setParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, xWallerPayBeanJson);
        }
    }

    @Override
    protected int createContentView() {
        return R.layout.cashier_activity_cashier;
    }

    @Override
    protected ScanCashierPresenter createPresenter() {
        return new ScanCashierPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();

        startBindService();
    }

    private void startBindService() {
        String xWallerPayBeanJson = (String) SharedPreferencesUtils.getParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, "");
        if (!TextUtils.isEmpty(xWallerPayBeanJson)) {

            mXWalletPayBean = new Gson().fromJson(xWallerPayBeanJson, XWalletPayBean.class);
            QueryOrderBean orderQueryBean = new QueryOrderBean(mXWalletPayBean.getPayToken(), mXWalletPayBean.getTransOrderNo(), "1",mXWalletPayBean.getPackName());
            ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                    .withString(BaseConstants.Transmit.JSON, new Gson()
                            .toJson(orderQueryBean)).navigation(mContext, CommonConstants.ResultCode.Request);
            finish();
//            Intent intent = new Intent();
//            //这里说明一下,前面的是项目的包名,后面的是相应AIDL绑定服务的名(全路径哦)
//            String packName = mXWalletPayBean.getPackName();
//            if (!TextUtils.isEmpty(packName)) {
//                intent.setComponent(new ComponentName(packName, CashierConstants.SERVICEPATH));
//                mIsSuccess = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
//            }
        }
    }

    @Override
    public void initDate() {
        super.initDate();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void initListener() {
        super.initListener();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mIsSuccess) {
            unbindService(serviceConnection);
        }
    }

    @Override
    public void orderInfoSuccess(OrderInfoBean orderInfoBean) {

    }

    @Override
    public void orderInfoFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode != CommonConstants.ResultCode.Request) return;
        if (resultCode == CommonConstants.ResultCode.Success) {
            String dataJson = data.getStringExtra(CommonConstants.Transmit.JSON);
            XWalletPayBean xWalletPayBean = new Gson().fromJson(dataJson, XWalletPayBean.class);
            xWalletPayBean.setPackName(mXWalletPayBean.getPackName());
            //支付成功的回调
            CashierSuccessActivity.jumpCashierSuccess(mContext, new Gson().toJson(xWalletPayBean));
            finish();
        } else {
            try {
                if (mIsSuccess) {
                    mICashierAidlInterface.payResult(CashierConstants.OrderPayStatus.CANCEL, CashierConstants.OrderPayError.CANCEL);
                    if (mXWalletPayBean != null) {
                        Intent intent = AppUtils.getAppOpenIntentByPackageName(mContext, mXWalletPayBean.getPackName());
                        startActivity(intent);
                    }
                    finish();
                    //清除之前保存的相应数据
                    SharedPreferencesUtils.setParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, "");
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }
}
