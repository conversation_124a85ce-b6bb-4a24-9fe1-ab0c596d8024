package om.rrtx.mobile.cashiermodel.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;

import com.rrtx.xwalletpaylib.IAppUserInfoInterface;

import om.rrtx.mobile.cashiermodel.CashierConstants;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;


/**
 * <AUTHOR>
 * 提供app状态的Service
 */
public class AidlAppInfoService extends Service {

    private IAppUserInfoInterface.Stub mIAppUserInfoInterface = new IAppUserInfoInterface.Stub() {
        @Override
        public String getAppPagePath(String type) throws RemoteException {
            String userName = (String) SharedPreferencesUtils.getParam(AidlAppInfoService.this, BaseConstants.SaveParameter.USERNAME, "");
            String token = (String) SharedPreferencesUtils.getParam(AidlAppInfoService.this, BaseConstants.SaveParameter.AUTHORIZATION, "");

            if (TextUtils.isEmpty(userName) && TextUtils.isEmpty(token)) {
                //登录页面
                return CashierConstants.LOGINACTIVITY;
            } else {
                if (TextUtils.isEmpty(token)) {
                    //密码登录
                    return CashierConstants.LOGINPASSWORDLOCKACTIVITY;
                } else {
                    //收银台页面
                    return getPath(type);
                }
            }
        }
    };

    public String getPath(String type) {
        switch (type) {
            case CommonConstants.ExtJumpType.Jump_App_Pay:
                return CashierConstants.CASHIERACTIVITY;
            case CommonConstants.ExtJumpType.AUTO_Debit_Sign:
                return CashierConstants.Auto_Debit_Activity;
            default:
                return "";
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mIAppUserInfoInterface;
    }
}
