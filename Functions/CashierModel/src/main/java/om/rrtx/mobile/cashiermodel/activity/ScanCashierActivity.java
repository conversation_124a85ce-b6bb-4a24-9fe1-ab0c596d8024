package om.rrtx.mobile.cashiermodel.activity;

import android.text.TextUtils;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import om.rrtx.mobile.cashiermodel.CashierConstants;
import om.rrtx.mobile.cashiermodel.R;
import om.rrtx.mobile.cashiermodel.R2;
import om.rrtx.mobile.cashiermodel.presenter.ScanCashierPresenter;
import om.rrtx.mobile.cashiermodel.utils.LogUtil;
import om.rrtx.mobile.cashiermodel.view.ScanCashierView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.bean.OutJumpBean;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.XWalletPayBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 扫码页面的付款
 */
@Route(path = ARouterPath.Cashier.ScanCashierActivity)
public class ScanCashierActivity extends BaseSuperActivity<ScanCashierView, ScanCashierPresenter>
        implements ScanCashierView {

    @BindView(R2.id.heardIv)
    CircleImageView mHeardIv;
    @BindView(R2.id.userNameTv)
    TextView mUserNameTv;
    private XWalletPayBean mXWalletPayBean;
    private CashierManager mCashierManager;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String xWallerPayBeanJson = getIntent().getStringExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON);

        if (!TextUtils.isEmpty(xWallerPayBeanJson)) {
            OutJumpBean outJumpBean = new Gson().fromJson(xWallerPayBeanJson, OutJumpBean.class);

            mXWalletPayBean = new XWalletPayBean.Builder()
                    .setPayToken(outJumpBean.getPayToken())
                    .setOrderSource(CommonConstants.OrderSource.EXTERNALORDER)
                    .setPaymentProduct(CommonConstants.PaymentProduct.APP)
                    .setOutType(outJumpBean.getIsOutJump())
                    .setCurrency(outJumpBean.getCurrency())
                    .builder();
        }
    }

    @Override
    protected int createContentView() {
        return R.layout.cashier_activity_cashier;
    }

    @Override
    protected ScanCashierPresenter createPresenter() {
        return new ScanCashierPresenter(mContext);
    }

    @Override
    protected void initView() {
        //状态栏
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();
    }

    @Override
    public void initDate() {
        super.initDate();
        String heardUrl = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
        ImageLoaderManager.getInstance().disPlayImage(mContext, heardUrl, R.drawable.common_ic_head_default, mHeardIv);

        //设置姓名
        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
        if (!TextUtils.isEmpty(userName)) {
            mUserNameTv.setText(userName);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mPresenter.requestOrderInfo(mXWalletPayBean.getOrderSource(), mXWalletPayBean.getPayToken());
    }

    @Override
    public void orderInfoSuccess(OrderInfoBean orderInfoBean) {
        CashierOrderInfoBean cashierOrderInfoBean = new CashierOrderInfoBean.Builder()
                .setPaymentProduct(CommonConstants.PaymentProduct.APP)
                .setPayType(CommonConstants.CashierPaymentType.Cashier_Payment)
                .setOrderAmt(orderInfoBean.getAmt())
                .setOrderNo(orderInfoBean.getOrderNo())
                .setOrderSource(CommonConstants.OrderSource.EXTERNALORDER)
                .setMerName(orderInfoBean.getMerName())
                .setTransType(CommonConstants.TransType.Payment)
                .setCurrency(CurrencyUtils.setCurrency(mContext, orderInfoBean.getCurrency()))
                .setMerNo(orderInfoBean.getMerNo())
                .setTransferToken(orderInfoBean.getOrderNo())
                .setPaymentToken(mXWalletPayBean.getPayToken())
                .builder();

        mCashierManager = new CashierManager(this, new Gson().toJson(cashierOrderInfoBean), new CashierCallBack() {
            @Override
            public void paymentFailed(@NonNull String message) {

            }

            @Override
            public void cancelOrderPay() {
                mCashierManager.dismiss();
                //取消
                finish();
                if (TextUtils.equals(mXWalletPayBean.getOutType(), BaseConstants.QrType.WEBTOKEN)) {
                    moveTaskToBack(true);
                }
            }

            @Override
            public void forgotCallBack() {
                ARouter.getInstance()
                        .build(ARouterPath.SecurityPath.PayPinSendActivity)
                        .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.CASHIERACTIVITY)
                        .navigation();
            }

            @Override
            public void paymentSuccess(String dataJson) {
                LogUtil.e("TAG", "paymentSuccess: " + dataJson);
                ScanCashierSuccessActivity.jumpScanCashierSuccess(mContext, dataJson, mXWalletPayBean.getOutType());
                finish();
            }
        });
        mCashierManager.showCashierDialog();
    }

    @Override
    public void orderInfoFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
        finish();
        if (TextUtils.equals(mXWalletPayBean.getOutType(), BaseConstants.QrType.WEBTOKEN)) {
            moveTaskToBack(true);
        }
    }
}
