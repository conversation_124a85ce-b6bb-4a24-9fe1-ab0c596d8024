package om.rrtx.mobile.cashiermodel.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;

import butterknife.BindView;
import om.rrtx.mobile.cashiermodel.R;
import om.rrtx.mobile.cashiermodel.R2;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;

/**
 * <AUTHOR>
 * 扫码支付成功页面
 */
public class ScanCashierSuccessActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    @BindView(R2.id.moneyTv)
    TextView mMoneyTv;
    @BindView(R2.id.moneyTypeTv)
    TextView mMoneyTypeTv;
    @BindView(R2.id.orderAmt)
    TextView mOrderAmt;
    @BindView(R2.id.orderAmtTv)
    TextView mOrderAmtTv;
    @BindView(R2.id.fee)
    TextView mFee;
    @BindView(R2.id.feeTv)
    TextView mFeeTv;
    @BindView(R2.id.showContentCl)
    ConstraintLayout mShowContentCl;
    private CashierOrderInfoBean mCashierOrderInfoBean;
    private String mOutType;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String orderInfoJson = getIntent().getStringExtra(CommonConstants.Transmit.CASHIERORDERINFO);
        if (!TextUtils.isEmpty(orderInfoJson)) {
            LogUtil.e("TAG", "doGetExtra: " + orderInfoJson);
            mCashierOrderInfoBean = new Gson().fromJson(orderInfoJson, CashierOrderInfoBean.class);
        }

        mOutType = getIntent().getStringExtra(CommonConstants.Transmit.OUTTYPE);
    }

    public static void jumpScanCashierSuccess(Context context, String orderInfoJson, String outType) {
        Intent intent = new Intent(context, ScanCashierSuccessActivity.class);
        intent.putExtra(CommonConstants.Transmit.CASHIERORDERINFO, orderInfoJson);
        intent.putExtra(CommonConstants.Transmit.OUTTYPE, outType);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.cashier_activity_scan_cashier_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();

        mBackIv.setVisibility(View.GONE);
        mTitleTv.setText(R.string.success);
    }

    @Override
    public void initDate() {
        super.initDate();
        if (mCashierOrderInfoBean != null) {
            mMoneyTv.setText(mCashierOrderInfoBean.getActAmount());
//            String currencyStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, "");
//            currencyStr = CurrencyUtils.setCurrency(mContext, currencyStr);
            mMoneyTypeTv.setText(CurrencyUtils.setCurrency(mContext, mCashierOrderInfoBean.getCurrency()));

            //设置费率
            String feeAmount = mCashierOrderInfoBean.getPayFeeAmount();
            if (!TextUtils.isEmpty(feeAmount)) {
                BigDecimal feeAmtBd = new BigDecimal(feeAmount);
                int i = feeAmtBd.compareTo(BigDecimal.ZERO);
                if (i > 0) {
                    String feeStr = feeAmount + " " + CurrencyUtils.setCurrency(mContext, mCashierOrderInfoBean.getCurrency());
                    mFeeTv.setText(feeStr);
                    //设置订单金额
                    String orderAmtStr = mCashierOrderInfoBean.getOrderAmount() + " " + CurrencyUtils.setCurrency(mContext, mCashierOrderInfoBean.getCurrency());
                    mOrderAmtTv.setText(orderAmtStr);
                    mShowContentCl.setVisibility(View.VISIBLE);
                } else {
                    mShowContentCl.setVisibility(View.GONE);
                }
            } else {
                mShowContentCl.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void initListener() {
        mDoneTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                finish();
                if (TextUtils.equals(mOutType, BaseConstants.QrType.WEBTOKEN)) {
                    moveTaskToBack(true);
                }
            }
        });
    }
}
