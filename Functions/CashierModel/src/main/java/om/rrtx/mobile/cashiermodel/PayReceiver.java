package om.rrtx.mobile.cashiermodel;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;

import java.util.List;

import om.rrtx.mobile.cashiermodel.activity.CashierActivity;
import om.rrtx.mobile.cashiermodel.utils.LogUtil;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;


/**
 * <AUTHOR>
 * 接收支付吊起App的广播
 */
public class PayReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtil.e("接收广播", "onReceive: ");
        if (intent != null) {
            String xWallerPayBeanJson = intent.getStringExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON);
            SharedPreferencesUtils.setParam(context, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, xWallerPayBeanJson);
            statusJump(context, new LoginStatusCallBack() {
                @Override
                public void jumpLogin() {
                    ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                            .navigation();
                }

                @Override
                public void jumpPsdLogin() {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.CashierJump)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                            .navigation();
                }

                @Override
                public void jumpCashier() {
                    Intent intent = new Intent(context, CashierActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    context.startActivity(intent);
                }
            });
        }
    }

    /**
     * 状态跳转判断
     */
    public void statusJump(Context context, LoginStatusCallBack callBack) {
        String userName = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERNAME, "");
        String token = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.AUTHORIZATION, "");

        if (TextUtils.isEmpty(userName) && TextUtils.isEmpty(token)) {
            callBack.jumpLogin();
        } else {
            if (TextUtils.isEmpty(token)) {
                callBack.jumpPsdLogin();
            } else {
                callBack.jumpCashier();
            }
        }
    }

    public interface LoginStatusCallBack {
        /**
         * 登录页面
         */
        void jumpLogin();

        /**
         * 密码登录
         */
        void jumpPsdLogin();

        /**
         * 收银台页面
         */
        void jumpCashier();
    }
}
