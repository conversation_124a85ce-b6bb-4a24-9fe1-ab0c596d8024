package om.rrtx.mobile.cashiermodel;

import java.util.Map;

import io.reactivex.Observable;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import retrofit2.Response;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * 登录的Api接口层
 */
public interface CashierService {
    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CashierConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);
}
