package om.rrtx.mobile.cashiermodel.activity

import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Paint
import android.os.IBinder
import android.os.RemoteException
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import com.kapp.xmarketing.CouponCallback
import com.kapp.xmarketing.ShowMarketingManager
import com.kapp.xmarketing.bean.CouponBean
import com.kapp.xmarketing.bean.CouponBean.UseListBean
import com.kapp.xmarketing.bean.DirectReductionBean
import com.kapp.xmarketing.bean.GoodsBean
import com.kapp.xmarketing.bean.UseCouponsBean
import com.kapp.xmarketing.bean.UsePointsBean
import kotlinx.android.synthetic.main.cashier_base_title.backIv
import kotlinx.android.synthetic.main.cashier_base_title.leftBg
import kotlinx.android.synthetic.main.cashier_base_title.titleTv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.icon_iv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.name_tv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.newMoney_tv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.next_tv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.oldMoney_tv
import kotlinx.android.synthetic.main.payment_activity_scan_code_pay.rv
import om.rrtx.mobile.cashiermodel.CashierConstants
import om.rrtx.mobile.cashiermodel.ICashierAidlInterface
import om.rrtx.mobile.cashiermodel.R
import om.rrtx.mobile.cashiermodel.adapter.ScanCodePayAdapter
import om.rrtx.mobile.cashiermodel.databinding.PaymentActivityScanCodePayBinding
import om.rrtx.mobile.cashiermodel.vm.ScanCodePayVM
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.activity.ComResultActivity
import om.rrtx.mobile.functioncommon.bean.AirtimeSuccessBean
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.ComResultBean
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean
import om.rrtx.mobile.functioncommon.bean.ZesaResultBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack1
import om.rrtx.mobile.functioncommon.utils.CashierManager1
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.bean.MarketingBean
import om.rrtx.mobile.rrtxcommon1.bean.OrderSuccessBean
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean
import om.rrtx.mobile.rrtxcommon1.bean.RequestUseMarketingBean
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController
import om.rrtx.mobile.rrtxcommon1.utils.AppUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils

@Route(path = ARouterPath.Cashier.CashierPayActivity)
class CashierPayActivity : BaseVVMActivity<ScanCodePayVM, PaymentActivityScanCodePayBinding>() {

    private lateinit var mQueryOrderBean: QueryOrderBean
    private lateinit var mOrderInfoBean: OrderInfoBean
    private lateinit var mFronType: String
    private lateinit var mFronScanOrderInfoBean: OrderInfoBean
    private lateinit var mMarketingBean: MarketingBean

    private lateinit var mScanCodePayAdapter: ScanCodePayAdapter

    private lateinit var mICashierAidlInterface: ICashierAidlInterface

    private var mIsSuccess = false

    //
//
    private val serviceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            mICashierAidlInterface = ICashierAidlInterface.Stub.asInterface(service)
        }

        override fun onServiceDisconnected(name: ComponentName) {}
    }

    override fun createContentView() = R.layout.payment_activity_scan_code_pay

    override fun doGetExtra() {
        mFronType = intent.getStringExtra(BaseConstants.Transmit.FROMTYPE).toString()
        if (!TextUtils.isEmpty(mFronType) && "scan" == mFronType) {
            val oederJson = intent.getStringExtra(BaseConstants.Transmit.ORDERJSON)
            mFronScanOrderInfoBean = Gson().fromJson(oederJson, OrderInfoBean::class.java)
        }
        val json = intent.getStringExtra(BaseConstants.Transmit.JSON)
        mQueryOrderBean = Gson().fromJson(json, QueryOrderBean::class.java)
        if (mQueryOrderBean.isExtAppJump()) {
            startService()
        }

    }

    override fun initView() {
        ImmersionBar.with(this).statusBarView(R.id.statusView).statusBarDarkFont(true).init()

        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
            titleTv.text = getString(R.string.using_code)
        } else {
            titleTv.text = getString(R.string.payment)
        }
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)

        rv.apply {
            layoutManager = LinearLayoutManager(mContext)
            mScanCodePayAdapter = ScanCodePayAdapter()
            adapter = mScanCodePayAdapter
        }
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    next_tv -> {
                        showCashier()
                    }

                    leftBg -> {
                        onBackPressed()
                    }
                }
            }
        }.apply {
            next_tv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }
    }

    override fun initData() {
        when (val paymentProduct = mQueryOrderBean.paymentProduct) {
            CommonConstants.PaymentProduct.BARCODE, //  商户扫个人付款码
            CommonConstants.PaymentProduct.QRCODE   // 个人扫商户收款码
            -> {
                viewModel.requestCreateQrCodeOrder(
                    paymentProduct,
                    mQueryOrderBean.merNo,
                    mQueryOrderBean.merName,
                    mQueryOrderBean.checkstandNo,
                    mQueryOrderBean.amt,
                    mQueryOrderBean.qrCode,
                    mQueryOrderBean.currency
                )
            }

            CommonConstants.PaymentProduct.MERCHANT_CODE_PAY   // 汇款吗付款
            -> {
                /*viewModel.checkMerCodePayOrder(
                    mQueryOrderBean.orderNo,
                    mQueryOrderBean.amt,
                    mQueryOrderBean.currency
                )*/
                val oederJson = intent.getStringExtra(BaseConstants.Transmit.ORDERJSON)
                mOrderInfoBean = Gson().fromJson(oederJson, OrderInfoBean::class.java)
                icon_iv.setBackgroundResource(R.drawable.ic_mer)
                name_tv.text = mOrderInfoBean.merName
                viewModel.queryMarketingInfo("", mOrderInfoBean.trxOrderNo, "30")
            }

            CommonConstants.PaymentProduct.ORDER_ZESA   // 电费支付
            -> {
                viewModel.checkZesaPayOrder(
                    mQueryOrderBean.merNo,
                    mQueryOrderBean.merName,
                    mQueryOrderBean.amt,
                    mQueryOrderBean.currency
                )
            }

            CommonConstants.PaymentProduct.ORDER_BUND   // 话费支付
            -> {
                viewModel.checkBuyAirtimeOrBundle(
                    mQueryOrderBean.orderSource,
                    mQueryOrderBean.merNo,
                    mQueryOrderBean.amt,
                    mQueryOrderBean.currency,
                    mQueryOrderBean.qrCode,
                    mQueryOrderBean.checkstandNo
                )
            }

            else -> {
                if (!TextUtils.isEmpty(mFronType) && "scan" == mFronType) {
                    mOrderInfoBean = mFronScanOrderInfoBean
                    icon_iv.setBackgroundResource(R.drawable.ic_mer)
                    name_tv.text = mFronScanOrderInfoBean.merName
                    viewModel.queryMarketingInfo(
                        mQueryOrderBean.payToken,
                        mFronScanOrderInfoBean.trxOrderNo,
                        "30"
                    )
                } else {
                    // app跳转支付、 二维码订单
                    viewModel.requestOrderInfo(
                        mQueryOrderBean.orderSource,
                        mQueryOrderBean.payToken
                    )
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initVMListener() {
        // 订单信息回调
        viewModel.orderInfoBeanLD.observe(this) {
            mOrderInfoBean = it
            icon_iv.setBackgroundResource(R.drawable.ic_mer)
            if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
                name_tv.text = it.merTradeName
            } else {
                name_tv.text = it.merName
            }
        }
        // 营销信息回调
        viewModel.marketingBeanLD.observe(this) {
            mMarketingBean = it

            initOrderAmount()
            if (isMarketingOrder()) {
                newMoney_tv.visibility = View.VISIBLE
                oldMoney_tv.visibility = View.VISIBLE
                next_tv.visibility = View.VISIBLE
                initAdapter("1")
            } else {
                newMoney_tv.visibility = View.GONE
                oldMoney_tv.visibility = View.GONE
                next_tv.visibility = View.GONE
                showCashier()
            }
        }
        // 使用营销回调
        viewModel.useMarketingBeanLD.observe(this) {
//            initAdapter()
            refreshAdapter(it)
        }
    }

    private fun startService() {
        val intent = Intent()
        //这里说明一下,前面的是项目的包名,后面的是相应AIDL绑定服务的名(全路径哦)
        val packName: String = mQueryOrderBean.packName
        if (!TextUtils.isEmpty(packName)) {
            intent.component = ComponentName(packName, CashierConstants.SERVICEPATH)
            mIsSuccess = bindService(intent, serviceConnection, BIND_AUTO_CREATE)
        }
    }

    private fun isMarketingOrder(): Boolean {
//        mMarketingBean.orderAmt
        return (mMarketingBean.coupon.useList.isNotEmpty() || mMarketingBean.rights.useList.isNotEmpty() || mMarketingBean.points.pointsDetail.isNotEmpty())
    }

    private fun initOrderAmount() {
        val it = mMarketingBean
        val currency = it.currency + " "
        newMoney_tv.text = currency + it.paymentAmt

        val orderAmt = it.orderAmt
        val pAmount = mMarketingBean.points.amount//可用积分
        val cAmount = mMarketingBean.coupon.amount//可用优惠券
        if (it.paymentAmt == orderAmt) {
            oldMoney_tv.visibility = View.GONE
        } else {
            oldMoney_tv.visibility = View.VISIBLE
            if (MoneyUtil.isValidMoney(orderAmt) && it.paymentAmt != orderAmt) {
                oldMoney_tv.text = currency + orderAmt
                oldMoney_tv.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
            }
        }

    }

    private fun initAdapter(type: String) {
        val arrayListOf = arrayListOf<InfoItemBean>().apply {
            val currency = mMarketingBean.currency + " " + "- "
            val rights = mMarketingBean.rights
            if (rights.useList.isNotEmpty()) {
                add(
                    InfoItemBean(
                        getString(R.string.membership_Bonus),
                        text = currency + rights.amount.toString(),
                        iconId = R.drawable.ic_vip,
                    )
                )
            }

            val points = mMarketingBean.points
            // 没有可用积分不显示
            if (points.pointsDetail.isNotEmpty()) {
                val amount = points.amount
                val orderAmt = mMarketingBean.orderAmt
                val paymentAmt = mMarketingBean.paymentAmt

                var text = if (MoneyUtil.isValidMoney(amount)) {
                    currency + StringUtils.formatAmount(amount)
                } else {
                    if ("1" == type) {
                        getString(R.string.available)
                    } else {
                        if (MoneyUtil.isValidMoney(orderAmt) && MoneyUtil.isValidMoney(paymentAmt) && orderAmt == paymentAmt) {
                            currency + StringUtils.formatAmount(paymentAmt)
                        } else {
                            getString(R.string.available)
                        }
                    }
                }

                /*val text = if (MoneyUtil.isValidMoney(amount)) {
                    currency + StringUtils.formatAmount(amount)
                } else {
                    // 有可用的积分，但是没有使用积分
                    getString(R.string.available)
                }*/
                add(
                    InfoItemBean(
                        getString(R.string.membership_Points),
                        type = 0,
                        text = text,
                        iconId = R.drawable.ic_vip_points,
                        // 跳转积分
                        callBack = ::jumpMembersPointsActivity
                    )
                )
            }

            val coupon = mMarketingBean.coupon
            if (coupon.useList.isNotEmpty()) {
                val amount = coupon.amount
                val text = if (MoneyUtil.isValidMoney(amount)) {
                    currency + StringUtils.formatAmount(amount)
                } else {
                    // 有可用的优惠卷，但是没有使用
                    getString(R.string.available)
                }
                add(
                    InfoItemBean(
                        getString(R.string.coupon),
                        type = 0,
                        text = text,
                        iconId = R.drawable.ic_coupon,
                        // 选择优惠卷
                        callBack = ::jumpSelectCouponsActivity
                    )
                )
            }
        }
        mScanCodePayAdapter.setNewData(arrayListOf)
    }


    private fun refreshAdapter(bean: MarketingBean) {
        // 更新优惠使用list
        newMoney_tv.text = bean.paymentAmt
        mMarketingBean.apply {
            paymentAmt = bean.paymentAmt

            // 有效金额更新优惠卷使用list,无效仅更新金额
            var newAmount = bean.coupon.amount
            coupon.amount = newAmount
            if (bean.coupon.useList.size > 0) {
                if (StringUtils.isValidString(newAmount))
                    coupon.useList = bean.coupon.useList
            }

            newAmount = bean.points.amount
            points.amount = newAmount
            if (bean.points.pointsDetail.size > 0) {
                if (StringUtils.isValidString(newAmount))
                    points.pointsDetail = bean.points.pointsDetail
            }

            newAmount = bean.rights.amount
            rights.amount = newAmount
            if (StringUtils.isValidString(newAmount))
                rights.useList = bean.rights.useList
        }

        // 刷新页面数据
        initOrderAmount()
        initAdapter("0")
    }

    private fun showCashier() {
        if (!this::mQueryOrderBean.isInitialized || !this::mOrderInfoBean.isInitialized) return
        var payType = CommonConstants.CashierPaymentType.Cashier_Payment
        var transType = CommonConstants.TransType.Payment
        var orderInfo = getString(R.string.oneMoney_to_Bank)
        var merName = mOrderInfoBean.merName
        var amt = mOrderInfoBean.amt
        var setmeal = ""
        if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
            payType = CommonConstants.CashierPaymentType.AIRTIME_BUNDLE
            transType = CommonConstants.TransType.Mobile_Fee
            orderInfo = getString(R.string.buy_Bundle)
            setmeal = mQueryOrderBean.orderNo
            if (getString(R.string.buy_airtime) == mQueryOrderBean.merName) {
                payType = CommonConstants.CashierPaymentType.AIRTIME_BUNDLE
                orderInfo = getString(R.string.buy_airtime)
                setmeal =
                    getString(R.string.airtime) + " - " + mQueryOrderBean.currency + " " + StringUtils.formatAmount(
                        mQueryOrderBean.amt
                    )
            }
            amt = mOrderInfoBean.orderAmt
            merName = mOrderInfoBean.merTradeName

        } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
            payType = CommonConstants.CashierPaymentType.ZESA_FEE
            transType = CommonConstants.TransType.Bill_PAY
            orderInfo = getString(R.string.bill_Payment) + " - " + getString(R.string.zESA)
            amt = mOrderInfoBean.orderAmt
        } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
            payType = CommonConstants.CashierPaymentType.MMERCODE_PAY
            transType = CommonConstants.TransType.Payment
            orderInfo = mOrderInfoBean.commodityRemark
            amt = mOrderInfoBean.amt
        }

        val cashierOrderInfoBean = CashierOrderInfoBean.Builder()
            .setPayType(payType)
            .setTransType(transType)
            .setOrderSource(CommonConstants.OrderSource.EXTERNALORDER)
            .setOrderInfo(orderInfo)
            .setSetmeal(setmeal)
            .setQrCode(if (StringUtils.isValidString(mQueryOrderBean.payToken)) mQueryOrderBean.payToken else mQueryOrderBean.qrCode)
            .setPaymentToken(mQueryOrderBean.payToken)

            .setMerName(merName).setCurrency(mOrderInfoBean.currency)
            .setOrderAmt(amt).setOrderNo(mOrderInfoBean.trxOrderNo)

            .setPlanCode(mQueryOrderBean.qrCode)
            // 处理无营销
            .setAfterDisCountAmt(if (this::mMarketingBean.isInitialized) mMarketingBean.paymentAmt else "")
            .setIsFixMethod(false)

            .builder()
        CashierManager1(this, Gson().toJson(cashierOrderInfoBean), object : CashierCallBack1 {
            override fun paymentSuccess(dataJson: String) {
                //支付成功页面
                Log.e("TAG", "paymentSuccess: $dataJson")
                val fromJson = Gson().fromJson(dataJson, PaymentSuccessBean::class.java)
                val resultAmount = newMoney_tv.text.toString()
                Log.e("TAG", "resultAmount: $resultAmount")

                if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
                    var s2 = fromJson.currency + " " +  fromJson.orderAmount
                    if (isMarketingOrder()){
                        s2 = resultAmount;
                    }
                    val comResultBean =
                        ComResultBean(
                            getString(R.string.buy_Airtime_Bundle),
                            s1 = "",
                            s2 = s2,
                            flag = BaseConstants.JumpFlag.BUY_AIRTIME_BUNDLE,
                            status = fromJson.transStatus
                        )
                    ComResultActivity.jump(mContext, comResultBean)
                } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
                    var s2 = mOrderInfoBean.orderAmt
                    if (isMarketingOrder()){
                        s2 = resultAmount;
                    }
                    val orderSuccessBean =
                        OrderSuccessBean(
                            orderType = CommonConstants.CashierPaymentType.ZESA_FEE,
                            currency = mQueryOrderBean.currency,
                            orderAmount = amt,
                            actAmount = s2,//fromJson.actAmount,
                            merNo = mQueryOrderBean.merNo,
                            isTradeSuccess = fromJson.trxStatus == "30",
                            buyToken = fromJson.token,
                            trxStatus = fromJson.trxStatus
                        )
                    ARouter.getInstance()
                        .build(ARouterPath.TransferPath.ComTransferSuccessActivity)
                        .withString(
                            CommonConstants.Transmit.ORDER_SUCCESS_BRAN,
                            Gson().toJson(orderSuccessBean)
                        )
                        .navigation()
                } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
                    var s2 = fromJson.currency + " " + mOrderInfoBean.amt
                    if (isMarketingOrder()){
                        s2 = resultAmount;
                    }
                    val comResultBean =
                        ComResultBean(
                            getString(R.string.make_Payment),
                            s1 = "",
                            s2 = s2,
                            flag = BaseConstants.JumpFlag.Cashier_Pay,
                            status = fromJson.orderStatus
                        )
                    ComResultActivity.jump(mContext, comResultBean)
                } else {
                    var s2 = fromJson.currency + " " + mOrderInfoBean.amt
                    if (isMarketingOrder()){
                        s2 = resultAmount;
                    }
                    val comResultBean = ComResultBean(
                        getString(R.string.payment),
                        s2 = s2,
                        flag = BaseConstants.JumpFlag.Cashier_Pay,
                        status = fromJson.orderStatus
                    )

                    if (mQueryOrderBean.isExtAppJump()) {
                        ComResultActivity.jump(
                            mContext,
                            comResultBean,
                            CommonConstants.ResultCode.Request
                        )
                    } else {
                        ComResultActivity.jump(
                            mContext,
                            comResultBean
                        )
                    }
                }
            }

            override fun paymentFailed(message: String) {

            }

            override fun leftClick() {
                if (!isMarketingOrder()) {
                    // 不是营销订单直接关闭页面
                    finish()
                }
            }

            override fun rightClick() {
                if (!isMarketingOrder()) {
                    // 不是营销订单直接关闭页面
                    finish()
                }
            }

        }).showCashierDialog()
    }

    override fun finish() {
        setResult(CommonConstants.ResultCode.Success)
        super.finish()
    }

    private fun jumpSelectCouponsActivity() {
        var amt = mOrderInfoBean.amt
        if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.orderAmt
        } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.orderAmt
        } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.amt
        }
        val userID = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.USERID,
            ""
        ) as String
        ShowMarketingManager.getInstance().jumpSelectCouponsActivity(userID,
            mOrderInfoBean.merNo,
            amt,
            mMarketingBean.points.amount.toString(),
            mOrderInfoBean.currency,
            ArrayList<DirectReductionBean>(),
            getUseCouponsList(),
            getUsePointsList(),
            ArrayList<GoodsBean>(),
            object : CouponCallback {
                override fun onCouponCallback(couponBean: CouponBean) {
                    var trxTransType = "30"
                    if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
                        trxTransType = "32"
                    } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
                        trxTransType = "31"
                    } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
                        trxTransType = "30"
                    }
                    viewModel.requestUseMarketing(
                        RequestUseMarketingBean(
                            mQueryOrderBean.payToken,
                            mOrderInfoBean.trxOrderNo,
                            trxTransType,
                            getUseCouponsList(couponBean.coupon),
                            getUsePointsList()
                        )
                    )
                }
            })
    }

    private fun jumpMembersPointsActivity() {
        var amt = mOrderInfoBean.amt
        if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.orderAmt
        } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.orderAmt
        } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
            amt = mOrderInfoBean.amt
        }
        val userID = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.USERID,
            ""
        ) as String
        val useCouponsList = getUseCouponsList()
        ShowMarketingManager.getInstance().jumpMembersPointsActivity(userID,
            mOrderInfoBean.merNo,
            amt,
            mOrderInfoBean.currency,
            mMarketingBean.points.amount.toString(),
            ArrayList<DirectReductionBean>(),
            useCouponsList,
            ArrayList<GoodsBean>(),
            object : CouponCallback {
                override fun onCouponCallback(couponBean: CouponBean) {
                    var trxTransType = "30"
                    if (CommonConstants.PaymentProduct.ORDER_BUND == mQueryOrderBean.paymentProduct) {
                        trxTransType = "32"
                    } else if (CommonConstants.PaymentProduct.ORDER_ZESA == mQueryOrderBean.paymentProduct) {
                        trxTransType = "31"
                    } else if (CommonConstants.PaymentProduct.MERCHANT_CODE_PAY == mQueryOrderBean.paymentProduct) {
                        trxTransType = "30"
                    }
                    viewModel.requestUseMarketing(
                        RequestUseMarketingBean(
                            mQueryOrderBean.payToken,
                            mOrderInfoBean.trxOrderNo,
                            trxTransType,
                            useCouponsList,
                            getUsePointsList(couponBean.points)
                        )
                    )
                }
            })
    }


    //    private fun getUsePointsList(list: List<CouponBean.PointsBean> = mMarketingBean.points.pointsDetail): ArrayList<UsePointsBean> {
    /**
     * 区别于优惠卷，没有默认使用，consumptionPoints值有问题，需手动处理一下
     * points.amount=0,返回[]
     */
    private fun getUsePointsList(points: CouponBean.PointsDetail = mMarketingBean.points): ArrayList<UsePointsBean> {
        if (MoneyUtil.isValidMoney(points.amount)) {
            val arrayListOf = arrayListOf<UsePointsBean>()
            points.pointsDetail.forEach {
                arrayListOf.add(
                    UsePointsBean(
                        it.pointId,
                        it.consumptionPoints.toString(),
                        it.deductionAmount.toString(),
                        it.totalPoints.toString()
                    )
                )
            }
            return arrayListOf
        }
        return arrayListOf()
    }

    private fun handleUsePointsList(list: List<CouponBean.PointsBean>): ArrayList<UsePointsBean> {
        val arrayListOf = arrayListOf<UsePointsBean>()
        list.forEach {
            arrayListOf.add(
                UsePointsBean(
                    it.pointId,
                    it.consumptionPoints.toString(),
                    it.deductionAmount.toString(),
                    it.totalPoints.toString()
                )
            )
        }
        return arrayListOf
    }

    /**
     * 获取优惠卷使用列表，没有传入list，取初始页面默认值
     * 使用成功后 useMarketingBeanLD 回调会更新 mMarketingBean
     */
    private fun getUseCouponsList(coupon: CouponBean.Coupon = mMarketingBean.coupon): ArrayList<UseCouponsBean> {
        if (MoneyUtil.isValidMoney(coupon.amount)) {
            coupon.useList.forEach {
                // 优惠卷单选
                return arrayListOf(UseCouponsBean().apply {
                    no = it.no
                })
            }
        }
        return arrayListOf()
    }


    /**
     * app跳转订单
     */
    private fun onAppJumpOrderResult(result: Boolean) {
        Log.e("==shop==", "packName==" + mQueryOrderBean.packName)
        try {
            if (mIsSuccess) {
                if (result) {
                    mICashierAidlInterface.payResult(
                        CashierConstants.OrderPayStatus.SUCCESS,
                        CashierConstants.OrderPayError.SUCCESS
                    )
                } else {
                    mICashierAidlInterface.payResult(
                        CashierConstants.OrderPayStatus.CANCEL,
                        CashierConstants.OrderPayError.CANCEL
                    )
                }
                val intent =
                    AppUtils.getAppOpenIntentByPackageName(mContext, mQueryOrderBean.packName)
                startActivity(intent)
                finish()
                //清除之前保存的相应数据
                SharedPreferencesUtils.setParam(
                    mContext,
                    CashierConstants.SaveParameter.XWALLETPAYBEANJSON,
                    ""
                )
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        } finally {
            if (mIsSuccess) {
                ActivityController.getInstance().finishAllActivity()
            } else {
                finish()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CommonConstants.ResultCode.Request && resultCode == CommonConstants.ResultCode.Success) {
            onAppJumpOrderResult(true)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        onAppJumpOrderResult(false)
        if (mIsSuccess) {
            unbindService(serviceConnection)
        }
    }

    override fun onBackPressed() {
        finish()
    }

    companion object {
        @JvmStatic
        fun jump(context: Context, payCheckBeanStr: String?, qrCode: String?, jumpFlag: String?) {
            val intent = Intent(context, CashierPayActivity::class.java)
//            intent.putExtra(PaymentConstants.Transmit.PAYCHECKDETAILS, payCheckBeanStr)
//            intent.putExtra(PaymentConstants.Transmit.QRCODE, qrCode)
//            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag)
            context.startActivity(intent)
        }
    }

}