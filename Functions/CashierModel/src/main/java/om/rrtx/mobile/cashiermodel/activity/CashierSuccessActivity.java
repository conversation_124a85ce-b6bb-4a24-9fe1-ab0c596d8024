package om.rrtx.mobile.cashiermodel.activity;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;

import om.rrtx.mobile.functioncommon.bean.XWalletPayBean;

import butterknife.BindView;
import om.rrtx.mobile.cashiermodel.CashierConstants;
import om.rrtx.mobile.cashiermodel.ICashierAidlInterface;
import om.rrtx.mobile.cashiermodel.R;
import om.rrtx.mobile.cashiermodel.R2;
import om.rrtx.mobile.rrtxcommon1.utils.AppUtils;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 外部支付成功
 */
public class CashierSuccessActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    @BindView(R2.id.moneyTv)
    TextView mMoneyTv;
    @BindView(R2.id.moneyTypeTv)
    TextView mMoneyTypeTv;
    @BindView(R2.id.orderAmt)
    TextView mOrderAmt;
    @BindView(R2.id.orderAmtTv)
    TextView mOrderAmtTv;
    @BindView(R2.id.fee)
    TextView mFee;
    @BindView(R2.id.feeTv)
    TextView mFeeTv;
    @BindView(R2.id.showContentCl)
    ConstraintLayout mShowContentCl;
    private ICashierAidlInterface mICashierAidlInterface;
    private XWalletPayBean mXWalletPayBean;
    private boolean mIsSuccess;
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mICashierAidlInterface = ICashierAidlInterface.Stub.asInterface(service);
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

        }
    };

    public static void jumpCashierSuccess(Context context, String xWalletBeanJson) {
        Intent intent = new Intent(context, CashierSuccessActivity.class);
        intent.putExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON, xWalletBeanJson);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String xWalletPayBeanJson = getIntent().getStringExtra(CashierConstants.Transmit.XWALLETPAYBEANJSON);
        if (!TextUtils.isEmpty(xWalletPayBeanJson)) {
            mXWalletPayBean = new Gson().fromJson(xWalletPayBeanJson, XWalletPayBean.class);
        }
    }

    @Override
    protected int createContentView() {
        return R.layout.cashier_activity_cashier_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();

        mBackIv.setVisibility(View.GONE);
        mTitleTv.setText(R.string.success);

        startBindService();
    }

    private void startBindService() {
        if (mXWalletPayBean != null) {
            Intent intent = new Intent();
            //这里说明一下,前面的是项目的包名,后面的是相应AIDL绑定服务的名(全路径哦)
            String packName = mXWalletPayBean.getPackName();
            if (!TextUtils.isEmpty(packName)) {
                intent.setComponent(new ComponentName(packName, CashierConstants.SERVICEPATH));
                mIsSuccess = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
            }
        }
    }

    @Override
    public void initDate() {
        super.initDate();
        if (mXWalletPayBean != null) {
            mMoneyTv.setText(mXWalletPayBean.getOrderAmount());
//            String currencyStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, "");
//            currencyStr = CurrencyUtils.setCurrency(mContext, currencyStr);
            mMoneyTypeTv.setText(CurrencyUtils.setCurrency(mContext, mXWalletPayBean.getCurrency()));
        }

//        String currencyStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, "");

        if (mXWalletPayBean != null) {
            //设置费率
            String feeAmount = mXWalletPayBean.getPayFeeAmount();
            if (!TextUtils.isEmpty(feeAmount)) {
                BigDecimal feeAmtBd = new BigDecimal(feeAmount);
                int i = feeAmtBd.compareTo(BigDecimal.ZERO);
                if (i > 0) {
                    String feeStr = feeAmount + " " + CurrencyUtils.setCurrency(mContext, mXWalletPayBean.getCurrency());
                    mFeeTv.setText(feeStr);
                    //设置订单金额
                    String orderAmtStr = mXWalletPayBean.getOrderAmount() + " " + CurrencyUtils.setCurrency(mContext, mXWalletPayBean.getCurrency());
                    mOrderAmtTv.setText(orderAmtStr);
                    mShowContentCl.setVisibility(View.VISIBLE);
                } else {
                    mShowContentCl.setVisibility(View.GONE);
                }
            } else {
                mShowContentCl.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void initListener() {
        mDoneTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                try {
                    if (mIsSuccess) {
                        mICashierAidlInterface.payResult(CashierConstants.OrderPayStatus.SUCCESS, CashierConstants.OrderPayError.SUCCESS);
                        if (mXWalletPayBean != null) {
                            Intent intent = AppUtils.getAppOpenIntentByPackageName(mContext, mXWalletPayBean.getPackName());
                            startActivity(intent);
                        }
                        finish();
                        //清除之前保存的相应数据
                        SharedPreferencesUtils.setParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, "");
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onBackPressed() {
        try {
            if (mIsSuccess) {
                mICashierAidlInterface.payResult(CashierConstants.OrderPayStatus.SUCCESS, CashierConstants.OrderPayError.SUCCESS);
                if (mXWalletPayBean != null) {
                    Intent intent = AppUtils.getAppOpenIntentByPackageName(mContext, mXWalletPayBean.getPackName());
                    startActivity(intent);
                }
                finish();
                //清除之前保存的相应数据
                SharedPreferencesUtils.setParam(mContext, CashierConstants.SaveParameter.XWALLETPAYBEANJSON, "");
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mIsSuccess) {
            unbindService(serviceConnection);
        }
    }
}
