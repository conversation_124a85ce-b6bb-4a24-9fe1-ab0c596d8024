package om.rrtx.mobile.cashiermodel;

/**
 * <AUTHOR>
 */
public class CashierConstants {
    /**
     * 绑定服务的全路径
     */
    public static final String SERVICEPATH = "com.rrtx.xwalletpaylib.CashierPayService";
    /**
     * 登录页面
     */
    public static final String LOGINACTIVITY = "om.rrtx.mobile.loginmodule.LoginActivity";
    /**
     * 验证登录
     */
    public static final String LOGINPASSWORDLOCKACTIVITY = "om.rrtx.mobile.securitymodule.LoginPasswordLockActivity";
    /**
     * 收银台支付
     */
    public static final String CASHIERACTIVITY = "om.rrtx.mobile.cashiermodel.CashierActivity";
    public static final String Auto_Debit_Activity = "om.rrtx.mobile.securitymodule.activity.AutoDebitActivity";


    public interface URL {
        /**
         * 获取公钥接口
         */
        String GETPUB = "/encrypt/public/key";

    }

    /**
     * 订单支付状态
     */
    public interface OrderPayStatus {
        /**
         * 取消
         */
        int CANCEL = -1;
        /**
         * 成功
         */
        int SUCCESS = 0;
    }

    public interface OrderPayError {
        String CANCEL = "cancel";
        String SUCCESS = "success";
    }

    public interface Transmit {
        /**
         * 传递的json参数
         */
        String XWALLETPAYBEANJSON = "xWalletPayBeanJson";
    }


    public interface SaveParameter {
        /**
         * 传递的json参数
         */
        String XWALLETPAYBEANJSON = "xWalletPayBeanJson";
    }
}
