<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--密码控件-->
    <declare-styleable name="Cashier_PasswordEditText">
        <!-- 密码的个数 -->
        <attr name="cashier_passwordNumber" format="integer" />
        <!-- 密码圆点的半径 -->
        <attr name="cashier_passwordRadius" format="dimension" />
        <!-- 密码圆点的颜色 -->
        <attr name="cashier_passwordColor" format="color" />
        <!-- 分割线的颜色 -->
        <attr name="cashier_divisionLineColor" format="color" />
        <!-- 分割线的大小 -->
        <attr name="cashier_divisionLineSize" format="color" />
        <!-- 背景边框的颜色 -->
        <attr name="cashier_bgColor" format="color" />
        <!-- 背景边框的大小 -->
        <attr name="cashier_bgSize" format="dimension" />
        <!-- 背景边框的圆角大小 -->
        <attr name="cashier_bgCorner" format="dimension" />
    </declare-styleable>
</resources>