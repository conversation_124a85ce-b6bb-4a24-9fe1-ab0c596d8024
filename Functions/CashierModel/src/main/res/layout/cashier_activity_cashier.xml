<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_ye_F3881E"
    tools:context=".activity.CashierActivity">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/heardIv"
        app:layout_constraintTop_toTopOf="parent" />


    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/heardIv"
        android:layout_width="160pt"
        android:layout_height="160pt"
        android:layout_marginTop="110pt"
        android:contentDescription="@string/base_app_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusView" />

    <TextView
        android:id="@+id/userNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10pt"
        android:textColor="@color/color_FFFFFF"
        android:textSize="30pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/heardIv"
        tools:text="userName" />
</androidx.constraintlayout.widget.ConstraintLayout>