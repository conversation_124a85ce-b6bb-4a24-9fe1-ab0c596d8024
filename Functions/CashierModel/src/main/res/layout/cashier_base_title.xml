<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_FFFFFF"
    android:orientation="vertical">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_FFFFFF"
        app:layout_constraintBottom_toTopOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="match_parent"
        android:layout_height="90pt"
        android:paddingLeft="90pt"
        android:paddingRight="90pt"
        android:gravity="center"
        android:textColor="@color/color_212121"
        android:textSize="36pt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusView"
        tools:text="Certification" />

    <View
        android:id="@+id/leftBg"
        android:layout_width="90pt"
        android:layout_height="90pt"
        app:layout_constraintBottom_toBottomOf="@id/titleTv"
        app:layout_constraintLeft_toLeftOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="@id/titleTv" />

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:contentDescription="@string/common_app_name"
        app:layout_constraintBottom_toBottomOf="@id/leftBg"
        app:layout_constraintLeft_toLeftOf="@id/leftBg"
        app:layout_constraintRight_toRightOf="@id/leftBg"
        app:layout_constraintTop_toTopOf="@id/leftBg" />
</androidx.constraintlayout.widget.ConstraintLayout>