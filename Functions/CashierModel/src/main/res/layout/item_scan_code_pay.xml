<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="100pt"
        android:paddingLeft="24pt"
        android:paddingRight="24pt">

        <ImageView
            android:id="@+id/icon_iv"
            android:layout_width="40pt"
            android:layout_height="40pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/icon_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/text_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_ye_F3881E"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/more_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/more_iv"
            android:layout_width="40pt"
            android:layout_height="40pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/icon_more"
            app:tint="@color/common_ye_F3881E"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>