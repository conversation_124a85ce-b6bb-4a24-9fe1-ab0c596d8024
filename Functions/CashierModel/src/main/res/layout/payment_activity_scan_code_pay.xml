<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg_f7f8fa">

        <include
            android:id="@+id/include_title"
            layout="@layout/cashier_base_title" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/infoBg_cl"
            cornerBackgroundRadius="@{10}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="24pt"
            android:layout_marginRight="30pt"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <ImageView
                android:id="@+id/icon_iv"
                android:layout_width="100pt"
                android:layout_height="100pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="53pt"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@drawable/ic_mer"
                />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0pt"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:layout_marginTop="16pt"
                android:textColor="@color/common_text_1d2129"
                android:textSize="36pt"
                android:lines="1"
                android:ellipsize="end"
                android:layout_marginLeft="24pt"
                android:layout_marginRight="24pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/icon_iv" />

            <TextView
                android:id="@+id/newMoney_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32pt"
                android:textColor="@color/common_text_1d2129"
                android:textSize="46pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv" />

            <TextView
                android:id="@+id/oldMoney_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16pt"
                android:textColor="@color/common_text_86909C"
                android:textSize="26pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/newMoney_tv" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="78pt"
                app:layout_constraintTop_toBottomOf="@id/oldMoney_tv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/next_tv"
            android:layout_width="match_parent"
            android:layout_height="88pt"
            app:layout_constraintTop_toBottomOf="@id/infoBg_cl"
            android:layout_marginTop="64pt"
            android:background="@drawable/common_btn_bg"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:text="@string/confrim"
            android:textSize="36pt"
            android:gravity="center"
            android:textColor="@color/common_text_FFFFFF"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>