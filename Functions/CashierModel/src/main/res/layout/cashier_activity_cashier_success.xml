<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.CashierSuccessActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/cashier_base_title" />

    <ImageView
        android:id="@+id/iconIv"
        android:layout_width="88pt"
        android:layout_height="88pt"
        android:layout_marginTop="120pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/cashier_ic_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <TextView
        android:id="@+id/moneyTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80pt"
        tools:text="@string/success"
        android:textColor="@color/color_17904B"
        android:textSize="48pt"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iconIv" />

    <TextView
        android:id="@+id/moneyTypeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10pt"
        android:layout_marginTop="80pt"
        tools:text="@string/success"
        android:textColor="@color/color_17904B"
        android:textSize="24pt"
        app:layout_constraintBaseline_toBaselineOf="@id/moneyTv"
        app:layout_constraintBottom_toBottomOf="@id/moneyTv"
        app:layout_constraintLeft_toRightOf="@id/moneyTv" />

    <TextView
        android:id="@+id/hintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12pt"
        android:text="@string/checkout_label_pay_success"
        android:textColor="@color/color_131313"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/moneyTv" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/showContentCl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="60pt"
        android:layout_marginRight="30pt"
        android:background="@drawable/drawable_white_round"
        android:paddingBottom="30pt"
        app:layout_constraintTop_toBottomOf="@id/hintTv">

        <TextView
            android:id="@+id/orderAmt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="40pt"
            android:text="@string/checkout_label_order_amount"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/orderAmtTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:layout_marginRight="30pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/orderAmt"
            app:layout_goneMarginBottom="30pt" />

        <TextView
            android:id="@+id/fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="10pt"
            android:text="@string/checkout_label_fee"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/orderAmtTv" />

        <TextView
            android:id="@+id/feeTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:layout_marginRight="30pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fee" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/doneTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="60pt"
        android:background="@drawable/common_usable_btn"
        android:gravity="center"
        android:text="@string/done"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/showContentCl" />
</androidx.constraintlayout.widget.ConstraintLayout>