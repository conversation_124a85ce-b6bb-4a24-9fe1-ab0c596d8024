<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="om.rrtx.mobile.cashiermodel">

    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application>
        <service
            android:name=".service.AidlAppInfoService"
            android:enabled="true"
            android:exported="true" />

        <receiver
            android:name=".PayReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.mobile.omMobile.action" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".activity.CashierActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden"
            android:exported="true">
            <intent-filter>
                <action android:name="om.rrtx.mobile.cashiermodel.CashierActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.CashierSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.ScanCashierActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.ScanCashierSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.CashierPayActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
    </application>

</manifest>