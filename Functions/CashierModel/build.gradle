apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'

//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name

        // 控制日志Log 输出打印
        buildConfigField("boolean", "enableLog", "true")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled true
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    resourcePrefix "cashier_"
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"

    //基础类库
    api project(path: ':BaseModule:RrtxCommon')

    //收银台的库
    api project(path: ':BaseModule:FunctionCommon')

    //butterKnife
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    annotationProcessor "com.jakewharton:butterknife-compiler:${libs.butterknife_compiler}"

    //arouter
    annotationProcessor "com.alibaba:arouter-compiler:${libs.arouter_compiler}"

    // kotlin 与 butterknife 冲突解决
    kapt "com.jakewharton:butterknife-compiler:${libs.butterknife_compiler}"
    //arouter
    annotationProcessor "com.alibaba:arouter-compiler:${libs.arouter_compiler}"
    kapt "com.alibaba:arouter-compiler:${libs.arouter_compiler}"
}
