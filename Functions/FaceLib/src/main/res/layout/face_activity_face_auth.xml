<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="daceAuthOpt"
            type="com.rrtx.facelib.acticity.FaceAuthActivity.FaceAuthOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".acticity.FaceAuthActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title"
            app:baseTitle="@{daceAuthOpt}" />

        <TextView
            android:id="@+id/hint1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="60pt"
            android:text="@string/protrait_collect"
            android:textColor="@color/color_000000"
            android:textSize="36pt"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <TextView
            android:id="@+id/hint2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40pt"
            android:text="@string/face_hint"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/authBig"
            android:layout_width="319pt"
            android:layout_height="398pt"
            android:layout_marginTop="86pt"
            android:src="@drawable/face_image_faceauthentication"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint2" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/sunnyIv"
            android:layout_width="95pt"
            android:layout_height="91pt"
            android:layout_marginTop="80pt"
            android:src="@drawable/face_image_sunny"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/positionIv"
            app:layout_constraintTop_toBottomOf="@id/authBig" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/positionIv"
            android:layout_width="95pt"
            android:layout_height="91pt"
            android:layout_marginTop="80pt"
            android:src="@drawable/face_image_position"
            app:layout_constraintLeft_toRightOf="@id/sunnyIv"
            app:layout_constraintRight_toLeftOf="@id/slowIv"
            app:layout_constraintTop_toBottomOf="@id/authBig" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/slowIv"
            android:layout_width="95pt"
            android:layout_height="91pt"
            android:layout_marginTop="80pt"
            android:src="@drawable/face_image_slow"
            app:layout_constraintLeft_toRightOf="@id/positionIv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/authBig" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/nextTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="86pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:onClick="@{()->daceAuthOpt.startFace()}"
            android:text="@string/start_auth"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/positionIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
