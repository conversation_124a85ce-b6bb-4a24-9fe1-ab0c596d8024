<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="baseTitle"
            type="com.rrtx.facelib.utils.BaseTitleClick" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/rootCl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/statusView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/color_FFFFFF"
            app:layout_constraintBottom_toTopOf="@id/titleTv"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/backBg"
            android:layout_width="90pt"
            android:layout_height="90pt"
            android:onClick="@{()->baseTitle.leftClick()}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusView" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/backIv"
            android:layout_width="45pt"
            android:layout_height="45pt"
            android:src="@drawable/face_back"
            app:layout_constraintBottom_toBottomOf="@id/backBg"
            app:layout_constraintLeft_toLeftOf="@id/backBg"
            app:layout_constraintRight_toRightOf="@id/backBg"
            app:layout_constraintTop_toTopOf="@id/backBg" />

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="0dp"
            android:layout_height="90pt"
            android:ellipsize="end"
            android:gravity="center"
            android:textColor="@color/color_131313"
            android:textSize="36pt"
            app:layout_constraintLeft_toRightOf="@id/backBg"
            app:layout_constraintRight_toLeftOf="@id/rightBg"
            app:layout_constraintTop_toBottomOf="@id/statusView"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="Identity Authentication" />


        <View
            android:id="@+id/rightBg"
            android:layout_width="90pt"
            android:layout_height="90pt"
            android:onClick="@{()->baseTitle.rightClick()}"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusView"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/rightIv"
            android:layout_width="45pt"
            android:layout_height="45pt"
            app:layout_constraintBottom_toBottomOf="@id/rightBg"
            app:layout_constraintLeft_toLeftOf="@id/rightBg"
            app:layout_constraintRight_toRightOf="@id/rightBg"
            app:layout_constraintTop_toTopOf="@id/rightBg" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
