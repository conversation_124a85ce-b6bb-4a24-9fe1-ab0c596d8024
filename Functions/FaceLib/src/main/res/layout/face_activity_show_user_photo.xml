<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="showUserPhotoOpt"
            type="com.rrtx.facelib.acticity.ShowUserPhotoActivity.ShowUserPhotoOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".acticity.ShowUserPhotoActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title"
            app:baseTitle="@{showUserPhotoOpt}" />

        <TextView
            android:id="@+id/hintTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="45pt"
            android:gravity="center"
            android:text="@string/face_hint2"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <com.github.chrisbanes.photoview.PhotoView
            android:id="@+id/showUserIv"
            android:layout_width="567pt"
            android:layout_height="361pt"
            android:layout_marginTop="103pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hintTv" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/nextTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="86pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:onClick="@{()->showUserPhotoOpt.nextClick()}"
            android:text="@string/next"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/showUserIv" />

        <TextView
            android:id="@+id/againBtn"
            android:layout_width="232pt"
            android:layout_height="37pt"
            android:layout_marginTop="24pt"
            android:gravity="center"
            android:onClick="@{()->showUserPhotoOpt.leftClick()}"
            android:text="@string/scan_again"
            android:textColor="@color/face_color_f3881e"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/nextTv" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>