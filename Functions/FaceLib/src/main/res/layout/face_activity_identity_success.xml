<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="identityOpt"
            type="com.rrtx.facelib.acticity.IdentitySuccessActivity.IdentityOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".acticity.IdentitySuccessActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title"
            app:baseTitle="@{identityOpt}" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardCv"
            android:layout_width="690pt"
            android:layout_height="wrap_content"
            app:cardCornerRadius="20pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20pt"
                    android:layout_marginTop="15pt"
                    android:text="@string/personal_info"
                    android:textColor="@color/color_000000"
                    android:textSize="24pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line"
                    android:layout_width="0dp"
                    android:layout_height="1pt"
                    android:layout_marginTop="15pt"
                    android:background="@color/color_E0E0E0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/info" />

                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="30pt"
                    android:text="@string/face_name"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line" />

                <TextView
                    android:id="@+id/nameTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/name" />

                <TextView
                    android:id="@+id/id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:text="@string/id_number"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/nameTv" />

                <TextView
                    android:id="@+id/idTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/id" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="300pt"
                    android:layout_height="300pt"
                    android:layout_marginTop="50pt"
                    android:layout_marginRight="124pt"
                    android:src="@drawable/face_image_successful"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="160pt"
                    android:layout_height="160pt"
                    android:layout_marginTop="25pt"
                    android:layout_marginRight="20pt"
                    android:src="@drawable/face_img_head_contact"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/certificationInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="35pt"
            android:text="@string/certification_info"
            android:textColor="@color/color_999999"
            android:textSize="24pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardCv" />

        <androidx.cardview.widget.CardView
            android:layout_width="690pt"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/certificationInfo">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="29pt"
                    android:text="@string/auth_time"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/timeTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="29pt"
                    android:layout_marginRight="20pt"
                    android:text="2020-12-18"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line2"
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginTop="28pt"
                    android:background="@color/color_AEB3C0"
                    app:layout_constraintTop_toBottomOf="@id/time" />

                <TextView
                    android:id="@+id/type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="29pt"
                    android:layout_marginBottom="28pt"
                    android:text="@string/auth_type"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line2" />

                <TextView
                    android:id="@+id/typeTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="29pt"
                    android:layout_marginRight="20pt"
                    android:text="EKYC"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line2" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>