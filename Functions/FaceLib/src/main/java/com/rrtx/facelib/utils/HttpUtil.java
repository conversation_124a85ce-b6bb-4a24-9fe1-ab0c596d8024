/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.rrtx.facelib.utils;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.rrtx.facelib.OnResultListener;
import com.rrtx.facelib.exception.FaceException;
import com.rrtx.facelib.manager.APIService;
import com.rrtx.facelib.model.AccessToken;
import com.rrtx.facelib.model.RequestParams;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 使用OKHttp请求token和调用公安接口
 */
public class HttpUtil {
    private static final String TAG = HttpUtil.class.getSimpleName();

    private OkHttpClient client;
    private Handler handler;
    private static volatile HttpUtil instance;

    private HttpUtil() {
    }

    public static HttpUtil getInstance() {
        if (instance == null) {
            synchronized (HttpUtil.class) {
                if (instance == null) {
                    instance = new HttpUtil();
                }
            }
        }
        return instance;
    }

    public void init() {
        client = new OkHttpClient();
        handler = new Handler(Looper.getMainLooper());
    }

    public <T> void post(String path, String key, RequestParams params,
                         final Parser<T> parser, final OnResultListener<T> listener) {
       Base64RequestBody body = new Base64RequestBody();

        body.setKey(key);
        body.setFileParams(params.getFileParams());
        body.setStringParams(params.getStringParams());
        body.setJsonParams(params.getJsonParams());
        final Request request = new Request.Builder()
                .url(path)
                .post(body)
                .build();
        // 经常client为空指针
        if (client == null) {
            HttpUtil.getInstance().release();
            HttpUtil.getInstance().init();
            if (client == null) {
                throwError(listener, -999, "okhttp inner error");
                return;
            }
        }

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
                throwError(listener, FaceException.ErrorCode.NETWORK_REQUEST_ERROR,
                        "network request error");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseString = response.body().string();
                LogUtil.e(TAG, "res = " + responseString);
                try {
                    final T result = parser.parse(responseString);
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            listener.onResult(result);
                        }
                    });
                } catch (final FaceException error) {
                    error.printStackTrace();
                    // throwError(listener, -1, error.toString());
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            listener.onError(error);
                        }
                    });
                }
            }
        });
    }

    /**
     * 获取accessToken
     * @param listener  回调监听
     * @param url        url
     * @param param      请求参数
     */
    public void getAccessToken(final OnResultListener<AccessToken> listener, String url, String param) {

        final Parser<AccessToken> accessTokenParser = new AccessTokenParser();
        RequestBody body = RequestBody.create(MediaType.parse("text/html"), param);
        final Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
                throwError(listener, FaceException.ErrorCode.NETWORK_REQUEST_ERROR,
                        "network request error");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response == null || response.body() == null || TextUtils.isEmpty(response.toString())) {
                    throwError(listener, FaceException.ErrorCode.ACCESS_TOKEN_PARSE_ERROR,
                            "token is parse error, please rerequest token");
                }
                try {
                    final AccessToken accessToken = accessTokenParser.parse(response.body().string());
                    if (accessToken != null) {
                        APIService.getInstance().setAccessToken(accessToken.getAccessToken());
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                listener.onResult(accessToken);
                            }
                        });
                    } else {
                        throwError(listener, FaceException.ErrorCode.ACCESS_TOKEN_PARSE_ERROR,
                                "token is parse error, please rerequest token");
                    }
                } catch (FaceException error) {
                    error.printStackTrace();
                    throwError(listener, FaceException.ErrorCode.ACCESS_TOKEN_PARSE_ERROR,
                            "token is parse error, please rerequest token");
                }
            }
        });

    }

    /**
     * 失败回调
     * @param errorCode  错误代码
     * @param msg         错误信息
     * @param listener   回调监听
     */
    private void throwError(final OnResultListener listener, int errorCode, String msg) {
        final FaceException error = new FaceException(errorCode, msg);
        handler.post(new Runnable() {
            @Override
            public void run() {
                listener.onError(error);
            }
        });
    }

    /**
     * 释放资源
     */
    public void release() {
        client = null;
        handler = null;
    }

    public String requestPost(String paramStr, String requestUrl) {
        HttpURLConnection conn = null; // 连接对象
        int responseCode = -1;
        String resultData = null;

        OutputStream outputStream = null;
        InputStream inputStream = null;
        ByteArrayOutputStream baos = null;
        try {
            URL url = new URL(requestUrl);
            conn = (HttpURLConnection) url.openConnection();
            System.setProperty("sun.net.client.defaultConnectTimeout", "8000");
            System.setProperty("sun.net.client.defaultReadTimeout", "8000");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setUseCaches(false);
            conn.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0;");
            conn.setRequestProperty("Accept-Language", "zh-CN");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("Charset", "UTF-8");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.connect();
            outputStream = conn.getOutputStream();
            outputStream.write(paramStr.getBytes());
            outputStream.flush();
            outputStream.close();
            responseCode = conn.getResponseCode();
            LogUtil.e(TAG, "request code " + responseCode);
            if (HttpURLConnection.HTTP_OK == responseCode) {
                inputStream = conn.getInputStream();
                byte[] buffer = new byte[1024];
                baos = new ByteArrayOutputStream();
                int len = -1;
                while ((len = inputStream.read(buffer)) != -1) {
                    baos.write(buffer, 0, len);
                }
                byte[] b = baos.toByteArray();
                resultData = new String(b, "utf-8");
                baos.flush();
                LogUtil.e(TAG, "request data " + resultData);
            }
        } catch (MalformedURLException e) {
            LogUtil.e(TAG, "MalformedURLException " + e.getMessage());
            e.printStackTrace();
        } catch (IOException e) {
            LogUtil.e(TAG, "IOException " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (baos != null) {
                    baos.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return resultData;
    }
}
