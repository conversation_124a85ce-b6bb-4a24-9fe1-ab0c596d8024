package com.rrtx.facelib.net;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 */
public class NetInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request().newBuilder()
                .addHeader("Accept-Encoding", "identity")
                .addHeader("Connection", "close").build();

        return chain.proceed(request);
    }
}
