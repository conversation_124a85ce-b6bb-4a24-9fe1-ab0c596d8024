package com.rrtx.facelib.utils;

import com.rrtx.facelib.bean.PhotoBean;

/**
 * <AUTHOR>
 */
public class UserInfoSingleton {
    private static final UserInfoSingleton ourInstance = new UserInfoSingleton();

    public static UserInfoSingleton getInstance() {
        return ourInstance;
    }

    private UserInfoSingleton() {
    }

    private PhotoBean mPhotoBean;

    public void setUserInfo(PhotoBean photoBean) {
        mPhotoBean = photoBean;
    }

    public PhotoBean getPhotoBean() {
        return mPhotoBean;
    }
}
