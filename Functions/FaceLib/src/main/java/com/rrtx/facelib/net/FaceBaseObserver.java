package com.rrtx.facelib.net;

import android.content.Context;
import android.text.TextUtils;

import com.rrtx.facelib.utils.LogUtil;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.net.ApiException;
import om.rrtx.mobile.rrtxcommon1.net.DisposableManager;
import om.rrtx.mobile.rrtxcommon1.net.LoadingDialog;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import retrofit2.Response;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2018/11/5 23:30
 * 描述 :
 */
public abstract class FaceBaseObserver<T> implements Observer<Response<T>> {


    private static final String TAG = FaceBaseObserver.class.getSimpleName();

    private Context mContext;
    private String mTag;
    private LoadingDialog mLoadingDialog;

    public FaceBaseObserver(Context sContext) {
        mContext = sContext;
    }

    @Override
    public void onSubscribe(Disposable d) {
        showDialog(mContext);
    }

    @Override
    public void onNext(Response<T> baseBean) {
        T body = baseBean.body();
        dismissDialog();
        if (body != null) {
            requestSuccess(body);
        } else {
            String netError = ResourceHelper.getString(mContext, R.string.common_tip_network_error);
            requestFail(netError);
        }
    }

    /**
     * 请求成功,返回相应的数据
     *
     * @param sResData 成功的实体类
     */
    public abstract void requestSuccess(T sResData);

    /**
     * 请求成功,但是返回失败的回调
     *
     * @param sResMsg 失败信息
     */
    public abstract void requestFail(String sResMsg);

    @Override
    public void onError(Throwable e) {
        dismissDialog();
        if (e instanceof ApiException) {
            requestFail(e.getMessage());
        } else {
            LogUtil.e(TAG, "onError: " + e.toString());
            String netError = ResourceHelper.getString(mContext, R.string.common_tip_network_error);
            requestFail(netError);
        }
    }

    @Override
    public void onComplete() {
        if (!TextUtils.isEmpty(mTag)) {
            DisposableManager.getInstance().removeDisposable(mTag);
        }
    }

    private void showDialog(Context context) {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog(context);
        }
        mLoadingDialog.show();
    }

    private void dismissDialog() {
        if (mLoadingDialog != null && mLoadingDialog.isShowing() && mContext != null) {
            mLoadingDialog.dismiss();
            mLoadingDialog.cancel();
        }
    }
}
