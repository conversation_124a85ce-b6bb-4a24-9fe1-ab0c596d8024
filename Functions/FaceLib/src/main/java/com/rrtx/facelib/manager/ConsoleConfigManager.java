package com.rrtx.facelib.manager;

import android.content.Context;
import android.util.Log;

import com.rrtx.facelib.model.ConsoleConfig;
import com.rrtx.facelib.utils.FileUtil;
import com.rrtx.facelib.utils.LogUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

public class ConsoleConfigManager {

    private static final String FILE_NAME = "console_config.json";

    private static ConsoleConfigManager instance;

    private ConsoleConfig config;

    public static ConsoleConfigManager getInstance(Context ctx) {
        if (instance == null) {
            synchronized (ConsoleConfigManager.class) {
                if (instance == null) {
                    instance = new ConsoleConfigManager(ctx.getApplicationContext());
                }
            }
        }
        return instance;
    }

    private ConsoleConfigManager(Context ctx) {
        try {
            config = new ConsoleConfig();
            String json = FileUtil.readAssetFileUtf8String(ctx.getAssets(), FILE_NAME);
            JSONObject jsonObject = new JSONObject(json);
            config.parseFromJSONObject(jsonObject);
        } catch (IOException e) {
            LogUtil.e(this.getClass().getName(), "初始配置读取失败");
            config = null;
        } catch (JSONException e) {
            LogUtil.e(this.getClass().getName(), "初始配置读取失败, JSON格式不正确");
            config = null;
        } catch (Exception e) {
            LogUtil.e(this.getClass().getName(), "初始配置读取失败, JSON格式不正确");
            config = null;
        }
    }

    public ConsoleConfig getConfig() {
        return config;
    }
}
