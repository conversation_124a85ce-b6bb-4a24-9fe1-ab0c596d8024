package com.rrtx.facelib.acticity;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.rrtx.facelib.FaceConfig;
import com.rrtx.facelib.R;
import com.rrtx.facelib.base.BaseActivity;
import com.rrtx.facelib.bean.CheckBean;
import com.rrtx.facelib.model.FaceModel;
import com.rrtx.facelib.net.FaceBaseObserver;
import com.rrtx.facelib.utils.BitmapUtils;
import com.rrtx.facelib.utils.FileUtils;
import com.rrtx.facelib.utils.IntentUtil;

import java.io.File;

import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * 人证核验接口调用
 */
public class CollectVerifyActivity extends BaseActivity {
    private static final String TAG = CollectVerifyActivity.class.getSimpleName();

    private ImageView mImageAnim;
    private TextView mTextVerifyIng;
    private TextView mTextNetError;
    private TextView mTextCheckNet;
    private Button mBtnReturnHome;
    private Button mBtnReTry;

    private String mBase64Img;

    private ObjectAnimator mRotationAnimator;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_collect_verify);
        initView();
        initData();
    }

    private void initView() {
        mImageAnim = findViewById(R.id.image_anim);
        mTextVerifyIng = findViewById(R.id.text_verify_ing);
        mTextNetError = findViewById(R.id.text_net_error);
        mTextCheckNet = findViewById(R.id.text_check_net);
        mBtnReTry = findViewById(R.id.btn_retry);
        mBtnReturnHome = findViewById(R.id.btn_return_home);
        startAnim(mImageAnim);
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
            mBase64Img = IntentUtil.getInstance().getBase64Img();
            getAccessTokenFromServer(mBase64Img);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        cancelAnim();
    }

    private void startAnim(ImageView imageAnim) {
        // 构造ObjectAnimator对象的方法
        mRotationAnimator = ObjectAnimator.ofFloat(imageAnim, "rotation",
                0.0F, 360.0F); // 设置顺时针360度旋转
        mRotationAnimator.setDuration(1000); // 设置旋转时间
        mRotationAnimator.setRepeatCount(-1); // 设置动画重复次数
        mRotationAnimator.setRepeatMode(ValueAnimator.RESTART); // 动画重复模式
        mRotationAnimator.start(); // 开始执行动画（顺时针旋转动画）
    }

    /**
     * 取消动画
     */
    private void cancelAnim() {
        if (mRotationAnimator != null) {
            mRotationAnimator.cancel();
            mRotationAnimator.end();
        }
    }

    /**
     * 获取accessToken
     */
    private void getAccessTokenFromServer(final String base64Img) {

        Bitmap faceBitmap = BitmapUtils.base64ToBitmap(base64Img);

        File outputDirectory = FileUtils.getOutputDirectory(CollectVerifyActivity.this);
        File faceFile = new File(outputDirectory, FaceConfig.FacePhotoName);

        if (faceFile.exists()) {
            faceFile.delete();
        }

        BitmapUtils.saveBitmapFile(faceBitmap, faceFile.getPath());


        File photoFile = new File(outputDirectory, FaceConfig.PhotoName);

        FaceModel faceModel = new FaceModel();
        faceModel.checkUserAvatar(photoFile, faceFile, new FaceBaseObserver<CheckBean>(CollectVerifyActivity.this) {
            @Override
            public void requestSuccess(CheckBean sResData) {
                if (TextUtils.equals(sResData.getResult(), "True")) {
                    IdentitySuccessActivity.jumpIdentitySuccess(CollectVerifyActivity.this);
                } else {
                    FaceLivenessExpActivity.jumpFaceLivenessExp(CollectVerifyActivity.this);
                    ToastUtil.show(CollectVerifyActivity.this, getString(R.string.verify_error));
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(CollectVerifyActivity.this, getString(R.string.verify_error));
            }
        });


//        APIService.getInstance().init();
//        APIService.getInstance().initAccessTokenWithAkSk(new OnResultListener<AccessToken>() {
//            @Override
//            public void onResult(AccessToken result) {
//                if (result != null && result.getAccessToken() != null) {
//                    // 调用风控接口
//                    policeVerifyFromServer(base64Img, secType, getApplicationContext());
//                }
//            }
//
//            @Override
//            public void onError(final FaceException error) {
//                if (error == null) {
//                    return;
//                }
//
//                // 网络加载失败
//                if (error.getErrorCode() == FaceException.ErrorCode.NETWORK_REQUEST_ERROR) {
//                    cancelAnim();
//                    mImageAnim.setImageResource(R.drawable.icon_verify_network);
//                    mTextVerifyIng.setVisibility(View.GONE);
//                    mTextCheckNet.setVisibility(View.VISIBLE);
//                    mTextNetError.setVisibility(View.VISIBLE);
//                    mBtnReTry.setVisibility(View.VISIBLE);
//                    mBtnReturnHome.setVisibility(View.VISIBLE);
//                }
//            }
//        }, Config.apiKey, Config.secretKey);
    }

//    private void policeVerifyFromServer(String base64Img, final int secType, Context context) {
//        if (TextUtils.isEmpty(base64Img)) {
//            return;
//        }
//
//        final ConsoleConfig consoleConfig = ConsoleConfigManager.getInstance(getApplicationContext()).getConfig();
//        DynamicParams params = new DynamicParams();
//        params.setImgType("BASE64");
//        params.setBase64Img(base64Img);
//        try {
//            params.setUsername(URLDecoder.decode(mUsername, "UTF-8"));
//        } catch (UnsupportedEncodingException e) {
//            params.setUsername(mUsername);
//            e.printStackTrace();
//        }
//        params.setIdCardNum(mIdCardNum);
//        params.setQualityControl(consoleConfig.getOnlineImageQuality());
//        params.setLivenessControl(consoleConfig.getOnlineLivenessQuality());
//        params.setSpoofingControl("NONE");
//        // 以下参数只为加密版接口使用
//        if (secType == 1) {
//            params.setRiskIdentify(consoleConfig.isOpenRiskIdentify());
//            // 开启风控
//            if (consoleConfig.isOpenRiskIdentify()) {
//                params.setZid(FaceSDKManager.getInstance().getZid(context, 5001));
//                params.setIp(IPUtil.getLocalIpAddress(getApplicationContext()));
//                // TODO： 手机号，需要自己手动修改
//                // params.setPhone("");
//            }
//            params.setImageSec(true);
//            params.setApp("Android");
//            params.setEv("smrz");
//        }
//
//
//        APIService.getInstance().policeVerify(params, secType, new OnResultListener<LivenessVsIdcardResult>() {
//            @Override
//            public void onResult(LivenessVsIdcardResult result) {
//                if (result == null || consoleConfig == null) {
//                    return;
//                }
//
//                // 如果开启风控，则需要进行风险等级判断
//                if (consoleConfig.isOpenRiskIdentify()) {
//                    if ("1".equals(result.getRiskLevel())) {
//                        Intent intent = new Intent(CollectVerifyActivity.this,
//                                CollectFailureActivity.class);
//                        intent.putExtra("err_code", FaceException.ErrorCode.VERIFY_LIVENESS_FAILURE);
//                        startActivity(intent);
//                        finish();
//                        return;
//                    }
//                }
//
//                // 针对加密版接口
//                if (secType == 1 && result.getVerifyStatus() != 0) {
//                    Intent intent = new Intent(CollectVerifyActivity.this,
//                            CollectFailureActivity.class);
//                    intent.putExtra("verify_status", result.getVerifyStatus());
//                    startActivity(intent);
//                    finish();
//                    return;
//                }
//
//                // 判断人证核验匹配度
//                if (result.getScore() >= consoleConfig.getRiskScore()) {
//                    Intent intent = new Intent(CollectVerifyActivity.this,
//                            CollectionSuccessExpActivity.class);
//                    startActivity(intent);
//                    finish();
//                } else {
//                    Intent intent = new Intent(CollectVerifyActivity.this,
//                            CollectFailureActivity.class);
//                    intent.putExtra("err_code", FaceException.ErrorCode.LOW_SCORE);
//                    startActivity(intent);
//                    finish();
//                }
//            }
//
//            @Override
//            public void onError(final FaceException error) {
//                if (error == null) {
//                    return;
//                }
//
//                // 网络加载失败
//                if (error.getErrorCode() == FaceException.ErrorCode.NETWORK_REQUEST_ERROR
//                        || error.getErrorCode() == FaceException.ErrorCode.VERIFY_NET_ERROR
//                        || error.getErrorCode() == FaceException.ErrorCode.VERIFY_NET_ERROR_05) {
//                    cancelAnim();
//                    mImageAnim.setImageResource(R.drawable.icon_verify_network);
//                    mTextVerifyIng.setVisibility(View.GONE);
//                    mTextCheckNet.setVisibility(View.VISIBLE);
//                    mTextNetError.setVisibility(View.VISIBLE);
//                    mBtnReTry.setVisibility(View.VISIBLE);
//                    mBtnReturnHome.setVisibility(View.VISIBLE);
//                } else {
//                    // 其它错误码
//                    Intent intent = new Intent(CollectVerifyActivity.this,
//                            CollectFailureActivity.class);
//                    intent.putExtra("err_code", error.getErrorCode());
//                    startActivity(intent);
//                    finish();
//                }
//            }
//        });
//    }

    /**
     * 重新加载
     */
    public void onRetry(View v) {
        mImageAnim.setImageResource(R.drawable.icon_loading);
        mTextVerifyIng.setVisibility(View.VISIBLE);
        mTextCheckNet.setVisibility(View.GONE);
        mTextNetError.setVisibility(View.GONE);
        mBtnReTry.setVisibility(View.GONE);
        mBtnReturnHome.setVisibility(View.GONE);
        startAnim(mImageAnim);
        getAccessTokenFromServer(mBase64Img);
    }

    /**
     * 退出核验
     */
    public void onReturnHome(View v) {
        finish();
    }
}
