/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.rrtx.facelib.manager;


import com.rrtx.facelib.OnResultListener;
import com.rrtx.facelib.model.AccessToken;
import com.rrtx.facelib.model.DynamicParams;
import com.rrtx.facelib.model.LivenessVsIdcardResult;
import com.rrtx.facelib.utils.HttpUtil;
import com.rrtx.facelib.utils.PoliceCheckResultParser;

/**
 * Created by wangtianfei01 on 17/7/13.
 */

public class APIService {

    // 获取token
    private static final String ACCESS_TOEKN_URL = "https://aip.baidubce.com/oauth/2.0/token?";
    // 公安接口
    private static final String LIVENESS_VS_IDCARD_URL = "https://aip.baidubce.com/rest/2.0/face/v3/person/verify";
    // 加密接口
    private static final String ENCRYPT_URL = "https://aip.baidubce.com/rest/2.0/face/v3/person/verifySec";

    private String accessToken;

    private APIService() {

    }

    private static volatile APIService instance;

    public static APIService getInstance() {
        if (instance == null) {
            synchronized (APIService.class) {
                if (instance == null) {
                    instance = new APIService();
                }
            }
        }
        return instance;
    }

    // 初始化OKHttp
    public void init() {
        HttpUtil.getInstance().init();
    }

    /**
     * 设置accessToken 如何获取 accessToken 详情见:
     * @param accessToken accessToken
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getAccessToken() {
        return accessToken;
    }

    /**
     * 明文ak/sk获取token
     * @param listener  回调监听
     * @param ak         access key
     * @param sk         secret key
     */
    public void initAccessTokenWithAkSk(final OnResultListener<AccessToken> listener, String ak,
                                        String sk) {
        StringBuilder sb = new StringBuilder();
        sb.append("client_id=").append(ak);
        sb.append("&client_secret=").append(sk);
        sb.append("&grant_type=client_credentials");
        HttpUtil.getInstance().getAccessToken(listener, ACCESS_TOEKN_URL, sb.toString());
    }

    /**
     * 公安验证请求
     * @param params     请求参数
     * @param listener   回调监听
     */
    public void policeVerify(DynamicParams params, int secType,
                             OnResultListener<LivenessVsIdcardResult> listener) {
        PoliceCheckResultParser parser = new PoliceCheckResultParser();
        String url = "";
        if (secType == 0) {
            // 调用普通版url
            url = urlAppendCommonParams(LIVENESS_VS_IDCARD_URL);
        } else if (secType == 1) {
            // 调用加密版url
            url = urlAppendCommonParams(ENCRYPT_URL);
        }
        HttpUtil.getInstance().post(url, "image", params, parser, listener);
    }

    /**
     * URL拼接
     * @param url
     * @return
     */
    private String urlAppendCommonParams(String url) {
        StringBuilder sb = new StringBuilder(url);
        sb.append("?access_token=").append(accessToken);
        return sb.toString();
    }

}
