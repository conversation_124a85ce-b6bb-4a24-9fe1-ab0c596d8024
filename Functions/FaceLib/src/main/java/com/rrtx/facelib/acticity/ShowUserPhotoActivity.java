package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.lifecycle.ViewModelProvider;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.FaceConfig;
import com.rrtx.facelib.R;
import com.rrtx.facelib.databinding.FaceActivityShowUserPhotoBinding;
import com.rrtx.facelib.model.ShowUserPhotoViewModel;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.rrtx.facelib.utils.FileUtils;
import com.rrtx.facelib.utils.UserInfoSingleton;

import java.io.File;

import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 展示用户照片的页面
 */
public class ShowUserPhotoActivity extends BaseActivity<FaceActivityShowUserPhotoBinding> {

    private ShowUserPhotoViewModel mShowUserPhotoViewModel;

    public static void jumpShowUserPhoto(Context context) {
        Intent intent = new Intent(context, ShowUserPhotoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.face_activity_show_user_photo;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setShowUserPhotoOpt(new ShowUserPhotoOpt());
        mShowUserPhotoViewModel = new ViewModelProvider(this).get(ShowUserPhotoViewModel.class);

        mShowUserPhotoViewModel.getPhotoLv().observe(this, photoBean -> {

            UserInfoSingleton.getInstance().setUserInfo(photoBean);

            String imagePath = photoBean.getImage();
            if (!TextUtils.isEmpty(imagePath)) {
                dataBinding.showUserIv.setTag(null);
                imagePath = "https://ocr.rrtx.vimbug.com/OCR/getIdcardImage?image_name=" + imagePath;
                Glide.with(this)
                        .load(imagePath)
                        .centerCrop()
                        .diskCacheStrategy(DiskCacheStrategy.NONE)//禁用磁盘缓存
                        .skipMemoryCache(true)//跳过内存缓存
                        .into(dataBinding.showUserIv);
            }
        });

        mShowUserPhotoViewModel.getErrorLv().observe(this, error -> {
            ToastUtil.show(mContext, error);
            finish();
        });
    }

    @Override
    public void initView() {
        super.initView();

        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setText(getString(R.string.identity_auth));
    }

    @Override
    public void initData() {
        super.initData();
        //获取图片的路径
        File outputDirectory = FileUtils.getOutputDirectory(mContext);
        File photoFile = new File(outputDirectory, FaceConfig.PhotoName);
//
//        //显示图片
//        Uri uri = Uri.fromFile(photoFile);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
//            ImageDecoder.Source source = ImageDecoder.createSource(photoFile);
//            try {
//                Drawable drawable = ImageDecoder.decodeDrawable(source);
//                dataBinding.showUserIv.setImageDrawable(drawable);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        } else {
//            try {
//                Bitmap bitmap = MediaStore.Images.Media.getBitmap(mContext.getContentResolver(), uri);
//                dataBinding.showUserIv.setImageBitmap(bitmap);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }

//        BitmapUtils.compressBitmap(photoFile.getPath(), 200);
        mShowUserPhotoViewModel.uploadUserAvatar(mContext, photoFile);
    }

    public class ShowUserPhotoOpt extends BaseTitleClick {

        @Override
        public void leftClick() {
            finish();
        }

        public void nextClick() {
            UserInfoActivity.jumpUserInfo(mContext);
        }
    }
}