/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.rrtx.facelib.model;

@SuppressWarnings("unused")
public class LivenessVsIdcardResult extends ResponseResult {

    private double score;
    private double faceliveness;
    private String idcardImage;
    private String riskLevel;
    private int verifyStatus;

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public String getIdcardImage() {
        return idcardImage;
    }

    public void setIdcardImage(String idcardImage) {
        this.idcardImage = idcardImage;
    }

    public double getFaceliveness() {
        return faceliveness;
    }

    public void setFaceliveness(double faceliveness) {
        this.faceliveness = faceliveness;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public int getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(int verifyStatus) {
        this.verifyStatus = verifyStatus;
    }
}

