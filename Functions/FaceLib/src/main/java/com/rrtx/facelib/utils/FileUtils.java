package com.rrtx.facelib.utils;

import android.app.Activity;

import com.rrtx.facelib.R;

import java.io.File;

/**
 * <AUTHOR>
 * 文件夹工具类
 * https://blog.csdn.net/wyg1230/article/details/79153641
 * https://blog.csdn.net/weixin_28729271/article/details/80656614
 */
public class FileUtils {

    /**
     * 获取图片存放的路径
     *
     * @param activity 上下文
     * @return 文件
     */
    public static File getOutputDirectory(Activity activity) {

        File[] externalMediaDirs = activity.getExternalMediaDirs();
        if (externalMediaDirs != null) {
            File firstFile = externalMediaDirs[0];

            File resultFile = new File(firstFile, activity.getResources().getString(R.string.app_name));
            if (!resultFile.exists()) {
                resultFile.mkdirs();
            }

            return resultFile;
        }

        return activity.getFilesDir();
    }
}
