package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;

import com.alibaba.android.arouter.launcher.ARouter;
import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.R;
import com.rrtx.facelib.bean.PhotoBean;
import com.rrtx.facelib.databinding.FaceActivityIdentitySuccessBinding;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.rrtx.facelib.utils.UserInfoSingleton;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils;

/**
 * <AUTHOR>
 * 校验成功
 */
public class IdentitySuccessActivity extends BaseActivity<FaceActivityIdentitySuccessBinding> {

    public static void jumpIdentitySuccess(Context context) {
        Intent intent = new Intent(context, IdentitySuccessActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected int createContentView() {
        return R.layout.face_activity_identity_success;
    }

    @Override
    protected void initWorkspaceAction() {

    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.statusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setText(getString(R.string.identity_auth));

        PhotoBean photoBean = UserInfoSingleton.getInstance().getPhotoBean();
        dataBinding.nameTv.setText(photoBean.getName());
        dataBinding.idTv.setText(photoBean.getIDNumber());

        dataBinding.timeTv.setText(TimeUtils.long2StringAll(System.currentTimeMillis()));
    }

    public class IdentityOpt extends BaseTitleClick {
        @Override
        public void leftClick() {
            ARouter.getInstance().build(ARouterPath.SecurityPath.SecurityCenterActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    .navigation();
        }
    }

    @Override
    public void onBackPressed() {
        ARouter.getInstance().build(ARouterPath.SecurityPath.SecurityCenterActivity)
                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP)
                .navigation();
    }
}