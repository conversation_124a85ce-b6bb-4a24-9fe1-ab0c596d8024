package com.rrtx.facelib.model;

import com.rrtx.facelib.FaceCheckRetrofitServiceManager;
import com.rrtx.facelib.FaceCheckService;
import com.rrtx.facelib.FaceRetrofitServiceManager;
import com.rrtx.facelib.FaceService;
import com.rrtx.facelib.bean.CheckBean;
import com.rrtx.facelib.bean.PhotoBean;
import com.rrtx.facelib.net.FaceBaseObserver;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;

/**
 * <AUTHOR>
 */
public class FaceModel extends BaseLoader {

    private FaceService mFaceService;
    private FaceCheckService mFaceCheckService;

    public FaceModel() {
        mFaceService = FaceRetrofitServiceManager.getInstance().create(FaceService.class);
        mFaceCheckService = FaceCheckRetrofitServiceManager.getInstance().create(FaceCheckService.class);
    }

    public void uploadUserAvatar(File userPhoto, FaceBaseObserver<PhotoBean> baseObserver) {
        RequestBody requestFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), userPhoto);

        MultipartBody.Part body =
                MultipartBody.Part.createFormData("file_obj", userPhoto.getName(), requestFile);

        observe(mFaceService.uploadImg(body)).subscribe(baseObserver);
    }

    public void checkUserAvatar(File userPhoto, File userAvatar, FaceBaseObserver<CheckBean> baseObserver) {
        List<MultipartBody.Part> list = new ArrayList<>();
        RequestBody userFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), userPhoto);
        MultipartBody.Part userPhotoBody =
                MultipartBody.Part.createFormData("file_obj_source", userPhoto.getName(), userFile);


        RequestBody userAvatarFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), userAvatar);
        MultipartBody.Part userAvatarBody =
                MultipartBody.Part.createFormData("file_obj_target", userAvatar.getName(), userAvatarFile);


        list.add(userPhotoBody);
        list.add(userAvatarBody);

        observe(mFaceCheckService.checkImg(list)).subscribe(baseObserver);
    }
}
