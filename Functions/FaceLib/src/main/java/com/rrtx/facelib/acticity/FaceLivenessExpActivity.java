package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.baidu.idl.face.platform.FaceStatusNewEnum;
import com.baidu.idl.face.platform.model.ImageInfo;
import com.rrtx.facelib.utils.IntentUtil;
import com.rrtx.facelib.widget.TimeoutDialog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FaceLivenessExpActivity extends FaceLivenessActivity implements
        TimeoutDialog.OnTimeoutDialogClickListener {

    public static final String EXT_USERNAME = "username";
    public static final String EXT_ID_NUMBER = "idNumber";

    private TimeoutDialog mTimeoutDialog;
    private Context mContext;

    public static void jumpFaceLivenessExp(Context context) {
        Intent faceIntent = new Intent();
        faceIntent.setClass(context, FaceLivenessExpActivity.class);
        faceIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK|Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(faceIntent);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = this;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mContext = null;
    }

    @Override
    public void onLivenessCompletion(FaceStatusNewEnum status, String message,
                                     HashMap<String, ImageInfo> base64ImageCropMap,
                                     HashMap<String, ImageInfo> base64ImageSrcMap, int currentLivenessCount) {
        super.onLivenessCompletion(status, message, base64ImageCropMap, base64ImageSrcMap, currentLivenessCount);
        if (status == FaceStatusNewEnum.OK && mIsCompletion) {
            getBestImage(base64ImageCropMap, base64ImageSrcMap);
        } else if (status == FaceStatusNewEnum.DetectRemindCodeTimeout) {
            if (mViewBg != null) {
                mViewBg.setVisibility(View.VISIBLE);
            }
            showMessageDialog();
        }
    }

    /**
     * 获取采集返回的最优抠图和原图
     *
     * @param imageCropMap 抠图集合
     * @param imageSrcMap  原图集合
     */
    private void getBestImage(HashMap<String, ImageInfo> imageCropMap, HashMap<String, ImageInfo> imageSrcMap) {
        String cropBmpStr = null;
        String cropSecBmpStr = null;
        String srcBmpStr = null;
        String srcSecBmpStr = null;
        // 将抠图集合中的图片按照质量降序排序，最终选取质量最优的一张抠图图片
        if (imageCropMap != null && imageCropMap.size() > 0) {
            List<Map.Entry<String, ImageInfo>> list1 = new ArrayList<>(imageCropMap.entrySet());
            Collections.sort(list1, new Comparator<Map.Entry<String, ImageInfo>>() {

                @Override
                public int compare(Map.Entry<String, ImageInfo> o1,
                                   Map.Entry<String, ImageInfo> o2) {
                    String[] key1 = o1.getKey().split("_");
                    String score1 = key1[2];
                    String[] key2 = o2.getKey().split("_");
                    String score2 = key2[2];
                    // 降序排序
                    return Float.valueOf(score2).compareTo(Float.valueOf(score1));
                }
            });

            cropBmpStr = list1.get(0).getValue().getBase64();
            cropSecBmpStr = list1.get(0).getValue().getSecBase64();
        }

        // 将原图集合中的图片按照质量降序排序，最终选取质量最优的一张原图图片
        if (imageSrcMap != null && imageSrcMap.size() > 0) {
            List<Map.Entry<String, ImageInfo>> list2 = new ArrayList<>(imageSrcMap.entrySet());
            Collections.sort(list2, new Comparator<Map.Entry<String, ImageInfo>>() {

                @Override
                public int compare(Map.Entry<String, ImageInfo> o1,
                                   Map.Entry<String, ImageInfo> o2) {
                    String[] key1 = o1.getKey().split("_");
                    String score1 = key1[2];
                    String[] key2 = o2.getKey().split("_");
                    String score2 = key2[2];
                    // 降序排序
                    return Float.valueOf(score2).compareTo(Float.valueOf(score1));
                }
            });

            srcBmpStr = list2.get(0).getValue().getBase64();
            srcSecBmpStr = list2.get(0).getValue().getSecBase64();
        }

        intentActivity(cropBmpStr, cropSecBmpStr, srcBmpStr, srcSecBmpStr);
    }

    /**
     * 进行页面跳转，往新页面需要传入：图片的base64、图片加密类型（加密、不加密）、姓名、身份证号
     *
     * @param cropBmpStr    抠图不加密的base64
     * @param cropSecBmpStr 抠图加密的base64
     * @param srcBmpStr     原图不加密的base64
     * @param srcSecBmpStr  原图加密的base64
     */
    private void intentActivity(String cropBmpStr, String cropSecBmpStr, String srcBmpStr, String srcSecBmpStr) {

        // TODO: 12/28/20 这里应该传递参数 到下个页面请求接口
        Intent intent = new Intent(mContext, CollectVerifyActivity.class);
        int outputImageType = mFaceConfig.getOutputImageType();
        int secType = mFaceConfig.getSecType();
        // 将采集成功的图片以base64的方式传入到认证页面进行风控请求认证，因图片base64过大，Intent传值
        // 极易造成崩溃，所以采用IntentUtil方式传值

        // 抠图
        if (outputImageType == 1) {
//            if (secType == 0) {    // 不加密
                IntentUtil.getInstance().setBase64Img(cropBmpStr);
//            } else if (secType == 1) {   // 加密
//                IntentUtil.getInstance().setSecBase64Img(cropSecBmpStr);
//            }
            // 原图
        } else if (outputImageType == 0) {
//            if (secType == 0) {   // 不加密
                IntentUtil.getInstance().setBase64Img(srcBmpStr);
//            } else if (secType == 1) {   // 加密
//                IntentUtil.getInstance().setSecBase64Img(srcSecBmpStr);
//            }
        }

        startActivity(intent);
    }

    /**
     * 显示超时弹窗
     */
    private void showMessageDialog() {
        mTimeoutDialog = new TimeoutDialog(this);
        mTimeoutDialog.setDialogListener(this);
        mTimeoutDialog.setCanceledOnTouchOutside(false);
        mTimeoutDialog.setCancelable(false);
        mTimeoutDialog.show();
        onPause();
    }

    /**
     * 弹窗：重新采集
     */
    @Override
    public void onRecollect() {
        if (mTimeoutDialog != null) {
            mTimeoutDialog.dismiss();
        }
        if (mViewBg != null) {
            mViewBg.setVisibility(View.GONE);
        }
        onResume();
    }

    /**
     * 弹窗：关闭
     */
    @Override
    public void onReturn() {
        if (mTimeoutDialog != null) {
            mTimeoutDialog.dismiss();
        }
    }
}
