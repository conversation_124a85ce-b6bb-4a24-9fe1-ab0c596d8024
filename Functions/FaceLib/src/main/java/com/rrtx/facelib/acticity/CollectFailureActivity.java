package com.rrtx.facelib.acticity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.rrtx.facelib.R;
import com.rrtx.facelib.base.BaseActivity;
import com.rrtx.facelib.exception.FaceException;
import com.rrtx.facelib.utils.LogUtil;

/**
 * 采集请求失败页面
 * Created by v_liujialu01 on 2020/8/21.
 */

public class CollectFailureActivity extends BaseActivity {
    private static final String TAG = CollectFailureActivity.class.getSimpleName();

    private TextView mTextErrMessage;
    private TextView mTextErrTips;
    private Button mBtnReturnHome;
    private Button mBtnReCollect;
    private ImageView mImageError;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_collect_failure);
        initView();
        initData();
    }

    private void initView() {
        mTextErrMessage = (TextView) findViewById(R.id.text_err_message);
        mTextErrTips = (TextView) findViewById(R.id.text_err_tips);
        mBtnReturnHome = (Button) findViewById(R.id.btn_return_home);
        mBtnReCollect = (Button) findViewById(R.id.btn_recollect);
        mImageError = (ImageView) findViewById(R.id.image_failure_icon);
    }

    private void initData() {
        Intent intent = getIntent();
        if (intent != null) {
            int errCode = intent.getIntExtra("err_code", 0);
            int verifyStatus = intent.getIntExtra("verify_status", 0);
            judgeErrorCode(errCode, verifyStatus);
        }
    }

    private void judgeErrorCode(int errCode, int verifyStatus) {
        LogUtil.e(TAG, "errCode = " + errCode + ", verifyStatus = " + verifyStatus);
        // 公安网身份信息覆盖不全
        if (errCode == FaceException.ErrorCode.VERIFY_NO_PICTURE_50
                || errCode == FaceException.ErrorCode.VERIFY_NO_PICTURE_55
                || verifyStatus == 2) {
            mTextErrMessage.setText(getString(R.string.tips1));
            mTextErrTips.setText(getString(R.string.tips2));
            mBtnReturnHome.setText(getString(R.string.closed));
            mBtnReCollect.setVisibility(View.GONE);
            // 活体检测未通过
        } else if (errCode == FaceException.ErrorCode.VERIFY_LIVENESS_FAILURE) {
            mTextErrMessage.setText(getString(R.string.tips3));
            mTextErrTips.setText("请确保是本人操作且正脸采集");
            // 姓名与身份证不匹配或分值低
        } else if (errCode == FaceException.ErrorCode.VERITY_NO_MATCH_51
                || errCode == FaceException.ErrorCode.VERITY_NO_MATCH_54
                || errCode == FaceException.ErrorCode.VERITY_NO_MATCH_60
                || errCode == FaceException.ErrorCode.LOW_SCORE
                || verifyStatus == 1) {
            mTextErrMessage.setText(getString(R.string.tips3));
            mTextErrTips.setText(getString(R.string.please_again));
            // 质量检测未通过
        } else if (errCode == FaceException.ErrorCode.VERITY_QUALITY_FAILURE) {
            mTextErrMessage.setText(getString(R.string.tips3));
            mTextErrTips.setText(getString(R.string.tips4));
        } else {
            // 其他错误
            mTextErrMessage.setText(getString(R.string.params_error));
            mTextErrTips.setText(getString(R.string.tips5));
            mBtnReturnHome.setText(getString(R.string.closed));
            mBtnReCollect.setVisibility(View.GONE);
            mImageError.setImageResource(R.drawable.icon_params_error);
        }
    }

    public void onCloseVerify(View v) {
        finish();
    }

    public void onRecollect(View v) {
        finish();
    }
}
