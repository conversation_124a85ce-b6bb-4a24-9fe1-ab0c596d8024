package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;

import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.R;
import com.rrtx.facelib.bean.PhotoBean;
import com.rrtx.facelib.databinding.FaceActivityUserInfoBinding;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.rrtx.facelib.utils.UserInfoSingleton;

import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;


/**
 * <AUTHOR>
 * 基本信息确认页面
 */
public class UserInfoActivity extends BaseActivity<FaceActivityUserInfoBinding> {

    public static void jumpUserInfo(Context context) {
        Intent intent = new Intent(context, UserInfoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.face_activity_user_info;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setUserInfoOpt(new UserInfoOpt());
    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));

        dataBinding.includeTitle.titleTv.setText(getString(R.string.check_info));
    }

    @Override
    public void initData() {
        super.initData();
        PhotoBean photoBean = UserInfoSingleton.getInstance().getPhotoBean();
        if (photoBean != null) {
            dataBinding.nameTv.setText(photoBean.getName());
            dataBinding.idTv.setText(photoBean.getIDNumber());
            dataBinding.genderTv.setText(photoBean.getGender());
            dataBinding.addressTv.setText(photoBean.getAddress());
            dataBinding.validTv.setText("20year");
        }
    }

    public class UserInfoOpt extends BaseTitleClick {
        @Override
        public void leftClick() {
            finish();
        }

        public void next() {
            FaceAuthActivity.jumpFaceAuth(mContext);
        }
    }
}