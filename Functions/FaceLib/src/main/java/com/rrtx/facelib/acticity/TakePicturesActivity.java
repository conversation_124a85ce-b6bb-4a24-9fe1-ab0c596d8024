package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.camera.core.AspectRatio;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;
import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.FaceConfig;
import com.rrtx.facelib.R;
import com.rrtx.facelib.databinding.FaceActivityTakePicturesBinding;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.rrtx.facelib.utils.FileUtils;
import com.rrtx.facelib.utils.LogUtil;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;

/**
 * <AUTHOR>
 * 拍照页面
 * https://codelabs.developers.google.com/codelabs/camerax-getting-started#3
 */
public class TakePicturesActivity extends BaseActivity<FaceActivityTakePicturesBinding> {

    private static final String FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS";
    private ExecutorService cameraExecutor;
    private File outputDirectory;
    private ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
    private ImageCapture mImageCapture;
    private ProcessCameraProvider mCameraProvider;
    private Runnable mCameraRunnable = () -> {
        try {
            mCameraProvider = cameraProviderFuture.get();

            Preview preview = new Preview.Builder()
                    .build();

            mImageCapture = new ImageCapture.Builder().build();

            //设置比例
            ImageAnalysis imageAnalysis = new ImageAnalysis.Builder()
                    .setTargetRotation(Surface.ROTATION_90)
                    .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                    .build();

            preview.setSurfaceProvider(dataBinding.picSur.createSurfaceProvider());

            CameraSelector cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;

            mCameraProvider.unbindAll();

            mCameraProvider.bindToLifecycle(this, cameraSelector, preview, mImageCapture, imageAnalysis);
        } catch (Exception e) {
            e.printStackTrace();
        }
    };

    public static void jumpTakePictures(Context context) {
        Intent intent = new Intent(context, TakePicturesActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected int createContentView() {
        return R.layout.face_activity_take_pictures;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setTakePicOpt(new TakePicOpt());
    }

    @Override
    public void initView() {
        super.initView();

        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();
        dataBinding.includeTitle.backIv.setImageResource(R.drawable.ic_back_w);
        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.face_color_464646));
        dataBinding.includeTitle.statusView.setBackgroundColor(getResources().getColor(R.color.face_color_464646));
    }

    @Override
    public void initData() {
        startCamera();

        //初始化线程池
        cameraExecutor = Executors.newSingleThreadExecutor();

        //创建相应的路径
        outputDirectory = FileUtils.getOutputDirectory(mContext);
    }

    /**
     * 开启摄像机
     */
    private void startCamera() {
        cameraProviderFuture = ProcessCameraProvider.getInstance(mContext);
        cameraProviderFuture.addListener(mCameraRunnable, ContextCompat.getMainExecutor(this));
    }

    private void takePhoto() {


        if (mImageCapture == null) {
            return;
        }

        File photoFile = new File(outputDirectory, FaceConfig.PhotoName);

        if (photoFile.exists()) {
            photoFile.delete();
        }

        ImageCapture.OutputFileOptions outputOptions = new ImageCapture.OutputFileOptions.Builder(photoFile).build();

        mImageCapture.takePicture(outputOptions, ContextCompat.getMainExecutor(this), new ImageCapture.OnImageSavedCallback() {
            @Override
            public void onImageSaved(@NonNull ImageCapture.OutputFileResults outputFileResults) {
                //保存成功,直接跳转到下个页面,在下个页面进行请求
                Uri uri = Uri.fromFile(photoFile);
                LogUtil.e("TAG", "onImageSaved: " + uri);
                ShowUserPhotoActivity.jumpShowUserPhoto(mContext);
            }

            @Override
            public void onError(@NonNull ImageCaptureException exception) {
                //保存失败
                LogUtil.e("TAG", "onError: " + exception);
            }
        });
    }


    public class TakePicOpt extends BaseTitleClick {
        @Override
        public void leftClick() {
            finish();
        }

        public void takePic() {
            takePhoto();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cameraExecutor.shutdown();
        if (mCameraProvider != null) {
            mCameraProvider.unbindAll();
        }
    }
}