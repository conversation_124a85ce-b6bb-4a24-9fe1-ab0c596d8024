package com.rrtx.facelib.model;

import android.content.Context;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.rrtx.facelib.bean.PhotoBean;
import com.rrtx.facelib.net.FaceBaseObserver;

import java.io.File;

/**
 * <AUTHOR>
 * 展示用户
 */
public class ShowUserPhotoViewModel extends ViewModel {

    private FaceModel mFaceModel;
    private MutableLiveData<PhotoBean> mPhotoLv = new MutableLiveData<>();
    private MutableLiveData<String> mErrorLv = new MutableLiveData<>();

    public ShowUserPhotoViewModel() {
        mFaceModel = new FaceModel();
    }

    public MutableLiveData<PhotoBean> getPhotoLv() {
        if (mPhotoLv == null) {
            mPhotoLv = new MutableLiveData<>();
        }
        return mPhotoLv;
    }

    public MutableLiveData<String> getErrorLv() {
        if (mErrorLv == null) {
            mErrorLv = new MutableLiveData<>();
        }
        return mErrorLv;
    }

    /**
     * 请求账户余额接口
     */
    public void uploadUserAvatar(Context context, File userPhoto) {
        mFaceModel.uploadUserAvatar(userPhoto, new FaceBaseObserver<PhotoBean>(context) {
            @Override
            public void requestSuccess(PhotoBean sResData) {
                getPhotoLv().setValue(sResData);
            }

            @Override
            public void requestFail(String sResMsg) {
                getErrorLv().setValue(sResMsg);
            }
        });
    }
}
