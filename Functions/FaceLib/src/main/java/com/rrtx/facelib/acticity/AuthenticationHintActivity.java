package com.rrtx.facelib.acticity;

import android.Manifest;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.FaceCheckRetrofitServiceManager;
import com.rrtx.facelib.FaceConfig;
import com.rrtx.facelib.FaceRetrofitServiceManager;
import com.rrtx.facelib.R;
import com.rrtx.facelib.databinding.FaceActivityAuthenticationHintBinding;
import com.rrtx.facelib.net.HttpLogger;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.tbruyelle.rxpermissions2.RxPermissions;

import io.reactivex.disposables.Disposable;
import okhttp3.Interceptor;
import okhttp3.logging.HttpLoggingInterceptor;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitConfig;

/**
 * <AUTHOR>
 * 人脸识别提示页面
 * 第一个页面
 */
@Route(path = ARouterPath.Face.AuthenticationHintActivity)
public class AuthenticationHintActivity extends BaseActivity<FaceActivityAuthenticationHintBinding> {

    private RxPermissions mRxPermissions;
    private DoubleDialog mDoubleDialog;

    @Override
    protected int createContentView() {
        return R.layout.face_activity_authentication_hint;
    }

    @Override
    protected void initWorkspaceAction() {
        HttpLoggingInterceptor logInterceptor = new HttpLoggingInterceptor(new HttpLogger());
        logInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        //初始化网络请求
        RetrofitConfig retrofitConfig = new RetrofitConfig.Builder()
                .setUrl(FaceConfig.URL.BASEURL)
                .setReadTime(60)
                .setWriteTime(60)
                .setConnectTime(60)
                .setRetry(true)
                .setGson(true)
                .setInterceptor(new Interceptor[]{
                        //请求打印数据的拦截器
                        logInterceptor,
                })
                .builder();

        //初始化网络请求
        RetrofitConfig checkConfig = new RetrofitConfig.Builder()
                .setUrl(FaceConfig.URL.CHECKBASEURL)
                .setReadTime(60)
                .setWriteTime(60)
                .setConnectTime(60)
                .setRetry(true)
                .setGson(true)
                .setInterceptor(new Interceptor[]{
                        //请求打印数据的拦截器
                        logInterceptor,
                })
                .builder();

        FaceRetrofitServiceManager.getInstance().init(retrofitConfig);
        FaceCheckRetrofitServiceManager.getInstance().init(checkConfig);

        dataBinding.setAuthenticationOpt(new AuthenticationOpt());
    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setText(getString(R.string.identity_auth));
    }

    @Override
    public void initData() {
        super.initData();
        mRxPermissions = new RxPermissions(this);
    }

    public class AuthenticationOpt extends BaseTitleClick {
        @Override
        public void leftClick() {
            finish();
        }

        public void scanCertificate() {
            Disposable subscribe = mRxPermissions.request(
                    Manifest.permission.CAMERA,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    .subscribe((isSuccess) -> {
                        mDoubleDialog = new DoubleDialog(mContext);
                        mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                            @Override
                            public void leftCallback() {
                                mDoubleDialog.dismiss();
                            }

                            @Override
                            public void rightCallback() {
                                mDoubleDialog.dismiss();
                                TakePicturesActivity.jumpTakePictures(mContext);
                            }
                        });
                        mDoubleDialog.show();
                        mDoubleDialog.setMyTitle(getString(R.string.common_alert_prompt))
                                .setContentStr("\"xWallet\" wants to access your camera\n" +
                                        "If it is not allowed, you will not be able to use it in xWallet\n" +
                                        "scan code, brush face, take pictures and shoot, etc")
                                .setLeftStr(getString(R.string.common_alert_keep_pay))
                                .setLeftColor(getResources().getColor(R.color.color_F85A40))
                                .setRightStr(getString(R.string.common_alert_ok))
                                .setRightColor(getResources().getColor(R.color.common_ye_F3881E));
                    });
        }
    }
}