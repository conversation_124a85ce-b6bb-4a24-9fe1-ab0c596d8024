/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.rrtx.facelib.model;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class DynamicParams implements RequestParams {

    private Map<String, Object> params = new HashMap<>();
    private Map<String, File> fileMap = new HashMap<>();
    private String jsonParams;

    @Override
    public Map<String, File> getFileParams() {
        return fileMap;
    }

    @Override
    public Map<String, Object> getStringParams() {
        return params;
    }

    public void setJsonParams(String jsonParams) {
        this.jsonParams = jsonParams;
    }

    @Override
    public String getJsonParams() {
        return this.jsonParams;
    }

    private void putParam(String key, String value) {
        if (value != null) {
            params.put(key, value);
        }
    }

    public void putParam(String key, int value) {
        params.put(key, value);
    }

    private void putParam(String key, boolean value) {
        params.put(key, value);
    }

    public void putFile(String key, File file) {
        fileMap.put(key, file);
    }

    public void setBase64Img(String base64Img) {
        putParam("image", base64Img);
    }

    public void setImgType(String imgType) {
        putParam("image_type", imgType);
    }

    public void setUsername(String username) {
        putParam("name", username);
    }

    public void setIdCardNum(String idCardNum) {
        putParam("id_card_number", idCardNum);
    }

    public void setQualityControl(String qualControl) {
        putParam("quality_control", qualControl);
    }

    public void setLivenessControl(String liveControl) {
        putParam("liveness_control", liveControl);
    }

    public void setSpoofingControl(String spoofControl) {
        putParam("spoofing_control", spoofControl);
    }

    // 以下为加密版接口
    public void setRiskIdentify(boolean riskIdentify) {
        putParam("risk_identify", riskIdentify);
    }

    public void setZid(String zid) {
        putParam("zid", zid);
    }

    public void setIp(String ip) {
        putParam("ip", ip);
    }

    public void setPhone(String phone) {
        putParam("phone", phone);
    }

    public void setImageSec(boolean imageSec) {
        putParam("image_sec", imageSec);
    }

    public void setApp(String app) {
        putParam("app", app);
    }

    public void setEv(String ev) {
        putParam("ev", ev);
    }
}
