/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.rrtx.facelib.model;


public class Config {
    //    // 百度AI官网ai.baidu.com人脸模块创建应用，选择相应模块，然后查看ak,sk(公安权限需要在官网控制台提交工单开启)
    //    // 为了apiKey,secretKey为您调用百度人脸在线接口的，如注册，识别等。
    //    // 为了的安全，建议放在您的服务端，端把人脸传给服务器，在服务端端进行人脸注册、识别放在示例里面是为了您快速看到效果
    public static String apiKey = "MiDGwSP1MsYDx6lhw6RjUy7q";
    public static String secretKey = "ia8ukOt5GXj7DAB7XIfjezXktrSmAzPn";
    public static String licenseID = "xWalletFaceDemo-face-android";
    public static String licenseFileName = "idl-license.face-android";
}
