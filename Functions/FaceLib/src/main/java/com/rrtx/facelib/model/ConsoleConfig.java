package com.rrtx.facelib.model;

import android.text.TextUtils;

import com.baidu.idl.face.platform.LivenessTypeEnum;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 与控制台交互的配置信息
 * Created by v_liujialu01 on 2020/8/10.
 */
public class ConsoleConfig {

    // 动作活体类型
    private static Map<String, LivenessTypeEnum> actionKVMap = new HashMap<>();

    static {
        actionKVMap.put("eye", LivenessTypeEnum.Eye);
        actionKVMap.put("mouth", LivenessTypeEnum.Mouth);
        actionKVMap.put("headRight", LivenessTypeEnum.HeadRight);
        actionKVMap.put("headLeft", LivenessTypeEnum.HeadLeft);
        actionKVMap.put("headUp", LivenessTypeEnum.HeadUp);
        actionKVMap.put("headDown", LivenessTypeEnum.HeadDown);
        actionKVMap.put("yaw", LivenessTypeEnum.HeadLeftOrRight);
    }

    // 随机
    private static final String ACTION_RANDOM = "random";

    private float occlusion = 0.6f;     // 遮挡阈值
    private float illumination = 40;  // 光照阈值
    private float blur = 0.5f;          // 模糊阈值
    private int useOcr = 1;          // 是否使用Ocr

    private boolean isFaceVerifyRandom = false;
    private List<LivenessTypeEnum> actions = new ArrayList<>();

    private boolean isOpenActionLive = true;   // 是否开启动作活体
    private int secType = 1;                // 加密类型：0、普通Base64加密；1、加密算法加密
    private boolean isOpenRiskIdentify = false; // 是否开启风控
    private double riskScore = 80f;           // 公安验证是否是同一人的阈值设定

    // 用于从控制台获取，作为入参传入公安验证接口
    private String onlineImageQuality;
    private String onlineLivenessQuality;

    public static String getActionRandom() {
        return ACTION_RANDOM;
    }

    public float getOcclusion() {
        return occlusion;
    }

    public float getIllumination() {
        return illumination;
    }

    public float getBlur() {
        return blur;
    }

    public boolean isFaceVerifyRandom() {
        return isFaceVerifyRandom;
    }

    public List<LivenessTypeEnum> getActions() {
        return actions;
    }

    public String getOnlineImageQuality() {
        return onlineImageQuality;
    }

    public String getOnlineLivenessQuality() {
        return onlineLivenessQuality;
    }

    public boolean isOpenActionLive() {
        return isOpenActionLive;
    }

    public int getSecType() {
        return secType;
    }

    public boolean isOpenRiskIdentify() {
        return isOpenRiskIdentify;
    }

    public double getRiskScore() {
        return riskScore;
    }

    public int getUseOcr() {
        return useOcr;
    }

    /**
     * 解析json文件的内容
     *
     * @param jsonObject JSON数据
     * @throws JSONException JSON异常
     */
    public void parseFromJSONObject(JSONObject jsonObject) throws Exception {
        // 获取version 字段，目前支持1.0.0 和 2.0.1，后续扩展根据版本
        String version = jsonObject.optString("version");
        if (TextUtils.isEmpty(version)) {
            return;
        }

        switch (version) {
            case "1.0.0":
                parseFromJSONObjectFirst(jsonObject);
                break;
            case "2.0.1":
                parseFromJSONObjectSecond(jsonObject);
                break;
            default:
                break;
        }
    }

    private void parseFromJSONObjectFirst(JSONObject jsonObject) throws Exception {
        JSONObject imageQuality = jsonObject.optJSONObject("localImageQuality");
        occlusion = (float) imageQuality.optDouble("occlusion");
        illumination = (float) imageQuality.optDouble("illumination");
        blur = (float) imageQuality.optDouble("blur");
        // ocr
        int collection = jsonObject.optInt("useOCR");
        if (collection == 1) {
            useOcr = 1;
        } else {
            useOcr = 0;
        }
        // 动作随机
        String randomFlag = jsonObject.optString("faceVerifyActionOrder");
        if (ACTION_RANDOM.equals(randomFlag)) {
            isFaceVerifyRandom = true;
        }

        onlineImageQuality = jsonObject.optString("onlineImageQuality");
        onlineLivenessQuality = jsonObject.optString("onlineLivenessQuality");

        JSONArray ja = jsonObject.optJSONArray("faceVerifyAction");

        if (ja != null) {
            for (int i = 0; i < ja.length(); i++) {
                String action = ja.getString(i);
                if (actionKVMap.get(action) == null) {
                    throw new JSONException("初始配置读取失败, JSON格式不正确");
                }
                actions.add(actionKVMap.get(action));
            }
        }
    }

    private void parseFromJSONObjectSecond(JSONObject jsonObject) throws Exception {
        JSONObject imageQuality = jsonObject.optJSONObject("localImageQuality");
        occlusion = (float) imageQuality.optDouble("occlusion");
        illumination = (float) imageQuality.optDouble("illumination");
        blur = (float) imageQuality.optDouble("blur");
        // ocr
        int collection = jsonObject.optInt("collection");
        if (collection == 1) {
            useOcr = 1;
        } else {
            useOcr = 0;
        }
        // 动作随机
        String randomFlag = jsonObject.optString("faceVerifyActionOrder");
        if (ACTION_RANDOM.equals(randomFlag)) {
            isFaceVerifyRandom = true;
        }

        onlineImageQuality = jsonObject.optString("onlineImageQuality");
        onlineLivenessQuality = jsonObject.optString("onlineLivenessQuality");

        JSONArray ja = jsonObject.optJSONArray("faceVerifyAction");

        if (ja != null) {
            for (int i = 0; i < ja.length(); i++) {
                String action = ja.getString(i);
                if (actionKVMap.get(action) == null) {
                    throw new JSONException("初始配置读取失败, JSON格式不正确");
                }
                actions.add(actionKVMap.get(action));
            }
        }

        int openActionLive = jsonObject.optInt("faceVerifyActionCheck");
        isOpenActionLive = (openActionLive == 1);

        int policeCheck = jsonObject.optInt("policeCheck");
        if (policeCheck == 1) {
            secType = 1;
        } else {
            secType = 0;
        }

        int openRiskIdentify = jsonObject.optInt("risk");
        isOpenRiskIdentify = (openRiskIdentify == 1);

        riskScore = jsonObject.optDouble("policeThreshold");
    }
}
