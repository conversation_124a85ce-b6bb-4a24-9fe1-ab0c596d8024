package com.rrtx.facelib.utils;

/**
 * 关于Intent传值所用的工具类，解决Intent传入较大的值造成的崩溃问题
 * Created by v_liujialu01 on 2020/8/21.
 */

public class IntentUtil {

    private static IntentUtil instance = null;

    private String base64Img;
    private String secBase64Img;

    /**
     * 单例模式
     * @return IntentUtil实体
     */
    public static IntentUtil getInstance() {
        if (instance == null) {
            synchronized (IntentUtil.class) {
                if (instance == null) {
                    instance = new IntentUtil();
                }
            }
        }
        return instance;
    }

    public String getBase64Img() {
        return base64Img;
    }

    public void setBase64Img(String base64Img) {
        this.base64Img = base64Img;
    }

    public String getSecBase64Img() {
        return secBase64Img;
    }

    public void setSecBase64Img(String secBase64Img) {
        this.secBase64Img = secBase64Img;
    }

    public void release() {
        if (instance != null) {
            instance = null;
        }
    }
}
