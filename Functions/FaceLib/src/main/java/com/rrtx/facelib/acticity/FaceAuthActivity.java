package com.rrtx.facelib.acticity;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.baidu.idl.face.platform.FaceConfig;
import com.baidu.idl.face.platform.FaceEnvironment;
import com.baidu.idl.face.platform.FaceSDKManager;
import com.baidu.idl.face.platform.listener.IInitCallback;
import com.gyf.immersionbar.ImmersionBar;
import com.rrtx.facelib.R;
import com.rrtx.facelib.databinding.FaceActivityFaceAuthBinding;
import com.rrtx.facelib.manager.ConsoleConfigManager;
import com.rrtx.facelib.model.Config;
import com.rrtx.facelib.model.ConsoleConfig;
import com.rrtx.facelib.utils.BaseTitleClick;
import com.rrtx.facelib.utils.LogUtil;

import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;

/**
 * 人脸识别开始页面
 *
 * <AUTHOR>
 */
public class FaceAuthActivity extends BaseActivity<FaceActivityFaceAuthBinding> {

    private ConsoleConfig mConsoleConfig;
    private boolean mIsInit = false;

    public static void jumpFaceAuth(Context context) {
        Intent intent = new Intent(context, FaceAuthActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.face_activity_face_auth;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setDaceAuthOpt(new FaceAuthOpt());
    }

    @Override
    public void initView() {
        super.initView();

        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.rootCl.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setText(getString(R.string.identity_info));
    }

    @Override
    public void initData() {
        super.initData();
        //设置人脸参数
        boolean success = setFaceConfig();
        if (!success) {
            return;
        }

        // 为了android和ios 区分授权，appId=appname_face_android ,其中appname为申请sdk时的应用名
        // 应用上下文
        // 申请License取得的APPID
        // assets目录下License文件名
        FaceSDKManager.getInstance().initialize(mContext, Config.licenseID,
                Config.licenseFileName, new IInitCallback() {
                    @Override
                    public void initSuccess() {
                        mIsInit = true;
                    }

                    @Override
                    public void initFailure(final int errCode, final String errMsg) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                LogUtil.e("TAG", "初始化失败 = " + errCode + " " + errMsg);
                            }
                        });
                    }
                });
    }

    /**
     * 参数配置方法
     */
    private boolean setFaceConfig() {
        FaceConfig config = FaceSDKManager.getInstance().getFaceConfig();
        mConsoleConfig = ConsoleConfigManager.getInstance(mContext).getConfig();
        if (mConsoleConfig == null) {
            return false;
        }
        // -----TODO：以下为通过console平台获取到的配置信息-----
        // 设置模糊度阈值
        config.setBlurnessValue(mConsoleConfig.getBlur());
        // 设置光照阈值（范围0-255）
        config.setBrightnessValue(mConsoleConfig.getIllumination());
        // 设置遮挡阈值
        config.setOcclusionValue(mConsoleConfig.getOcclusion());
        // 设置活体动作，通过设置list，LivenessTypeEunm.Eye, LivenessTypeEunm.Mouth,
        // LivenessTypeEunm.HeadUp, LivenessTypeEunm.HeadDown, LivenessTypeEunm.HeadLeft,
        // LivenessTypeEunm.HeadRight, LivenessTypeEunm.HeadLeftOrRight
        config.setLivenessTypeList(mConsoleConfig.getActions());
        // 设置动作活体是否随机
        config.setLivenessRandom(mConsoleConfig.isFaceVerifyRandom());
        // 风控加密类型，0：普通版；1：加密版
        config.setSecType(mConsoleConfig.getSecType());

        // -----TODO：以下不需要通过console平台配置，需要手动修改-----
        // 设置可检测的最小人脸阈值
        config.setMinFaceSize(FaceEnvironment.VALUE_MIN_FACE_SIZE);
        // 设置可检测到人脸的阈值
        config.setNotFaceValue(FaceEnvironment.VALUE_NOT_FACE_THRESHOLD);
        // 设置人脸姿态角阈值
        config.setHeadPitchValue(FaceEnvironment.VALUE_HEAD_PITCH);
        config.setHeadYawValue(FaceEnvironment.VALUE_HEAD_YAW);
        // 设置闭眼阈值
        config.setEyeClosedValue(FaceEnvironment.VALUE_CLOSE_EYES);
        // 设置图片缓存数量（非动作活体使用）
        config.setCacheImageNum(FaceEnvironment.VALUE_CACHE_IMAGE_NUM);
        // 设置口罩判断开关以及口罩阈值
        config.setOpenMask(FaceEnvironment.VALUE_OPEN_MASK);
        config.setMaskValue(FaceEnvironment.VALUE_MASK_THRESHOLD);
        // 设置开启提示音
        config.setSound(true);
        // 原图缩放系数
        config.setScale(FaceEnvironment.VALUE_SCALE);
        // 抠图高的设定，为了保证好的抠图效果，我们要求高宽比是4：3，所以会在内部进行计算，只需要传入高即可
        config.setCropHeight(FaceEnvironment.VALUE_CROP_HEIGHT);
        // 抠图人脸框与背景比例
        config.setEnlargeRatio(FaceEnvironment.VALUE_CROP_ENLARGERATIO);
        // 选择针对人脸采集输出图片的类型进行加密，0：原图，1：抠图
        config.setOutputImageType(FaceEnvironment.VALUE_OUTPUT_IMAGE_TYPE);
        FaceSDKManager.getInstance().setFaceConfig(config);
        return true;
    }

    public class FaceAuthOpt extends BaseTitleClick {

        @Override
        public void leftClick() {
            finish();
        }

        public void startFace() {
            // TODO: 12/28/20 这里应该能拿到姓名和证件号
            if (mIsInit) {
                FaceLivenessExpActivity.jumpFaceLivenessExp(mContext);
            } else {
                //初始化失败
                LogUtil.e("TAG,", "初始化失败!!!");
            }
        }
    }
}