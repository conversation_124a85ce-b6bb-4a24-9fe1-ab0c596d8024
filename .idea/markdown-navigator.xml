<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="FlexmarkProjectSettings">
    <FlexmarkHtmlSettings flexmarkSpecExampleRendering="0" flexmarkSpecExampleRenderHtml="false">
      <flexmarkSectionLanguages>
        <option name="1" value="Markdown" />
        <option name="2" value="HTML" />
        <option name="3" value="flexmark-ast:1" />
      </flexmarkSectionLanguages>
    </FlexmarkHtmlSettings>
  </component>
  <component name="MarkdownProjectSettings">
    <PreviewSettings splitEditorLayout="SPLIT" splitEditorPreview="PREVIEW" useGrayscaleRendering="false" zoomFactor="1.0" maxImageWidth="0" synchronizePreviewPosition="true" highlightPreviewType="LINE" highlightFadeOut="5" highlightOnTyping="true" synchronizeSourcePosition="true" verticallyAlignSourceAndPreviewSyncPosition="true" showSearchHighlightsInPreview="true" showSelectionInPreview="true" lastLayoutSetsDefault="false">
      <PanelProvider>
        <provider providerId="com.vladsch.md.nav.editor.swing.html.panel" providerName="Default - Swing" />
      </PanelProvider>
    </PreviewSettings>
    <ParserSettings gitHubSyntaxChange="false" correctedInvalidSettings="false" emojiShortcuts="1" emojiImages="0">
      <PegdownExtensions>
        <option name="ANCHORLINKS" value="true" />
        <option name="ATXHEADERSPACE" value="true" />
        <option name="FENCED_CODE_BLOCKS" value="true" />
        <option name="INTELLIJ_DUMMY_IDENTIFIER" value="true" />
        <option name="RELAXEDHRULES" value="true" />
        <option name="STRIKETHROUGH" value="true" />
        <option name="TABLES" value="true" />
        <option name="TASKLISTITEMS" value="true" />
      </PegdownExtensions>
      <ParserOptions>
        <option name="COMMONMARK_LISTS" value="true" />
        <option name="EMOJI_SHORTCUTS" value="true" />
        <option name="GFM_TABLE_RENDERING" value="true" />
        <option name="PRODUCTION_SPEC_PARSER" value="true" />
        <option name="SIM_TOC_BLANK_LINE_SPACER" value="true" />
      </ParserOptions>
    </ParserSettings>
    <HtmlSettings headerTopEnabled="false" headerBottomEnabled="false" bodyTopEnabled="false" bodyBottomEnabled="false" addPageHeader="false" imageUriSerials="false" addDocTypeHtml="true" noParaTags="false" plantUmlConversion="0">
      <GeneratorProvider>
        <provider providerId="com.vladsch.md.nav.editor.text.html.generator" providerName="Unmodified HTML Generator" />
      </GeneratorProvider>
      <headerTop />
      <headerBottom />
      <bodyTop />
      <bodyBottom />
    </HtmlSettings>
    <CssSettings previewScheme="UI_SCHEME" cssUri="" isCssUriEnabled="false" isCssUriSerial="true" isCssTextEnabled="false" isDynamicPageWidth="true">
      <StylesheetProvider>
        <provider providerId="com.vladsch.md.nav.editor.text.html.css" providerName="No Stylesheet" />
      </StylesheetProvider>
      <ScriptProviders />
      <cssText />
      <cssUriHistory />
    </CssSettings>
  </component>
</project>