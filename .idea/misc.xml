<project version="4">
  <component name="ASMSmaliIdeaPluginConfiguration">
    <asm skipDebug="true" skipFrames="true" skipCode="false" expandFrames="false" />
    <groovy codeStyle="LEGACY" />
  </component>
  <component name="DesignSurface">
    <option name="filePathToZoomLevelMap">
      <map>
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/drawable/common_drawable_btn_select.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/drawable/common_drawable_btn_unselect.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/drawable/common_drawable_keyboard_bg.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/drawable/common_white_corner_45_bg.xml" value="0.4375" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/drawable/common_white_corner_4_bg.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/activity_currency_list.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_activity_add_bank.xml" value="0.2765625" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_activity_select_bank_card.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_activity_select_currency_account.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_dialog_currency_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_dialog_fragment_bank_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_dialog_fragment_currency_list.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_dialog_fragment_details_bottom.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_fragment_bottom_pay_psd.xml" value="0.4171875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_item_bank_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_item_currency_account.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_item_currency_list.xml" value="0.12" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/common_item_select_bank.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/FunctionCommon/src/main/res/layout/dialog_currency_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/common_white_corner_4_bg.xml" value="0.296875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_btn_updata.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_f5f7f8_corner_8_bg.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_grey_corner_8_bg.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_light_grey_corner_8_bg.xml" value="0.42604166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_loader_bg.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/drawable/drawable_white_round.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/layout/dialog_alone.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/layout/dialog_currency_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/layout/dialog_promotion_picture.xml" value="0.2765625" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/BaseModule/RrtxCommon/src/main/res/layout/item_currency_list.xml" value="0.2765625" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/CashierModel/src/main/res/layout/cashier_activity_cashier.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/CashierModel/src/main/res/layout/cashier_activity_scan_cashier_success.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_account_assets_bg.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_black_border_bg.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_blue_btn.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_btn_select.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_btn_unselect.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_red_bg.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_tab_accoumt_bg.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_tab_bg.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_white_btn.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/drawable/home_drawable_white_round.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/activity_input_pin.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_account_balance_item.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_activity_contact_us.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_activity_home.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_activity_personal_info.xml" value="0.3796875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_activity_pin_input.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_activity_portrait_setting.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_content_view.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_fragment_accrount.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_fragment_home.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_fragment_pending.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/HomeModule/src/main/res/layout/home_left_view.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/border_blue_btn_bg.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/login_checkbox_bg.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/login_drawable_btn_select.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/login_drawable_btn_unselect.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/login_drawable_login_bg.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/drawable/login_drawable_tint_select.xml" value="0.3619791666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/activity_condition.xml" value="0.4234375" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/activity_update_condition.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_activity_login.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_activity_login_success.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_activity_registration.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_activity_splash.xml" value="0.2796875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_activity_temporary.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/LoginModule/src/main/res/layout/login_base_title.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/drawable/payment_drawable_btn_unselect.xml" value="0.32708333333333334" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/drawable/payment_drawable_line.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_aaspilt_bill.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_bar_code.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_be_swept_payment.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_be_swept_unfixed_payment.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_make_payment.xml" value="0.12" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_payment_success.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_receive_payment.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_scan_code.xml" value="0.2" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_select_contacts.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_set_price.xml" value="0.3013513513513513" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_spilt_bill.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_spilt_bill_success.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_activity_split_individually.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_fragment_lssued.xml" value="0.45625" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_fragment_receive.xml" value="0.30833333333333335" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_item_add_amt.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_item_lssued.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_item_receive.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PaymentModule/src/main/res/layout/payment_item_select.xml" value="0.2875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_activity_mer_details.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_activity_mer_list.xml" value="0.2786458333333333" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_dialog_bottom_shop.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_fragment_maps.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_fragment_mer_list.xml" value="0.2765625" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_item_mer_currency.xml" value="0.152" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_item_mer_info.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_item_mer_list.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_item_mer_type.xml" value="0.41302083333333334" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/PromotionModel/src/main/res/layout/promotion_popup_merchant_type.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/sec_border_blue_btn_bg.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/sec_checkbox_bg.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/security_drawable_btn_select.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/security_drawable_btn_unselect.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/security_drawable_select.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/drawable/security_drawable_send_select.xml" value="0.26979166666666665" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/activity_terms.xml" value="0.24947916666666667" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/activity_upd_condition.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/sec_activity_gesture_login.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/sec_activity_gesture_set.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/sec_activity_pwd_verify.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_fingerprint.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_login_finger_lock.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_login_password_lock.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_pin_input.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_pin_payment.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_psd_login.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_activity_security_center.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_fragment_current_device.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/SecurityModule/src/main/res/layout/security_fragment_other_device.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TopUpModule/src/main/res/layout/topup_activity_top_up.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TopUpModule/src/main/res/layout/topup_activity_top_up_success.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/drawable/transfer_drawable_gray_stork_line_bg.xml" value="0.38125" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/drawable/transfer_tab_indicator.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_agent_top_up.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_agent_tup_up.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_history_account.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_history_account_detail.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_history_agent_mobile.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_history_agent_tran.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/activity_moving_account.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/item_header.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/layout.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/tran_account_balance_layout.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_contact_details.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_contact_list.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_history.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_history_details.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_history_payment.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_transaction_detail.xml" value="0.39895833333333336" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_transfer.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_transfer_details.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_transfer_fixed.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_transfer_success.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_withdrawal.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_withdrawal_details.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_activity_withdrawal_type.xml" value="0.12" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_base_title.xml" value="0.****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_dialog_visa_delete.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_item_history_account_balance.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_item_history_list.xml" value="0.1" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_item_history_type.xml" value="0.136" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_item_select_bank.xml" value="0.271875" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_item_withdrawal.xml" value="0.*****************" />
        <entry key="..\:/RRTXProject/Agent/xWallet_android/Functions/TransferModule/src/main/res/layout/transfer_popup_history_type.xml" value="0.****************" />
      </map>
    </option>
  </component>
  <component name="NullableNotNullManager">
    <option name="myDefaultNullable" value="org.jetbrains.annotations.Nullable" />
    <option name="myDefaultNotNull" value="androidx.annotation.NonNull" />
    <option name="myNullables">
      <value>
        <list size="16">
          <item index="0" class="java.lang.String" itemvalue="org.jetbrains.annotations.Nullable" />
          <item index="1" class="java.lang.String" itemvalue="javax.annotation.Nullable" />
          <item index="2" class="java.lang.String" itemvalue="javax.annotation.CheckForNull" />
          <item index="3" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.Nullable" />
          <item index="4" class="java.lang.String" itemvalue="android.support.annotation.Nullable" />
          <item index="5" class="java.lang.String" itemvalue="androidx.annotation.Nullable" />
          <item index="6" class="java.lang.String" itemvalue="android.annotation.Nullable" />
          <item index="7" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNullable" />
          <item index="8" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.Nullable" />
          <item index="9" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableDecl" />
          <item index="10" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableType" />
          <item index="11" class="java.lang.String" itemvalue="com.android.annotations.Nullable" />
          <item index="12" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.Nullable" />
          <item index="13" class="java.lang.String" itemvalue="io.reactivex.annotations.Nullable" />
          <item index="14" class="java.lang.String" itemvalue="io.reactivex.rxjava3.annotations.Nullable" />
          <item index="15" class="java.lang.String" itemvalue="org.jspecify.nullness.Nullable" />
        </list>
      </value>
    </option>
    <option name="myNotNulls">
      <value>
        <list size="16">
          <item index="0" class="java.lang.String" itemvalue="org.jetbrains.annotations.NotNull" />
          <item index="1" class="java.lang.String" itemvalue="javax.annotation.Nonnull" />
          <item index="2" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.NonNull" />
          <item index="3" class="java.lang.String" itemvalue="android.support.annotation.NonNull" />
          <item index="4" class="java.lang.String" itemvalue="androidx.annotation.NonNull" />
          <item index="5" class="java.lang.String" itemvalue="android.annotation.NonNull" />
          <item index="6" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNonNull" />
          <item index="7" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.NonNull" />
          <item index="8" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullDecl" />
          <item index="9" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullType" />
          <item index="10" class="java.lang.String" itemvalue="com.android.annotations.NonNull" />
          <item index="11" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.NonNull" />
          <item index="12" class="java.lang.String" itemvalue="io.reactivex.annotations.NonNull" />
          <item index="13" class="java.lang.String" itemvalue="io.reactivex.rxjava3.annotations.NonNull" />
          <item index="14" class="java.lang.String" itemvalue="lombok.NonNull" />
          <item index="15" class="java.lang.String" itemvalue="org.jspecify.nullness.NonNull" />
        </list>
      </value>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_11" default="true" project-jdk-name="11" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/build/classes" />
  </component>
  <component name="ProjectType">
    <option name="id" value="Android" />
  </component>
</project>