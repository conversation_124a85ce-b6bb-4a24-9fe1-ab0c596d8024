<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="11" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/BaseModule" />
            <option value="$PROJECT_DIR$/BaseModule/FunctionApi" />
            <option value="$PROJECT_DIR$/BaseModule/FunctionCommon" />
            <option value="$PROJECT_DIR$/BaseModule/GoogleMapModule" />
            <option value="$PROJECT_DIR$/BaseModule/RrtxCommon" />
            <option value="$PROJECT_DIR$/Functions" />
            <option value="$PROJECT_DIR$/Functions/CashierModel" />
            <option value="$PROJECT_DIR$/Functions/GPushModule" />
            <option value="$PROJECT_DIR$/Functions/HomeModule" />
            <option value="$PROJECT_DIR$/Functions/LoginModule" />
            <option value="$PROJECT_DIR$/Functions/PaymentModule" />
            <option value="$PROJECT_DIR$/Functions/PromotionModel" />
            <option value="$PROJECT_DIR$/Functions/SecurityModule" />
            <option value="$PROJECT_DIR$/Functions/TopUpModule" />
            <option value="$PROJECT_DIR$/Functions/TransferModule" />
            <option value="$PROJECT_DIR$/Functions/XStoreLib" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/xwalletpro_flutter" />
            <option value="$PROJECT_DIR$/../xwalletpro_flutter/.android/Flutter" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/git/work_order_sdk-4fc2f8f2a68bebce659254f63d435636776288e8/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/device_info-2.0.3/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.7/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-4.2.1/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.0.8/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.5+5/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/package_info-2.0.2/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.0.15/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/video_player_android-2.3.10/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/wakelock-0.6.2/android" />
            <option value="$PROJECT_DIR$/../../../fvm-flutter-sdk/versions/2.10.5-2.16.2/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_android-2.8.14/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>