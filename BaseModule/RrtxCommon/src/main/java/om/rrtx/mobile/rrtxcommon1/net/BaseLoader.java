package om.rrtx.mobile.rrtxcommon1.net;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2018/11/5 23:22
 * 描述 : 所有RxJava的上游
 */
public class BaseLoader {
    public String TAG = BaseLoader.class.getSimpleName();

    /**
     * @param observable 上游
     * @param <T>        相应的数据类型
     * @return Observable对象
     */
    protected static <T> Observable<T> observe(Observable<T> observable) {
        return observable
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }
}