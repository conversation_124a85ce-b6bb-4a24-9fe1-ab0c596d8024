package om.rrtx.mobile.rrtxcommon1.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import om.rrtx.mobile.rrtxcommon1.R;


/**
 * <AUTHOR>
 * 输入Pin的View
 */
public class RxKeyPadView extends LinearLayout implements View.OnClickListener {
    /**
     * 显示的Code
     */
    private String mCode = "";
    private KeyPadCallBack mKeyPadCallBack;
    private ConstraintLayout keyTitle;

    public RxKeyPadView(Context context) {
        this(context, null);
    }

    public RxKeyPadView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RxKeyPadView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //这里初始化的应该是密码框和键盘
        View rootView = inflate(context, R.layout.common_view_key_pad, this);
        ConstraintLayout keyBoardView = rootView.findViewById(R.id.keyboard);
        keyTitle = rootView.findViewById(R.id.keyTitle);
        //设置底部按钮的监听
        setItemClickListener(keyBoardView);
    }

    /**
     * 给每一个自定义数字键盘上的View 设置点击事件
     *
     * @param view 相应的父View
     */
    private void setItemClickListener(View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                //不断的给里面所有的View设置setOnClickListener
                View childView = ((ViewGroup) view).getChildAt(i);
                setItemClickListener(childView);
            }
        } else {
            view.setOnClickListener(this);
        }
    }

    @Override
    public void onClick(View v) {
        if (v instanceof TextView) {
            String number = ((TextView) v).getText().toString().trim();
            if (mCode.length() < 6) {
                mCode += number;
            }
        }
        if (v instanceof ImageView) {
            if (TextUtils.isEmpty(mCode)) {
                return;
            }
            mCode = mCode.substring(0, mCode.length() - 1);
        }

        if (mKeyPadCallBack != null) {
            mKeyPadCallBack.inputCode(mCode);
        }
    }

    public void setKeyTitle(String isTitle)
    {
        if (TextUtils.isEmpty(isTitle))
        {
            keyTitle.setVisibility(VISIBLE);
        }else{
            keyTitle.setVisibility(GONE);
        }
    }

    /**
     * 设置相应的回调
     *
     * @param keyPadCallBack KeyPadView回调
     */
    public void setKeyPadCallBack(KeyPadCallBack keyPadCallBack) {
        mKeyPadCallBack = keyPadCallBack;
    }

    public interface KeyPadCallBack {
        /**
         * 文字填满的时候回调
         *
         * @param code 输入的内容
         */
        void inputCode(String code);
    }

}

