package om.rrtx.mobile.rrtxcommon1.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import androidx.constraintlayout.widget.ConstraintLayout

open class CommonBottomDialog(context: Context) : Dialog(context) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCancelable(false)
    }
    override fun show() {
        super.show()
        setLocation()
    }

    open fun setLocation() {
        val window = window
        if (window != null) {
            window.decorView.setPadding(0, 0, 0, 0)
            // 给 DecorView 设置背景颜色，很重要，不然导致 Dialog 内容显示不全，有一部分内容会充当 padding
            window.decorView.setBackgroundColor(Color.WHITE)
            window.setGravity(Gravity.BOTTOM)
            val WL = window.attributes
            WL.height = ConstraintLayout.LayoutParams.WRAP_CONTENT
            WL.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            window.attributes = WL
        }
    }
}