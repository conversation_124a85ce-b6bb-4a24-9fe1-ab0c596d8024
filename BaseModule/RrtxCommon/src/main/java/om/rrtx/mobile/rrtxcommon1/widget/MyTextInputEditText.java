package om.rrtx.mobile.rrtxcommon1.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuItem;

import com.google.android.material.textfield.TextInputEditText;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/4/17 17:30
 * 描述 :
 */
public class MyTextInputEditText extends TextInputEditText {
    public MyTextInputEditText(Context context) {
        super(context);

        setLongClickable(false);
        setTextIsSelectable(false);
    }

    public MyTextInputEditText(Context context, AttributeSet attrs) {
        super(context, attrs);

        setLongClickable(false);
        setTextIsSelectable(false);
    }

    public MyTextInputEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setLongClickable(false);
        setTextIsSelectable(false);
    }

    @Override
    protected void onSelectionChanged(int selStart, int selEnd) {
        CharSequence text = getText();
        if (text != null) {
            if (selStart != text.length() || selEnd != text.length()) {
                setSelection(text.length(), text.length());
                return;
            }
        }
        super.onSelectionChanged(selStart, selEnd);
    }
}
