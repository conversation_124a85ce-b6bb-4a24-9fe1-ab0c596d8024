package om.rrtx.mobile.rrtxcommon1.base;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.ViewModelProviders;

import om.rrtx.mobile.rrtxcommon1.utils.ClassUtil;


/**
 * 作者 : 贺金龙
 * 创建时间 :  2018/1/12 10:28
 * 类描述 :  带有懒加载的BaseFragment
 * 修改人 :
 * 修改内容 :
 * 修改时间 :
 * 类说明 : 使Fragment实现懒加载
 */
public abstract class BaseFragment<VM extends AndroidViewModel, DB extends ViewDataBinding>
        extends Fragment {

    public Context mContext;
    protected VM viewModel;
    protected DB dataBinding;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = getContext();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        dataBinding = DataBindingUtil.inflate(inflater,createViewLayoutId(),container,false);
        /*这里是使用相应的LiveData*/
        Class<VM> viewModelClass = ClassUtil.getViewModel(this);
        if (viewModelClass != null) {
            viewModel = ViewModelProviders.of(getActivity()).get(viewModelClass);
        }
        initView();
        return dataBinding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initDate();
        initListener();
    }

    //----------------------------------------------可去进行复写的方法----------------------------------------------//

    /**
     * author :  贺金龙
     * create time : 2018/1/12 10:55
     * description : 初始化数据/请求数据的相应方法
     * instructions : 调用这个方法要和相应的懒加载的数据区分开
     * 这个方法的调用的时机是可能不会刷新的数据的获取
     * version : 2.0.1
     */
    public void initDate() {
    }

    /**
     * author :  贺金龙
     * create time : 2018/1/12 14:23
     * description : 设置相应的监听
     * instructions :
     * version : 2.0.1
     */
    public void initListener() {
    }

    //----------------------------------------------可去进行复写的方法----------------------------------------------//

    //----------------------------------------------对外的抽象方法----------------------------------------------//


    /**
     * author :  贺金龙
     * create time : 2018/1/12 10:40
     * description : 设置相应View的方法
     * instructions :
     * version : 2.0.1
     */
    protected abstract int createViewLayoutId();

    /**
     * author :  贺金龙
     * create time : 2018/1/12 10:41
     * description : 初始化参数的相应方法
     * instructions :
     * version : 2.0.1
     */
    protected abstract void initView();

    //----------------------------------------------对外的抽象方法----------------------------------------------//

    /**
     * author :  贺金龙
     * create time : 2018/1/12 10:36
     * description : 判断Fragment的状态
     * instructions :
     * version :
     */
    private boolean getStatus() {
        return (isAdded() && !isRemoving());
    }

    //----------------------------------------------相应继承的方法----------------------------------------------//

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
