package om.rrtx.mobile.rrtxcommon1.bean;

import java.util.Map;

public  class FeedbackRouteBean {
    private String baseUrl;
    private String routeName;

    private String platform;

    private String language;

    private Map uiStyle;

    private Map params;

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Map getUiStyle() {
        return uiStyle;
    }

    public void setUiStyle(Map uiStyle) {
        this.uiStyle = uiStyle;
    }

    public Map getParams() {
        return params;
    }

    public void setParams(Map params) {
        this.params = params;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    @Override
    public String toString() {
        return "FeedbackRouteBean{" +
                "baseUrl='" + baseUrl + '\'' +
                ", routeName='" + routeName + '\'' +
                ", platform='" + platform + '\'' +
                ", language='" + language + '\'' +
                ", uiStyle=" + uiStyle +
                ", params=" + params +
                '}';
    }
}