package om.rrtx.mobile.rrtxcommon1.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import om.rrtx.mobile.rrtxcommon1.R;


/**
 * <AUTHOR>
 */
public class UpDataDialog extends Dialog {

    private ConstraintLayout mRootCl;
    private ClickCallBack mClickCallBack;

    public UpDataDialog(@NonNull Context context) {
        super(context, R.style.transparentDialogDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_updata);

        setCancelable(false);
        setCanceledOnTouchOutside(false);

        mRootCl = findViewById(R.id.rootCl);

        initListener();
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }


    private void initListener() {
        mRootCl.setOnClickListener(view -> {
            if (mClickCallBack != null) {
                mClickCallBack.rootClick();
            }
        });
    }


    public void setClickCallBack(ClickCallBack clickCallBack) {
        mClickCallBack = clickCallBack;
    }

    public interface ClickCallBack {
        /**
         * 点击事件
         */
        void rootClick();
    }
}
