package om.rrtx.mobile.rrtxcommon1.bean;

import android.os.Parcel;
import android.os.Parcelable;

import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * PayCheckBean
 *
 * <AUTHOR>
 */
public class CodeOrderBean implements Parcelable {
    /**
     * scanStatus : 0
     * merNo :
     * merName :
     * checkstandNo :
     * amt :
     * isPaid :
     */

    private String scanStatus;
    private String merNo;
    private String merName;
    private String cashierName;
    private String checkstandNo;
    private String amt;
    private String isPaid;
    private String payToken;
    private String currency;

    /**
     * 判断类型
     */
    private String source = "";
    /**
     * 订单类型
     * 0-内部 1-外部
     */
    private String orderSource;
    /**
     * 支付的token
     */
    private String transOrderNo;
    /**
     * 地址
     */
    private String address;
    /**
     * 是否是外部跳转
     */
    private String isOutJump;

    private String orderNo;
    private String orderStatus;
    private String commodityRemark;
    private String merTradeName;
    private String qrCode;


    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public String getCashierName() {
        return cashierName;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getOrderSource() {
        if (!StringUtils.isValidString(orderStatus)) return source;
        return orderSource;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTransOrderNo() {
        return transOrderNo;
    }

    public void setTransOrderNo(String transOrderNo) {
        this.transOrderNo = transOrderNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIsOutJump() {
        return isOutJump;
    }

    public void setIsOutJump(String isOutJump) {
        this.isOutJump = isOutJump;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCommodityRemark() {
        return commodityRemark;
    }

    public void setCommodityRemark(String commodityRemark) {
        this.commodityRemark = commodityRemark;
    }

    public String getMerTradeName() {
        return merTradeName;
    }

    public void setMerTradeName(String merTradeName) {
        this.merTradeName = merTradeName;
    }

    public String getScanStatus() {
        return scanStatus;
    }

    public void setScanStatus(String scanStatus) {
        this.scanStatus = scanStatus;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getCheckstandNo() {
        return checkstandNo;
    }

    public void setCheckstandNo(String checkstandNo) {
        this.checkstandNo = checkstandNo;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getIsPaid() {
        return isPaid;
    }

    public void setIsPaid(String isPaid) {
        this.isPaid = isPaid;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }



    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.scanStatus);
        dest.writeString(this.merNo);
        dest.writeString(this.merName);
        dest.writeString(this.checkstandNo);
        dest.writeString(this.amt);
        dest.writeString(this.isPaid);
        dest.writeString(this.payToken);
        dest.writeString(this.currency);
    }

    public CodeOrderBean() {
    }

    protected CodeOrderBean(Parcel in) {
        this.scanStatus = in.readString();
        this.merNo = in.readString();
        this.merName = in.readString();
        this.checkstandNo = in.readString();
        this.amt = in.readString();
        this.isPaid = in.readString();
        this.payToken = in.readString();
        this.currency = in.readString();
    }

    public static final Creator<CodeOrderBean> CREATOR = new Creator<CodeOrderBean>() {
        @Override
        public CodeOrderBean createFromParcel(Parcel source) {
            return new CodeOrderBean(source);
        }

        @Override
        public CodeOrderBean[] newArray(int size) {
            return new CodeOrderBean[size];
        }
    };
}
