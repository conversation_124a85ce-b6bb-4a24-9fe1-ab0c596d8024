package om.rrtx.mobile.rrtxcommon1.bean

data class UpgradeInfoBean(
    /**
     * 10 身份证 ，20 护照
     */
    var idType: String = "",
    var idNumber: String = "",
    var cityCode: String = "",
    var address: String = "",
    var email: String = "",

    var mobile: String = "",

    var pin: String = "",
    var juniorUserId: String = "",

    var conditionVersionNo: String = "",
    var conditionTitle: String = "",
    var conditionType: String = ""

) {
    fun verify(): Boolean {
        if (idType == "") return false
        if (idNumber == "") return false
        if (cityCode == "") return false
        if (address == "") return false
//        if (email == "*") return false
        return true
    }

    override fun toString(): String {
        return "UpgradeInfoBean(idType='$idType', idNumber='$idNumber', cityCode='$cityCode', address='$address', email='$email', mobile='$mobile', pin='$pin', juniorUserId='$juniorUserId', conditionVersionNo='$conditionVersionNo', conditionTitle='$conditionTitle', conditionType='$conditionType')"
    }


}
