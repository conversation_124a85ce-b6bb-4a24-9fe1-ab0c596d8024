package om.rrtx.mobile.rrtxcommon1.utils

import android.graphics.drawable.GradientDrawable
import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.IdRes
import androidx.databinding.BindingAdapter
import om.rrtx.mobile.rrtxcommon1.R
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px

class ExtendBindingAdapter {

    /**
     * view 添加圆角背景
     * 注!： 1、注意 @ColorInt  ，no @ColorRes int color
     * 2、一般不建议使用，使用shape方便复用
     *
     * @param radius 角度
     * @param color  背景颜色
     */
    companion object {
        @BindingAdapter(
            value = arrayOf("cornerBackgroundRadius", "cornerBackgroundColor"),
            requireAll = false
        )
        @JvmStatic
        fun setCornerBackground(view: View, radius: Int, @IdRes color: Int) {
            val gradientDrawable = GradientDrawable()
            // 圆角
            val v: Int = radius.pt2px()
            gradientDrawable.cornerRadius = v.toFloat()
            // 颜色
            gradientDrawable.setColor(ResourceHelper.getColor(if (color == 0) R.color.color_FFFFFF else color))
            view.background = gradientDrawable
        }
    }
}