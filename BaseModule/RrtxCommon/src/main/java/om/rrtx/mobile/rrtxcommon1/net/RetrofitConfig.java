package om.rrtx.mobile.rrtxcommon1.net;

import java.io.InputStream;
import java.io.Serializable;

import okhttp3.CookieJar;
import okhttp3.Interceptor;

/**
 * 提供网络请求配置的信息
 * 这里配置的时间单位为秒
 */
public class RetrofitConfig implements Serializable {
    /**
     * 读取时间
     */
    private int readTime;
    /**
     * 超时时间
     */
    private int writeTime;
    /**
     * 连接超时
     */
    private int connectTime;
    /**
     * 配置的数组
     */
    private Interceptor[] interceptor;
    /**
     * 配置相应cookieJar
     */
    private CookieJar cookieJar;
    /**
     * 是否失败重连
     * retryOnConnectionFailure
     */
    private boolean retry;
    /**
     * 证书路径
     */
    private InputStream[] certificateIs;
    /**
     * 请求路径
     */
    private String url;
    /**
     * 是否返回Gson否则直接返回String直接解析
     * 模式使用Gson
     */
    private boolean isGson;


    public int getReadTime() {
        return readTime;
    }

    public void setReadTime(int readTime) {
        this.readTime = readTime;
    }

    public int getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(int writeTime) {
        this.writeTime = writeTime;
    }

    public int getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(int connectTime) {
        this.connectTime = connectTime;
    }

    public Interceptor[] getInterceptor() {
        return interceptor;
    }

    public void setInterceptor(Interceptor[] interceptor) {
        this.interceptor = interceptor;
    }

    public CookieJar getCookieJar() {
        return cookieJar;
    }

    public void setCookieJar(CookieJar cookieJar) {
        cookieJar = cookieJar;
    }

    public boolean isRetry() {
        return retry;
    }

    public void setRetry(boolean retry) {
        this.retry = retry;
    }

    public InputStream[] getCertificatePath() {
        return certificateIs;
    }

    public void setCertificatePath(InputStream... certificateIs) {
        this.certificateIs = certificateIs;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isGson() {
        return isGson;
    }

    public void setGson(boolean gson) {
        isGson = gson;
    }

    public RetrofitConfig(Builder builder) {
        readTime = builder.readTime;
        writeTime = builder.writeTime;
        connectTime = builder.connectTime;
        interceptor = builder.interceptor;
        cookieJar = builder.cookieJar;
        retry = builder.retry;
        certificateIs = builder.certificateIs;
        url = builder.url;
        isGson = builder.isGson;
    }

    public static class Builder {
        /**
         * 读取时间
         */
        private int readTime;
        /**
         * 超时时间
         */
        private int writeTime;
        /**
         * 连接超时
         */
        private int connectTime;
        /**
         * 配置的数组
         */
        private Interceptor[] interceptor;
        /**
         * 配置相应cookieJar
         */
        private CookieJar cookieJar;
        /**
         * 是否失败重连
         * retryOnConnectionFailure
         */
        private boolean retry;
        /**
         * 证书路径
         */
        private InputStream[] certificateIs;
        /**
         * 请求路径
         */
        private String url;
        /**
         * 是否返回Gson否则直接返回String直接解析
         * 模式使用Gson
         */
        private boolean isGson;

        public Builder setReadTime(int readTime) {
            this.readTime = readTime;
            return this;
        }

        public Builder setWriteTime(int writeTime) {
            this.writeTime = writeTime;
            return this;
        }

        public Builder setConnectTime(int connectTime) {
            this.connectTime = connectTime;
            return this;
        }

        public Builder setInterceptor(Interceptor[] interceptor) {
            this.interceptor = interceptor;
            return this;
        }

        public Builder setCookieJar(CookieJar cookieJar) {
            this.cookieJar = cookieJar;
            return this;
        }

        public Builder setRetry(boolean retry) {
            this.retry = retry;
            return this;
        }

        public Builder setCertificateIs(InputStream... certificateIs) {
            this.certificateIs = certificateIs;
            return this;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setGson(boolean gson) {
            isGson = gson;
            return this;
        }

        public RetrofitConfig builder() {
            return new RetrofitConfig(this);
        }
    }
}
