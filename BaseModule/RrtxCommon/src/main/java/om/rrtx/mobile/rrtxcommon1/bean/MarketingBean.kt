package om.rrtx.mobile.rrtxcommon1.bean

import com.kapp.xmarketing.bean.CouponBean
import com.kapp.xmarketing.bean.CouponBean.RightsBean
import com.kapp.xmarketing.bean.UseCouponsBean
import com.kapp.xmarketing.bean.UsePointsBean

data class MarketingBean(
    val canNotUseCampaign: String,  // 不可用活动数量
    val cashBackAccountAmount: String,
    val currency: String,
    val merAmount: String,
    var orderAmt: String,           // 交易金额，业务发起金额
    var paymentAmt: String,         //  优惠抵扣后的金额
    var points: CouponBean.PointsDetail,             // 积分信息
    var coupon: CouponBean.Coupon,             // 优惠卷
    var rights: RightsBean              // 会员权益信息
)

//data class CouponsBean(
//    val amount: String,             // 抵扣总金额
//    val share: String,              // 是否和活动共享 0-不共享 1-共享
//    val useList: List<CouponBean>          // 已使用的列表
//)

//data class Coupon(
//    val amount: String,
//    val share: String,
//    val useList: List<Use>
//)
//
data class Points(
    val amount: String,
    val pointsDetail: List<PointsDetail>
)
//
//data class Rights(
//    val amount: String,
//    val share: String,
//    val useList: List<Any>
//)

data class Use(
    val currency: String,
    val deduction: String,
    val discountAmount: String,
    val no: String
)

data class PointsDetail(
    val cardName: String,
    val consumptionPoints: String,
    val cstId: String,
    val cstName: String,
    val cstType: String,
    val currency: String,
    val deductionAmount: String,
    val pointExchangeRulesAmount: String,
    val pointExchangeRulesPoints: String,
    val pointExchangeThresholdLimit: String,
    val pointExchangeThresholdPoints: String,
    val pointId: String,
    val selected: String,
    val totalPoints: String
)

data class RequestUseMarketingBean(
    val payToken: String,
    val trxOrderNo: String,
    val trxTransType: String,
    val useCoupons: ArrayList<UseCouponsBean>,
    val usePoints: ArrayList<UsePointsBean>
)

