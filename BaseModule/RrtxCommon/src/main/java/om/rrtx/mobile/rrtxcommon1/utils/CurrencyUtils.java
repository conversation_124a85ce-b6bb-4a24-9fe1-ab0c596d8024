package om.rrtx.mobile.rrtxcommon1.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;

/**
 * <AUTHOR>
 * 币种工具类,这里主要是想把币种做到拆分
 */
public class CurrencyUtils {

    /**
     * 设置币种,逻辑是这样的,传入币种如果为空就直接使用默认的
     */
    public static String setCurrency(Context context, String currencyStr) {
        String currency = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.CURCURRENCY,"ZWG");
        if (TextUtils.isEmpty(currency)){
            currency = "ZWG";
        }
        return TextUtils.isEmpty(currencyStr) ? currency : currencyStr;
    }
}
