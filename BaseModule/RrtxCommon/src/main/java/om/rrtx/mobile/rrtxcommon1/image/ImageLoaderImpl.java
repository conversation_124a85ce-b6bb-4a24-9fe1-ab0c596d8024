package om.rrtx.mobile.rrtxcommon1.image;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.request.RequestOptions;

/**
 * 图片加载的接口
 */
public interface ImageLoaderImpl {

    /**
     * 加载图片
     *
     * @param imageView imageView
     * @param url       图片地址,可以是各种图片地址(适合Glide)
     * @param failUrl   无法默认图片时的替代图片
     * @param context   上下文
     */
    void disPlayImage(Context context, Object url, int failUrl, ImageView imageView);
}
