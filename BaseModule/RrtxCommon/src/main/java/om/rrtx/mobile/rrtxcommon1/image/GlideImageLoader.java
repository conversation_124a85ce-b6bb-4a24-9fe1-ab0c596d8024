package om.rrtx.mobile.rrtxcommon1.image;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;

import om.rrtx.mobile.rrtxcommon1.R;

/**
 * 使用Glide加载图片
 */
public class GlideImageLoader implements ImageLoaderImpl {
    @Override
    public void disPlayImage(Context context, Object url, int failUrl, ImageView imageView) {
//        Glide.with(context)
//                .load(url)
//                //禁止使用内存缓存
////                .skipMemoryCache(true)
////                .priority(Priority.HIGH)
////                .centerCrop()
//                .skipMemoryCache(true) // 不使用内存缓存
//                .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用磁盘缓存
//                .override(imageView.getWidth(),imageView.getHeight())
//                //加载成功前显示的图片
//                .placeholder(failUrl)
//                //加载失败显示的图片
//                .fallback(failUrl)
//                .into(imageView);
        Glide.with(context)
                .load(url)
                //加载成功前显示的图片
                .placeholder(failUrl)
                //加载失败显示的图片
                .fallback(failUrl)
                // todo 同一天内上传图片后台不会更新路径和name，导致无法刷新，默认不使用缓存
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(imageView);

    }
}
