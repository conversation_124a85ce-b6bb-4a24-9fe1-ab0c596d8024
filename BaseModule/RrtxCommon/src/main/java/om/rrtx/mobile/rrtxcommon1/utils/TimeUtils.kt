package om.rrtx.mobile.rrtxcommon1.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class TimeUtils {
    companion object {

        private const val formatMY = "MM-yyyy"
        private const val formatDMY = "dd-MM-yyyy"
        private const val formatAll = "dd-MM-yyyy HH:mm:ss"

        private val timeFormatHM = SimpleDateFormat("dd-MM-yyyy HH:mm", Locale.ENGLISH)
        private val timeFormatHMS = SimpleDateFormat("dd-MM-yyyy HH:mm:ss", Locale.ENGLISH)
        private val stanTimeFormatHM = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.ENGLISH)
        private val stanTimeFormatHMS = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)

        @JvmStatic
        fun date2StringDMY(date: Date?): String? {
            val simpleDateFormat = SimpleDateFormat(formatDMY)
            return simpleDateFormat.format(date)
        }

        @JvmStatic
        fun date2StringMY(date: Date?): String? {
            val simpleDateFormat = SimpleDateFormat(formatMY)
            return simpleDateFormat.format(date)
        }

        @JvmStatic
        fun longFormat(time: Long): String? {
            val simpleDateFormat = SimpleDateFormat(formatMY, Locale.ENGLISH)
            return simpleDateFormat.format(time)
        }

        @JvmStatic
        fun long2StringAll(time: Long): String? {
            val simpleDateFormat = SimpleDateFormat(formatAll)
            return simpleDateFormat.format(time)
        }

        @JvmStatic
        fun strToDateLong(strDate: String?, parseFormat: String?): String? {
            val formatter = SimpleDateFormat(parseFormat)
            val simpleDateFormat = SimpleDateFormat(formatAll)
            return simpleDateFormat.format(formatter.parse(strDate))
        }

        /**
         * 前后台约定不处理,处理的时候统一处理
         */
        @JvmStatic
        fun getTime(time: String): String {
            return time
            return try {
                return timeFormatHMS.format(stanTimeFormatHMS.parse(time))
            } catch (e: Exception) {
                getTimeHm(time)
            }
            //val formatter = SimpleDateFormat("dd/MM/yyyy HH:mm")
            //val simpleDateFormat = SimpleDateFormat("dd-MM-yyyy HH:mm")
            //return simpleDateFormat.format(formatter.parse(time))
        }

        @JvmStatic
        fun getTimeHm(time: String): String {
            try {
                return timeFormatHM.format(stanTimeFormatHM.parse(time))
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return ""
        }

    }
}