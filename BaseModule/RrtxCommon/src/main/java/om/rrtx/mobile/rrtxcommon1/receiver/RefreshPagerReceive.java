package om.rrtx.mobile.rrtxcommon1.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;

/**
 * 接收的广播
 */
public class RefreshPagerReceive extends BroadcastReceiver {

    public RefreshPagerCallback mCallback;

    public RefreshPagerReceive(RefreshPagerCallback callback) {
        mCallback = callback;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        //这里处理接收的信息
        if (intent != null) {
            if (TextUtils.equals(intent.getAction(), BaseConstants.REFRESHPAGERACTION)) {
                if (mCallback != null) {
                    mCallback.refreshPager(intent);
                }
            }
        }
    }


    /**
     * <AUTHOR>
     * 刷新页面的回调
     */
    public interface RefreshPagerCallback {
        void refreshPager(Intent intent);
    }
}