package om.rrtx.mobile.rrtxcommon1.dialog;

import android.content.Context
import android.content.res.Resources
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker.OnDateChangedListener
import android.widget.LinearLayout
import android.widget.NumberPicker
import kotlinx.android.synthetic.main.dialog_date_select.dp_date
import kotlinx.android.synthetic.main.dialog_date_select.tv_cancel
import kotlinx.android.synthetic.main.dialog_date_select.tv_confirm
import kotlinx.android.synthetic.main.dialog_date_select.tv_title
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.R
import java.util.Calendar
import java.util.Date


class DateSelectDialog(context: Context, val flag: String,val dialogResult: DialogResult) :
    CommonBottomDialog(context) {

    var year: Int = 0
    var monthOfYear: Int = 0
    var dayOfMonth: Int = 0

    /**
     * 月份的取值
     */
    private val mDisplayMonths =
        arrayOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_date_select)
        initView()
        initDpView()
    }

    private fun initDpView() {

        // 可以在任何地方进行使用，但只能获取系统本身的资源。
        val resources = Resources.getSystem()
        // 获取布局LinearLayout
        val mSpinners =
            findViewById<View>(resources.getIdentifier("pickers", "id", "android")) as LinearLayout

        if (mSpinners != null) {

            // 获取年月日numberpicker
            val yearPicker: View = dp_date.findViewById<View>(
                resources.getIdentifier(
                    "year",
                    "id",
                    "android"
                )
            ) as NumberPicker
            val monthPicker: View = dp_date.findViewById<View>(
                resources.getIdentifier(
                    "month",
                    "id",
                    "android"
                )
            ) as NumberPicker
            val dayPicker: View = dp_date.findViewById<View>(
                resources.getIdentifier(
                    "day",
                    "id",
                    "android"
                )
            ) as NumberPicker

            /*重新排列datepicker年月日的顺序*/
            mSpinners.removeAllViews()
            mSpinners.addView(dayPicker)
            mSpinners.addView(monthPicker)
            mSpinners.addView(yearPicker)
        }

        val calendar = Calendar.getInstance()
        year = calendar[Calendar.YEAR]
        monthOfYear = calendar[Calendar.MONTH]
        dayOfMonth = calendar[Calendar.DAY_OF_MONTH] - 1
        dp_date.init(year, monthOfYear, dayOfMonth, onSelectDate)
        year = 2024
        if (BaseConstants.JumpFlag.Register_junior_Account==flag)
        {
            // 9-15岁
            dp_date.maxDate = Date(year - 9 - 1900, monthOfYear, dayOfMonth).time
            dp_date.minDate = Date(year - 15 - 1900 - 1, monthOfYear, dayOfMonth + 1).time
        }else{
            // 16-120岁
            dp_date.maxDate = Date(year - 16 - 1900, monthOfYear, dayOfMonth).time
            dp_date.minDate = Date(year - 120 - 1900 - 1, monthOfYear, dayOfMonth + 1).time
        }
        // 默认选中时间为最大
        year -= (if (BaseConstants.JumpFlag.Register_junior_Account==flag) 9 else 16)
        // 月份从 0 开始，所以默认加 1
        monthOfYear += 1

        //关键行
        (((dp_date.getChildAt(0) as ViewGroup).getChildAt(0) as ViewGroup).getChildAt(1) as NumberPicker).displayedValues =
            mDisplayMonths
    }

    fun initView() {
        tv_cancel.setOnClickListener {
            dismiss()
        }
        tv_confirm.setOnClickListener {
            dialogResult.onConfirm(year, monthOfYear, dayOfMonth)
            dismiss()
        }
    }

    /**
     * todo有时间抽一下
     */
    fun setDateSpace() {
        val calendar = Calendar.getInstance()
        year = calendar[Calendar.YEAR]
        monthOfYear = calendar[Calendar.MONTH]
        dayOfMonth = calendar[Calendar.DAY_OF_MONTH]
        dp_date.init(year, monthOfYear, dayOfMonth, onSelectDate)
        dp_date.maxDate = Date(year - 1900, monthOfYear, dayOfMonth).time
        dp_date.minDate = Date(year - 1900, monthOfYear - 2, dayOfMonth).time

        // 默认选中时间为最大
        // 月份从 0 开始，所以默认加 1
        monthOfYear += 1

        (((dp_date.getChildAt(0) as ViewGroup).getChildAt(0) as ViewGroup).getChildAt(1) as NumberPicker).displayedValues =
            mDisplayMonths
    }

    private val onSelectDate = OnDateChangedListener { datePicker, i, i1, i2 ->
        year = i
        monthOfYear = i1 + 1
        dayOfMonth = i2
        //关键行
        (((datePicker.getChildAt(0) as ViewGroup).getChildAt(0) as ViewGroup).getChildAt(1) as NumberPicker).displayedValues =
            mDisplayMonths
    }

    fun setTitle(s: String) {
        tv_title.text = s
    }

    interface DialogResult {
        fun onConfirm(year: Int, monthO: Int, date: Int)
    }

}
