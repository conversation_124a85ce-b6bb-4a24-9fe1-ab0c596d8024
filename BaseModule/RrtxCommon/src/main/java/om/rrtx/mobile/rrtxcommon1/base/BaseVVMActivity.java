package om.rrtx.mobile.rrtxcommon1.base;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;

import cn.bingoogolapple.swipebacklayout.BGASwipeBackHelper;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.receiver.RefreshPagerReceive;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils;
import om.rrtx.mobile.rrtxcommon1.utils.ClassUtil;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;


/**
 * <AUTHOR>
 * 类描述: 所有Activity的基类
 * 这里封装了相应的DataBading
 */

/**
 * <AUTHOR>
 * 类描述: 所有Activity的基类
 * 这里封装了相应的DataBading
 */
public abstract class BaseVVMActivity<VM extends AndroidViewModel, DB extends ViewDataBinding> extends BaseActivity<DB> {

    protected VM viewModel;

    @Override
    protected void initWorkspaceAction() {
        Class<VM> viewModelClass = ClassUtil.getViewModel(this);
        if (viewModelClass != null) {
            viewModel = new ViewModelProvider(this, new ViewModelProvider.AndroidViewModelFactory(getApplication())).get(viewModelClass);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initClickListener();
        initRefreshListener();
        initVMListener();
    }

    protected void initClickListener(){}
    protected void initRefreshListener(){}

    protected void initVMListener(){}
}
