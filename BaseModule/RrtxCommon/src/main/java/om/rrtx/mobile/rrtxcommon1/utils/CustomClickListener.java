package om.rrtx.mobile.rrtxcommon1.utils;

import android.util.Log;
import android.view.View;

/**
 * 防止多次点击的监听
 */
public abstract class CustomClickListener implements View.OnClickListener {
    private long mLastClickTime;
    private long timeInterval = 500L;

    public CustomClickListener() {

    }

    public CustomClickListener(long interval) {
        this.timeInterval = interval;
    }

    @Override
    public void onClick(View v) {
        long nowTime = System.currentTimeMillis();
        long l = nowTime - mLastClickTime;
        boolean b = l > timeInterval;
//        Log.e("onSingleClick", timeInterval + "//// 间隔：" + l + ",响应单击：" + b);
        if (b) {
            // 单次点击事件
            onSingleClick(v);
            mLastClickTime = nowTime;
        } else {
            // 快速点击事件
            onFastClick(v);
        }
    }

    /**
     * 单次点击事件
     *
     * @param view 点击的View
     */
    public abstract void onSingleClick(View view);

    /**
     * 快速点击事件
     *
     * @param view 点击的View
     */
    public void onFastClick(View view) {

    }
}