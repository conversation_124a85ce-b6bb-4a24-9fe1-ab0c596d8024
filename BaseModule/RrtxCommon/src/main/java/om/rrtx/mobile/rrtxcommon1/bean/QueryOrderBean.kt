package om.rrtx.mobile.rrtxcommon1.bean

import om.rrtx.mobile.rrtxcommon1.utils.StringUtils

data class QueryOrderBean @JvmOverloads constructor(
    var payToken: String,
    var orderNo: String = "",
    /**
     * 订单类型
     * 0-内部 1-外部
     */
    var orderSource: String = "",
    /**
     * 简单一点，有packName就开启aidl服务
     */
    var packName: String = "",

    // 创建 付款订单
    var merNo: String = "",
    var merName: String = "",
    var checkstandNo: String = "",
    var amt: String = "",
    var qrCode: String = "",
    var currency: String = "",
    var paymentProduct: String = "",
) {



    fun isExtAppJump(): Boolean = StringUtils.isValidString(packName)
}