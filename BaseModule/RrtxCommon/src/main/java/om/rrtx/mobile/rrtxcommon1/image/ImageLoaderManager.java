package om.rrtx.mobile.rrtxcommon1.image;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.request.RequestOptions;

/**
 * 控制图片加载的类
 * 因为这里怕更换图片框架,所以做成单例和策略模式
 */
public class ImageLoaderManager implements ImageLoaderImpl {

    private ImageLoaderImpl mImageLoader;

    private ImageLoaderManager() {
    }

    private static ImageLoaderManager sManager;

    public static ImageLoaderManager getInstance() {
        if (sManager == null) {
            synchronized (ImageLoaderManager.class) {
                if (sManager == null) {
                    sManager = new ImageLoaderManager();
                }
            }
        }
        return sManager;
    }

    public void initImageLoader(ImageLoaderImpl imageLoader) {
        mImageLoader = imageLoader;
    }

    @Override
    public void disPlayImage(Context context, Object url, int failUrl, ImageView imageView) {
        if (mImageLoader == null) {
            throw new RuntimeException("请在Application中调用初始化的方法");
        }
        mImageLoader.disPlayImage(context, url, failUrl, imageView);
    }
}
