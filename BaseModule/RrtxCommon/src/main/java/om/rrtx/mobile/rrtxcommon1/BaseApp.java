package om.rrtx.mobile.rrtxcommon1;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.android.arouter.launcher.ARouter;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import cn.bingoogolapple.swipebacklayout.BGASwipeBackHelper;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SystemUtils;


/**
 * <AUTHOR> Angle
 * 创建时间: 2018/11/4 21:52
 * 描述: 所有的APPlication都要继承的相应
 */
public abstract class BaseApp extends Application {

    private static Context mContext;
    /**
     * 是否显示升级对话框
     */
    private static String mIsHasDownLoadUrl = "";

    private final String TAG = BaseApp.class.getSimpleName();
    /**
     * 正常状态
     */
    public static final int STATE_NORMAL = 0;
    /**
     * 从后台回到前台
     */
    public static final int STATE_BACK_TO_FRONT = 1;
    /**
     * 从前台进入后台
     */
    public static final int STATE_FRONT_TO_BACK = 2;
    /**
     * APP状态
     */
    private static int sAppState = STATE_NORMAL;

    /**
     * 标记程序是否已进入后台(依据onStop回调)
     */
    private boolean flag;

    /**
     * 标记程序是否已进入后台(依据onTrimMemory回调)
     */
    private boolean background;
    /**
     * 从前台进入后台的时间
     */
    private static long frontToBackTime;
    /**
     * 从后台返回前台的时间
     */
    private static long backToFrontTime;

    private static final long TIMEOUT = 5 * 60 * 1000;
//    private static final long TIMEOUT = 1 * 1 * 1000;

    private static boolean isLock;


    public static boolean isPromotion = false;

    private ActivityLifecycleCallbacks mLifecycleCallbacks = new ActivityLifecycleCallbacks() {
        @Override
        public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
            LogUtil.i(TAG, "onActivityCreated: ");
        }

        @Override
        public void onActivityStarted(@NonNull Activity activity) {
            LogUtil.i(TAG, "onActivityStarted: ");
            ARouter.getInstance().destroy();
            //ARouter.openLog();
            //ARouter.openDebug();
            ARouter.init(BaseApp.this);
        }

        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            /*for (String activityName : BaseConstants.activitys) {
                try {
                    Class clazz = Class.forName(activityName);
                    if (TextUtils.equals(clazz.getName(), activity.getComponentName().getClassName())) {
                        return;
                    }
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }*/

            if (background || flag) {
                background = false;
                flag = false;
                sAppState = STATE_BACK_TO_FRONT;
                backToFrontTime = System.currentTimeMillis();
                LogUtil.i(TAG, "onResume: STATE_BACK_TO_FRONT");
                if (canShowAd()) {
                    String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");

                    if (!TextUtils.isEmpty(authorization)) {
                        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.HomeHomeJump)
                                .navigation();
                        isLock = true;
                    } else {
                        ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP)
                                .navigation();
                    }
                }
            } else {
                sAppState = STATE_NORMAL;
            }
        }

        @Override
        public void onActivityPaused(@NonNull Activity activity) {
            LogUtil.i(TAG, "onActivityPaused: ");
        }

        @Override
        public void onActivityStopped(@NonNull Activity activity) {
            //判断当前activity是否处于前台
            if (!SystemUtils.isCurAppTop(activity)) {
                // 从前台进入后台
                sAppState = STATE_FRONT_TO_BACK;
                frontToBackTime = System.currentTimeMillis();
                flag = true;
                LogUtil.i(TAG, "onStop: " + "STATE_FRONT_TO_BACK");
            } else {
                // 否则是正常状态
                sAppState = STATE_NORMAL;
            }
        }

        @Override
        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
            LogUtil.i(TAG, "onActivitySaveInstanceState: ");
        }

        @Override
        public void onActivityDestroyed(@NonNull Activity activity) {
            LogUtil.i(TAG, "onActivityDestroyed: ");
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();

        mIsHasDownLoadUrl = "";

        //获取全局上下文
        mContext = getApplicationContext();

        //注册监听
        registerActivityLifecycleCallbacks(mLifecycleCallbacks);

        //初始化
        List<Class<? extends View>> list = new ArrayList<>();
        list.add(ImageView.class);
        BGASwipeBackHelper.init(this, list);
    }


    @Override
    protected void attachBaseContext(Context base) {
        if (TextUtils.isEmpty(LocaleManager.getInstance().getLanguage(base))) {
            Locale locale = LocaleManager.getInstance().getLocale(base.getResources());
            LogUtil.i("TAG", "onCreate: " + locale.getLanguage());
            base = LocaleManager.getInstance().setNewLocale(base, locale.getLanguage());
        }
        super.attachBaseContext(LocaleManager.getInstance().setLocale(base));
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        LogUtil.i(TAG, "onConfigurationChanged: ");
        super.onConfigurationChanged(newConfig);
        LocaleManager.getInstance().setLocale(this);
    }

    /**
     * 全局上下文
     *
     * @return 全局上下文
     */
    public static Context getAPPContext() {
        return mContext;
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        // TRIM_MEMORY_UI_HIDDEN是UI不可见的回调, 通常程序进入后台后都会触发此回调,大部分手机多是回调这个参数
        // TRIM_MEMORY_BACKGROUND也是程序进入后台的回调, 不同厂商不太一样, 魅族手机就是回调这个参数
        if (level == Application.TRIM_MEMORY_UI_HIDDEN || level == TRIM_MEMORY_BACKGROUND) {
            background = true;
        } else if (level == Application.TRIM_MEMORY_COMPLETE) {
            background = !SystemUtils.isCurAppTop(this);
        }
        if (background) {
            frontToBackTime = System.currentTimeMillis();
            sAppState = STATE_FRONT_TO_BACK;
            LogUtil.i(TAG, "onTrimMemory: TRIM_MEMORY_UI_HIDDEN || TRIM_MEMORY_BACKGROUND");
        } else {
            sAppState = STATE_NORMAL;
        }
    }

    /**
     * 进入后台间隔10分钟以后可以再次显示广告
     *
     * @return 是否能显示广告
     */
    public static boolean canShowAd() {
        return sAppState == STATE_BACK_TO_FRONT &&
                (backToFrontTime - frontToBackTime) > TIMEOUT;
    }

    public static boolean isIsLock() {
        return isLock;
    }

    public static void setIsLock(boolean isLock) {
        BaseApp.isLock = isLock;
    }

    public static String getIsHasDownLoadUrl() {
        return mIsHasDownLoadUrl;
    }

    public static void setIsHasDownLoadUrl(String isHasDownLoadUrl) {
        mIsHasDownLoadUrl = isHasDownLoadUrl;
    }

    public static boolean isIsPromotion() {
        return isPromotion;
    }

    public static void setIsPromotion(boolean isPromotion) {
        BaseApp.isPromotion = isPromotion;
    }
}
