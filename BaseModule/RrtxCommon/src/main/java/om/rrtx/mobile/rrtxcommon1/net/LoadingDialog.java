package om.rrtx.mobile.rrtxcommon1.net;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;

import om.rrtx.mobile.rrtxcommon1.R;


/**
 * <AUTHOR>
 * 菊花框
 * Created by dell on 2015/11/23.
 */

public class LoadingDialog extends Dialog {

    public LoadingDialog(Context context) {
        super(context, R.style.fullScreenDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getDialogViewId());
        setLocation();
        initView();
    }

    public int getDialogViewId() {
        return R.layout.dialog_loading;
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.MATCH_PARENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }

    public void initView() {
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        LottieAnimationView gifIv = findViewById(R.id.lottieIv);
    }
}