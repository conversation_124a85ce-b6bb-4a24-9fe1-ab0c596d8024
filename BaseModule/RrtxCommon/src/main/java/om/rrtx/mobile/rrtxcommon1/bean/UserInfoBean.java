package om.rrtx.mobile.rrtxcommon1.bean;

import java.util.List;

/**
 * <AUTHOR>
 */
public class UserInfoBean {
    /**
     * realName : hejinlong
     * mobile : +60121261619616
     * nikeName : yoyoyou
     * userAvatar :
     * userName : hejinlong
     * isContact :
     * mobileAreaCode :
     * email : <EMAIL>
     * gender : 1
     */

    private String realName;
    private String mobile;
    private String nikeName;
    private String userAvatar;
    private String userName;
    private String idCard;
    private String isContact;
    private String mobileAreaCode;
    private String email;
    private String gender;
    private String idExpiredDate;
    private String maskName;
    private String nationality;
    private String nationalityName;
    private String qrCode;
    private String accountType;//账户类型 0：常规账户 1：亲子账户
    private String hasSecretWord;//是否设置过安全信息 0：否 1：是

    private ParentUserInfo parentUserInfo;//关联账户信息

    public ParentUserInfo getParentUserInfo() {
        return parentUserInfo;
    }

    public void setParentUserInfo(ParentUserInfo parentUserInfo) {
        this.parentUserInfo = parentUserInfo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getHasSecretWord() {
        return hasSecretWord;
    }

    public void setHasSecretWord(String hasSecretWord) {
        this.hasSecretWord = hasSecretWord;
    }

    public void setMaskName(String maskName) {
        this.maskName = maskName;
    }

    public String getNationalityName() {
        return nationalityName;
    }

    public String getNationality() {
        return nationality;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getIdExpiredDate() {
        return idExpiredDate;
    }

    public String getMaskName() {
        return maskName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIsContact() {
        return isContact;
    }

    public void setIsContact(String isContact) {
        this.isContact = isContact;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public class ParentUserInfo {

        private String relationShip;
        private String linkDate;
        private String maskMobile;
        private String maskName;

        public String getRelationShip() {
            return relationShip;
        }

        public void setRelationShip(String relationShip) {
            this.relationShip = relationShip;
        }

        public String getLinkDate() {
            return linkDate;
        }

        public void setLinkDate(String linkDate) {
            this.linkDate = linkDate;
        }

        public String getMaskMobile() {
            return maskMobile;
        }

        public void setMaskMobile(String maskMobile) {
            this.maskMobile = maskMobile;
        }

        public String getMaskName() {
            return maskName;
        }

        public void setMaskName(String maskName) {
            this.maskName = maskName;
        }
    }
}
