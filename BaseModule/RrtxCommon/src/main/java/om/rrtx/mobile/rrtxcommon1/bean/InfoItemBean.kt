package om.rrtx.mobile.rrtxcommon1.bean

data class InfoItemBean(
    val title: String,
    var type: Int = 3, // 别问为什么
    var typeStr: String = "",
    var text: String = "",
    var pictureText: Int = 0,
    var iconId: Int = 0,
    var callBack: (() -> Unit)?=null,
    var callBack1: ((position: Int) -> Unit)?=null,
    var callBack2: ((position: Int, bean: InfoItemBean) -> Unit)?=null

//    var callBack: () -> Unit? = {},
//    var callBack1: (position: Int) -> Unit? = { position: Int -> },
//    var callBack2: (position: Int, bean: InfoItemBean)->Unit? = { position: Int, bean: InfoItemBean -> }
)