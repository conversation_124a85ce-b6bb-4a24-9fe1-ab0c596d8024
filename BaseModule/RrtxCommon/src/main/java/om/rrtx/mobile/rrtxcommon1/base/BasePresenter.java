package om.rrtx.mobile.rrtxcommon1.base;

import java.lang.ref.WeakReference;

/**
 * @param <V> View的引用
 * <AUTHOR> Angle
 */
public class BasePresenter<V> {
    //使用弱引用,避免内存泄漏
    private WeakReference<V> mReference;

    /**
     * 连接上View模型，类型于Activity与Fragment的连接onTachActivity()
     *
     * @param view
     */
    public void attachView(V view) {
        mReference = new WeakReference<>(view);
    }

    /**
     * 断开与View模型的连接，类型于Activity与Fragment的断开ondeTachActivity()
     * 防止后面做一些无用的事情
     */
    public void detachView() {
        if (mReference != null) {
            mReference.clear();
            mReference = null;
        }
    }

    /**
     * 是否连接上view
     *
     * @return 是否挂在成功
     */
    public boolean isViewAttached() {
        return mReference != null && mReference.get() != null;
    }

    protected V getView() {
        return (mReference != null && mReference.get() != null) ? mReference.get() : null;
    }
}
