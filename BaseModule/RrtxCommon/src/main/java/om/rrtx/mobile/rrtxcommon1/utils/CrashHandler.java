package om.rrtx.mobile.rrtxcommon1.utils;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;


/**
 * <AUTHOR>
 * 异常上报信息
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

    public static final String TAG = "CrashHandler";

    // CrashHandler 实例
    private static CrashHandler INSTANCE = new CrashHandler();

    /**
     * 程序的 Context 对象
     */
    private Context mContext;
    private Application app;

    private ExceptionCallback mExceptionCallback;

    /**
     * 系统默认的 UncaughtException 处理类
     */
    private Thread.UncaughtExceptionHandler mDefaultHandler;

    /**
     * 保证只有一个 CrashHandler 实例
     */
    private CrashHandler() {
    }

    /**
     * 获取 CrashHandler 实例 ,单例模式
     */
    public static CrashHandler getInstance() {
        return INSTANCE;
    }

    public void init(Context context, Application app, ExceptionCallback exceptionCallback) {
        // 传入app对象，为完美终止app
        this.app = app;
        mContext = context;
        mExceptionCallback = exceptionCallback;
        // 获取系统默认的 UncaughtException 处理器
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        // 设置该 CrashHandler 为程序的默认处理器
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    /**
     * 当 UncaughtException 发生时会转入该函数来处理
     */
    @Override
    public void uncaughtException(@NonNull Thread thread, @NonNull Throwable ex) {
        if (!handleException(ex) && mDefaultHandler != null) {
            //交由系统处理
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            //自己处理,退出相应的程序
            ActivityController.getInstance().finishAllActivity();
            System.exit(0);
        }
    }

    /**
     * 自定义错误处理，收集错误信息，发送错误报告等操作均在此完成
     *
     * @param ex
     * @return true：如果处理了该异常信息；否则返回 false
     */
    private boolean handleException(Throwable ex) {
        if (ex == null) {
            return false;
        }

        StringBuffer sb = new StringBuffer();
        sb.append("Exception:");
        sb.append(ex.toString());

        StackTraceElement[] stackTrace = ex.getStackTrace();
        for (StackTraceElement traceElement : stackTrace) {
            Log.e(TAG, "handleException: " + traceElement);
            sb.append(traceElement);
        }

        //异常上报到网络
        if (mExceptionCallback != null) {
            mExceptionCallback.exceptionReport(sb.toString());
        }
        return true;
    }

    public interface ExceptionCallback {
        void exceptionReport(String exStr);
    }
}