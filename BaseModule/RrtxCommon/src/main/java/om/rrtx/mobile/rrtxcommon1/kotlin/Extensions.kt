package om.rrtx.mobile.rrtxcommon1.kotlin

import om.rrtx.mobile.rrtxcommon1.BaseApp
import om.rrtx.mobile.rrtxcommon1.utils.ScreenUtil

fun Int.pt2px(): Int {
    return ScreenUtil.pt2px(BaseApp.getAPPContext(), this.toFloat()).toInt()
}

fun Float.pt2px(): Float {
    return ScreenUtil.pt2px(BaseApp.getAPPContext(), this)
}

fun Float.pt2sp(): Float {
    return ScreenUtil.pt2sp(BaseApp.getAPPContext(), this)
}