package om.rrtx.mobile.rrtxcommon1.bean;

/**
 * <AUTHOR> <PERSON>le
 * 创建时间 : 2018/11/5 23:19
 * 描述 : 所有请求的数据的基类
 */
public class BaseBean<T> {

    private T data;

    private String status ;
    private String message;

    public T getData() {
        return data;
    }

    public void setData(T sResData) {
        data = sResData;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
