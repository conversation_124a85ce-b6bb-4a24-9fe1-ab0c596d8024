package om.rrtx.mobile.rrtxcommon1.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/1/24 10:12
 * 描述 :
 */
public class BigDecimalUtils {

    /**
     * 获取标准金额
     *
     * @param amount       待转换的字符串
     * @param newScale     需要保留的位数
     * @param roundingMode 进制形式
     * @return 整理后的字符串
     */
    public static String getStandardAmount(String amount, int newScale, int roundingMode) {
        BigDecimal bigDecimal = new BigDecimal(amount);
        return bigDecimal.setScale(newScale, roundingMode).toString();
    }
}
