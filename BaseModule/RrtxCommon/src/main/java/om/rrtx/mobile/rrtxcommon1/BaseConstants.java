package om.rrtx.mobile.rrtxcommon1;

/**
 * 管理相应网络请求的类
 */
public class BaseConstants {

    public interface Urls {
        String FLUTTER_TEST_URL = "https://msf-pyramid-gateway.rrtx.vimbug.com";//测试环境
        String FLUTTER_FAT_URL = "https://mfs-gateway-fat.rrtx.vimbug.com";
        String FLUTTER_UAT_URL = "https://gw.onemoney.co.zw/apptest";
    }

    /**
     * 系统网络改变广播
     */
    public static final String ANDROID_NET_CHANGE_ACTION = "android.net.conn.CONNECTIVITY_CHANGE";

    /**
     * 设置系统的水平屏幕尺寸
     */
    public static final int DESIGNWIDTHINPX = 750;
    /**
     * 页面刷新的广播
     */
    public static final String REFRESHPAGERACTION = "om.rrtx.mobile.functioncommon.refresh.pager.action";


    public interface SaveParameter {
        /**
         * 是否设置过支付密码
         */
        String PAYMENTPASSWORDSETUPFLAG = "paymentPasswordSetupFlag";
        /**
         * 用户名称
         */
        String USERNAME = "userName";
        String MASKNAME = "maskName";
        String JUNIOR_ID = "junior_id";
        String JUNIOR_MOBILE = "junior_mobile";
        /**
         * 用户名称
         */
        String REALNAME = "realName";
        /**
         * 用户电话
         */
        String USERMOBILE = "userMobile";
        /**
         * 用户Id
         */
        String USERCARDID = "userCardId";
        /**
         * 用户头像
         */
        String USERHEARD = "userHeard";
        /**
         * token
         */
        String AUTHORIZATION = "Authorization";
        /**
         * 注册信息
         */
        String REGISTRATIONSTR = "registrationStr";
        /**
         * 协议信息
         */
        String CONDITIONSTR = "conditionStr";
        /**
         * 是否绑定指纹
         */
        String ISFINGER = "isFinger";
        /**
         * 是否开启手势
         */
        String ISGESTURE = "isGesture";
        /**
         * 账户类型
         */
        String ACCOUNTTYPE = "accountType";
        /**
         * 是否设置过安全信息
         */
        String HASECRETWORD = "hasSecretWord";
        /**
         * 用户ID
         */
        String USERID = "userId";
        /**
         * nric
         */
        String IDCARD = "idCard";
        /**
         * 联系人列表数据
         */
        String CONTACTLISTJSON = "ContactListJson";
        /**
         * 区号
         */
        String MOBILEAREACODE = "mobileAreaCode";
        /**
         * security-token
         */
        String SECURITYTOKEN = "security-token";
        /**
         * 币种保存的字段
         */
        String SAVECURRENCY = "saveCurrency";
        /**
         * 默认币种
         */
        String CURCURRENCY = "curCurrency";
        String ACTIVATE_CURRENCY = "activateCurrency";
        /**
         * 本地支付类型
         */
        String LOCALPAYMENTTYPE = "localPaymentType";
        /**
         * 极光推送的Id
         */
        String REGISTRATIONID = "registrationId";
        /**
         * Google推送的Id
         */
        String DEVICETOKEN = "deviceToken";
        /**
         * 极光推送的信息
         */
        String JPUSHDATA = "jpushData";
        /**
         * 极光推送的信息
         */
        String ISBROADCAST = "isBroadcast";
        /**
         * 公钥
         */
        String PUBLICKEY = "publicKey";
        /**
         * 用户名称
         */
        String FULLNAME = "fullName";
        /**
         * 最后登录时间
         */
        String LASTLOGINTIME = "lastLoginTime";
        /**
         * 亲子升级信息
         */
        String UPGRADE_INFO = "upgradeInfo";
        String REGISTER_INFO = "registerInfo";
        String CONDITION_INFO = "conditionInfo";
    }

    /**
     * 传递的参数
     */
    public interface Transmit {
        /**
         * 安全问题
         */
        String SECRETWORD = "secretWord";
        /**
         * 密码修改成功页面的标识
         */
        String PSDSUCCCESSFLAG = "psdSuccessFlag";
        /**
         * Pin码的校验
         */
        String PINPAYMENT = "pinPayment";
        /**
         * Pin码的密码
         */
        String PINSTR = "pinStr";
        /**
         * Pin码的密码
         */
        String OLDPINSTR = "oldPinStr";
        /**
         * 跳转页面
         */
        String JUMPFLAG = "jumpFlag";
        String USERSTATUS = "userStatus";
        String JUNIOR_ID = "Junior_id";
        String JUNIOR_NAME = "Junior_name";


        /**
         * 二维码
         */
        String QRCODE = "qrCode";
        /**
         * 电话号码
         */
        String MOBILE = "mobile";
        /**
         * 区号
         */
        String MOBILEAREACODE = "mobileAreaCode";
        /**
         * 密码或者指纹是否可以返回
         */
        String ISPSDBACK = "isPsdBack";
        /**
         * 类型字符串
         */
        String TYPESTR = "typeStr";
        /**
         * 提现Josn
         */
        String XWALLETPAYBEANJSON = "xWalletPayBeanJson";
        /**
         * 外部跳转数据Json
         */
        String OUTJUMPDATEJSON = "outJumpDateJson";
        /**
         * 订单号
         */
        String ORDERID = "orderId";
        /**
         * 订单类型
         */
        String ORDERTYPE = "orderType";
        /**
         * 推送的数据
         */
        String JPUSHJSON = "jpushJson";
        /**
         * 用户名称
         */
        String USERNAME = "userName";
        /**
         * 发送短信传递的json
         */
        String SENDMESSAGEJSON = "sendMessageJson";
        /**
         * 链接地址
         */
        String WEBVIEWURL = "webViewUrl";
        /**
         * 用户密码
         */
        String PASSWORD = "password";
        /**
         * 账户余额
         */
        String BALANCEBEAN = "balanceBean";
        String BALANCE_CERTIFICATE = "balance_certificate";
        /**
         * tab位置
         */
        String TABPOS = "tabPos";
        /**
         * 币种
         */
        String CURRENCY = "currency";
        String CERTIFICATE = "certificate";
        /**
         * 手势类型
         */
        String GESTURETYPE = "gestureType";
        /**
         * 手势是否开启
         */
        String ISOPEN = "isOpen";
        /**
         * 验证登陆密码类型
         * 0 创建手势
         * 1 更改手势
         * 2 关闭手势
         */
        String TYPE = "type";

        /**
         * 跳转来源：1 手势
         */
        String FROMTYPE = "fromType";
        /**
         * 流量
         */
        String BUNDLE = "bundle";
        String JSON = "json";
        String ORDERJSON = "json_order";
    }

    /**
     * 校验
     */
    public interface Check {
        /**
         * pin码的验证
         */
        String AUTHENTICATION = "authentication";
        /**
         * pin第一次设置
         */
        String SETFIRST = "setFirst";
        /**
         * pin第二次设置
         */
        String SETSECOND = "setSecond";
    }

    /**
     * 跳转标识
     */
    public interface JumpFlag {

        String Register_Account = "Register_Account";
        String Register_junior_Account = "Register_junior_Account";
        String Upgrade_junior_Account = "Upgrade_junior_Account";
        String Delete_junior_Account = "Delete_junior_Account";
        String SET_JUNIOR_PIN = "SET_JUNIOR_PIN";
        String BUY_AIRTIME_BUNDLE = "buy_airtime_bundel";
        String Auto_Debit = "auto_debit";
        /**
         * 收银台支付页
         */
        String Cashier_Pay = "CashierPay";
        /**
         * 设置密码页面
         */
        String PSDMANAGERJUMP = "psdManagerJump";
        /**
         * 免密支付页
         */
        String NO_PIN_PAY = "noPinPayJump";
        /**
         * 设置密码页面
         */
        String PSDLOGINJUMP = "psdLogin";
        /**
         * 设置首页跳转
         */
        String HOMEJUMP = "homeJump";
        String Search_Contacts_JUMP = "Search_Contacts_JUMP";
        /**
         * 充值页
         */
        String TOP_UP_JUMP = "top_up_Jump";

        /**
         * 亲子账户去往转账页
         */
        String JUNIOR_SEND_MONEY = "junior_send_money";

        /**
         * 联系人去往转账页
         */
        String SEND_MONEY = "send money";
        /**
         * 首页去往转账页
         */
        String SEND_TO_ONEMONEY = "Send To OneMoney";
        /**
         * 登录页跳转
         */
        String LOGINJUMP = "loginJump";
        /**
         * 设置shoushi跳转
         */
        String GESTUREJUMP = "gestureJump";
        /**
         * 忘记密码
         */
        String LOGIN_FORGET_JUMP = "forgetJump";
        /**
         * 转账
         */
        String TRANSFERDETAILS = "TransferDetails";
        /**
         * 转账固定金额
         */
        String TRANSFERFIXEDACTIVITY = "TransferFixedActivity";
        /**
         * 跳转到转账页面
         */
        String SPILTBILLACTIVITY = "SpiltBillActivity";
        /**
         * 扫码支付固定金额
         */
        String BESWEPTPAYMENTACTIVITY = "BeSweptPaymentActivity";
        /**
         * 扫码支付非固定金额
         */
        String BESWEPTUNFIXEDPAYMENTACTIVITY = "BeSweptUnfixedPaymentActivity";

        /**
         * 忘记用户名
         */
        String FORGETUSER = "forgetUser";
        /**
         * 忘记密码
         */
        String FORGETPSD = "forgetPsd";

        /**
         * 转账页面
         */
        String TRANSFER = "transfer";
        /**
         * 密码管理页面
         */
        String PSDMANAGER = "psdManager";
        /**
         * 注册页面
         */
        String REGISTRATION = "registration";
        /**
         * 修改用户手机号页面
         */
        String USERINFOEDITMOBILE = "UserInfoEditMobile";

        /**
         * 忘记用户名
         */
        String FORGETUSERNAME = "forgetUserName";
        /**
         * 忘记密码
         */
        String FORGETPASSWORD = "forgetUserPassWord";

        /**
         * 被扫
         */
        String BARCODEFLAG = "02";
        /**
         * 主扫
         */
        String QRCODEFLAG = "03";
        /**
         * 提现页面
         */
        String WITHDRAWALACTIVITY = "WithdrawalActivityJump";
        /**
         * 收银台页面
         */
        String CASHIERACTIVITY = "CashierActivity";
        /**
         * 收银台内部页面
         */
        String SCANCASHIERACTIVITY = "ScanCashierActivity";
        /**
         * 充值页面
         */
        String TOPUPACTIVITY = "TopUpActivity";
        /**
         * 设置首页跳转
         */
        String JPUSHJUMP = "jpushJump";
        /**
         * 迁移用户充值PIN
         */
        String RESETJUMP = "resetJump";
        /**
         * 首页aa收款跳转
         */
        String HOMEAALAUNCH = "homeAAlaunch";
        /**
         * 首页aa收款跳转
         */
        String HOMEAARECEIVED = "homeAAreceived";
        /**
         * 临时账号登录
         */
        String USERNOACTIVE = "userNoActive";
        /**
         * 其他缴费
         */
        String BILLPAYMENTOTHERDETAILS = "billPaymentOtherDetails";
        /**
         * 电话缴费
         */
        String BILLPAYMENTPHONEDETAILS = "billPaymentPhoneDetails";
        String PAY_MERCHANT_CODE = "payMerchantCode";
    }


    public interface HomeJumpType {
        String ReceivePaymentJump = "ReceivePaymentJump";

        /**
         * 这个别忘记权限
         */
        String ScanCodeJump = "ScanCodeJump";
        String MakePaymentJump = "MakePaymentJump";
        String TransferJump = "TransferJump";
        String AASpiltBillJump = "AASpiltBillJump";
        String ContactListJump = "ContactListJump";
        String HistoryJump = "HistoryJump";
        String TopUpJump = "TopUpJump";
        String WithdrawalJump = "WithdrawalJump";
        String MerchantJump = "MerchantJump";

        String HomeHomeJump = "HomeHomeJump";
        String HomePendingJump = "HomePendingJump";
        String HomeAccountJump = "HomeAccountJump";

        String CashierJump = "CashierJump";
        String OutCashierJump = "outCashierJump";
        String NewsJump = "NewsJump";
        String XStoreJump = "xStoreJump";
        String JUMP_HOME = "homeAndAccountJump";
    }

    public interface AppConfig {
        String LoginActivity = "om.rrtx.mobile.loginmodule.activity.LoginActivity";
        //        String SplashActivity = "om.rrtx.mobile.loginmodule.activity.SplashActivity";
        String LoginFingerLockActivity = "om.rrtx.mobile.securitymodule.activity.LoginFingerLockActivity";
        String LoginPasswordLockActivity = "om.rrtx.mobile.securitymodule.activity.LoginPasswordLockActivity";
        String CashierActivity = "om.rrtx.mobile.cashiermodel.activity.CashierActivity";
    }

    public static String[] activitys = {
            AppConfig.LoginActivity,
//            AppConfig.SplashActivity,
            AppConfig.LoginFingerLockActivity,
            AppConfig.LoginPasswordLockActivity,
            AppConfig.CashierActivity,
    };

    /**
     * 支付产品类型
     */
    public interface PaymentProduct {
        /**
         * 支付产品 00-APP
         */
        String APP = "00";
        /**
         * 01-H5
         */
        String H5 = "01";
        /**
         * 02-条码(Bar Code Payment)
         */
        String BARCODE = "02";
        /**
         * 03-二维码(Qr Code Payment)
         */
        String QRCODE = "03";
    }

    /**
     * 支付产品类型
     */
    public interface BannerPosition {
        /**
         * 首页
         */
        String HOME = "home";
    }

    /**
     * 支付产品类型
     */
    public interface BannerJumpState {
        /**
         * 充值
         */
        String TOPUP = "TopUp";
        /**
         * 转账
         */
        String SENDMONEY = "SendMoney";
        /**
         * 奖励
         */
        String LR = "LoyaltyRewards";
        /**
         * 话费流量充值
         */
        String BUY_AB = "BuyAirtimeBundle";
        /**
         * 电力缴费
         */
        String ZESA = "ZESA";
        /**
         * 邀请好友
         */
        String INVITE_FRIENDS = "InviteFriends";
        /**
         * 亲子账户
         */
        String JUNIOR_ACCOUNT = "JuniorAccountManagement";

    }

    public interface OrderSourceType {
        String EXTERNAL = "1";
        String INSIDE = "0";
        String HOME_SOURCE = "0";
        String ACCOUNT_SOURCE = "2";

    }

    /**
     * 二维码类型
     */
    public interface QrType {
        /**
         * 扫描外部订单
         */
        String QRCODE = "qrCode";

        /**
         * h5跳转
         */
        String WEBTOKEN = "h5";
    }

    /**
     * 二维码类型
     */
    public interface MessageType {
        /**
         * 首页
         */
        String HOME = "home";
        /**
         * 转账
         */
        String TRANSACTION = "transaction";
        /**
         * 历史账单列表
         */
        String TRANSACTIONLIST = "list";
        /**
         * 历史账单详情
         */
        String TRANSACTIONDETAIL = "detail";
        /**
         * AA我发起的列表
         */
        String TRANSACTIONSPILTRECLIST = "spiltRecList";
        /**
         * AA我支付的列表
         */
        String TRANSACTIONSPILTPAYLIST = "spiltPayList";
        /**
         * 转账
         */
        String SENDMONEY = "SendMoney";
        /**
         * 奖励
         */
        String LR = "LoyaltyRewards";
        /**
         * 话费流量充值
         */
        String BUY_AB = "BuyAirtime/Bundle";
        /**
         * 电力缴费
         */
        String ZESA = "ZESA";
        /**
         * 邀请好友
         */
        String INVITE_FRIENDS = "InviteFriends";
        /**
         * 亲子账户
         */
        String JUNIOR_ACCOUNT = "JuniorAccountManagement";
    }

    public interface UserStatus {
        //用户状态 0：正常：1：冻结；2：删除  3：未激活
        String NORMAL = "0";
        String FROZEN = "1";
        String DELETE = "2";
        String NOTACTIVE = "3";

    }

    public interface ParticipantType {
        //返现参与人类型 0：发起人   1：推荐人
        String INITIATOR = "0";
        String RECOMMENDER = "1";
    }

    public interface BusinessType {
        //业务类型 05-注册 00-充值
        String REGISTER = "05";
        String TOPUP = "00";
    }

    /**
     * 短信模板的类型
     */
    public interface MessageTemplateType {
        // 为空取默认;注册流程-Registration;
        // 忘记用户名-ForgetUsername;
        // 忘记密码-ForgetPassword；
        // 修改手机号-ModifyMobile；
        // 忘记支付密码-ForgetPaymentPassword;
        // 获取授权码-GetAuthCode；
        // 发送授权码-SendAuthCode；
        // ModifyMasterDevice-修改主设备
        String Registration = "Registration";
        String ForgetUsername = "ForgetUsername";
        String ForgetPassword = "ForgetPassword";
        String ModifyMobile = "ModifyMobile";
        String ForgetPaymentPassword = "ForgetPaymentPassword";
        String GetAuthCode = "GetAuthCode";
        String SendAuthCode = "SendAuthCode";
        String ModifyMasterDevice = "ModifyMasterDevice";
    }

    /**
     * 发送短信的跳转类型
     */
    public interface SendMessageJump {
        /**
         * 注册
         */
        String REGISTRATION = "registration";
        /**
         * 用户修改手机号
         */
        String USERINFOEDITMOBILE = "UserInfoEditMobile";
        /**
         * 忘记用户名
         */
        String FORGETUSERNAME = "forgetUserName";
        /**
         * 忘记密码
         */
        String FORGETPASSWORD = "forgetUserPassWord";
        /**
         * 密码管理页面
         */
        String PSDMANAGER = "psdManager";
        /**
         * 转账
         */
        String TRANSFERDETAILS = "TransferDetails";
        /**
         * 收银台页面
         */
        String CASHIERACTIVITY = "CashierActivity";
        /**
         * 跳转到转账页面
         */
        String SPILTBILLACTIVITY = "SpiltBillActivity";
        /**
         * 提现页面
         */
        String WITHDRAWALACTIVITY = "WithdrawalActivityJump";
        /**
         * 充值页面
         */
        String TOPUPACTIVITY = "TopUpActivity";
        /**
         * 扫码支付非固定金额
         */
        String BESWEPTUNFIXEDPAYMENTACTIVITY = "BeSweptUnfixedPaymentActivity";
        /**
         * 转账固定金额
         */
        String TRANSFERFIXEDACTIVITY = "TransferFixedActivity";
        /**
         * 设备管理页面
         */
        String DEVICEACTIVITY = "DeviceActivity";
        /**
         * 切换主设备
         */
        String AUTHORIZATIONLISTACTIVITY = "AuthorizationListActivity";
    }

    /**
     * 历史选择的类型
     */
    public interface HistoryType {
        //所有
        String ALL = "All";
        String ALLTYPE = "";
        //充值
        String TOPUP = "Top Up";
        String TOPUPTYPE = "00";
        //转账
        String FUNDTRANSFER = "Fund Transfer";
        String FUNDTRANSFERTYPE = "10";
        //收款
        String WITHDRAWAL = "Withdrawal";
        String WITHDRAWALTYPE = "20";
        //支付
        String PAY = "Payment";
        String PAYTYPE = "30";
        //账单
        String SPLITBILL = "Split Bill";
        String SPLITBILLTYPE = "40";
        //退款
        String REFUND = "Refund";
        String REFUNDTYPE = "50";
        //返现
        String CASHBACK = "cashBack";
        String CASHBACKTYPE = "55";

        //代客充值
        String AGENTTOPUP = "01";
        //代客话费充值
        String AGENTMOBILE = "02";
        //代客转账
        String AGENTTRAN = "11";
        //代客转账退款
        String AGENTTRANREFUND = "12";
        //代客提现
        String AGENTWITH = "22";

        //充值信用卡支付
        String TOPCREDITCARDTYPE = "00";
    }

    /**
     * 是否有活动的判定
     */
    public interface HasActivity {
        String HasActivity = "1";
        String NoActivity = "0";
    }

    /**
     * 是否有活动的判定
     */
    public interface DeviceLevel {
        String TemporaryDevice = "1";
        String MainDevice = "0";
    }

    /**
     * 外部跳转标识
     */
    public interface ExternalJumpFlag {
        /**
         * 邀请好友
         */
        String INVITE = "invite";
        /**
         * 订单二维码支付
         */
        String ORDERQR = "orderQR";
        /**
         * 短息跳转
         */
        String SMS = "sms";
        /**
         * h5支付接入,这个和订单二维码的处理一致
         */
        String HTML = "html";
    }
}
