package om.rrtx.mobile.rrtxcommon1.utils

import android.text.Editable
import android.text.TextWatcher

interface TextChangeEndListener :TextWatcher {
    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun afterTextChanged(p0: Editable) {
        textChangeEnd(p0)
    }

    fun textChangeEnd(editable: Editable)
}