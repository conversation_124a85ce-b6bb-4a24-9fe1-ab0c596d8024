package om.rrtx.mobile.rrtxcommon1.utils;

import android.util.Base64;
import android.util.Log;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.security.PublicKey;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.asymmetric.AsymmetricAlgorithm;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.symmetric.AES;


public class CryptoUtil {
    private final static byte[] iv = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16};


    public static String rsaEncrypt(String content, PrivateKey key) throws UnsupportedEncodingException {
        RSA rsa = new RSA(AsymmetricAlgorithm.RSA_ECB_PKCS1.getValue(), key, null);
        return encrypt(content, rsa, KeyType.PrivateKey);
    }

    public static String rsaEncrypt(String content, PublicKey key) throws UnsupportedEncodingException {
        RSA rsa = new RSA(AsymmetricAlgorithm.RSA_ECB_PKCS1.getValue(), null, key);
        return encrypt(content, rsa, KeyType.PublicKey);
    }

    protected static String encrypt(String content, RSA rsa, KeyType keyType) throws UnsupportedEncodingException {
        byte[] bytes = content.getBytes("utf-8");
        bytes = rsa.encrypt(bytes, keyType);
        Log.e("加密", "encrypt: " + Base64.encodeToString(bytes, Base64.NO_WRAP));
        return Base64.encodeToString(bytes, Base64.NO_WRAP);
    }

    public static String rsaDecrypt(String secret, PublicKey key) {
        RSA rsa = new RSA("RSA/ECB/PKCS1Padding", null, key);
        return decrypt(secret, rsa, KeyType.PublicKey);
    }

    public static String rsaDecrypt(String secret, PrivateKey key) {
        RSA rsa = new RSA(AsymmetricAlgorithm.RSA_ECB_PKCS1.getValue(), key, null);
        return decrypt(secret, rsa, KeyType.PrivateKey);
    }

    protected static String decrypt(String secret, RSA rsa, KeyType keyType) {
        secret = new String(Base64.decode(secret, Base64.NO_WRAP));
        return new String(rsa.decrypt(Base64.decode(secret, Base64.NO_WRAP), keyType));
    }

    public static String aesEncrypt(String content, String key) throws UnsupportedEncodingException {
        AES aes = new AES(Mode.ECB.name(), "PKCS7Padding", key.getBytes("utf-8"));
        byte[] bytes = content.getBytes("utf-8");
        content = Base64.encodeToString(aes.encrypt(bytes), Base64.NO_WRAP);
        return Base64.encodeToString(content.getBytes("utf-8"), Base64.NO_WRAP);
    }

    public static String aesDecrypt(String secret, String key) throws UnsupportedEncodingException {
        AES aes = new AES(Mode.ECB.name(), "PKCS7Padding",  key.getBytes("utf-8"));
        secret = new String(Base64.decode(secret, Base64.NO_WRAP));
        byte[] bytes = Base64.decode(secret, Base64.NO_WRAP);
        return new String(aes.decrypt(bytes));

    }

    public static String signWithSha256(String content) {
        Digester sha256 = new Digester(DigestAlgorithm.SHA256);
        return sha256.digestHex(content);
    }

    public static boolean verifyWithSha256(String content, String sign) {
        if (StrUtil.isEmpty(content) || StrUtil.isEmpty(sign)) {
            return false;
        }
        Digester sha256 = new Digester(DigestAlgorithm.SHA256);

        String sha256Sign = sha256.digestHex(content);
        return sign.equalsIgnoreCase(sha256Sign);
    }
}
