package om.rrtx.mobile.rrtxcommon1.bean

data class OrderSuccessBean(
    val orderType: String,
    val currency: String = "",
    val orderAmount: String = "",
    val actAmount: String = "",
    val certificate: String = "",
    val mobile: String = "",
    val mobileAreaCode: String = "",
    val name: String = "",
    val failedMsg: String = "",
    val isTradeSuccess: Boolean = true,
    val isFixCur: Boolean = false,
    val merNo: String = "",
    val rcvMobileAreaCode: String = "",
    val rcvMobile: String = "",
    val rcvRealName: String = "",
    val payFeeAmount: String = "",
    val buyToken: String = "",
    val trxStatus: String = "" //20 处理中就是超时，30 成功
)