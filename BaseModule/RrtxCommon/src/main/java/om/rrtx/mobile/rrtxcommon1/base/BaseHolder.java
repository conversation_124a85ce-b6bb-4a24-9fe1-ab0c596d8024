package om.rrtx.mobile.rrtxcommon1.base;

import android.util.SparseArray;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;


/**
 * 作者: 贺金龙 QQ:753355530
 * 项目名称: kehai
 * 类名称:com.sanhai.nep.student.base
 * 类描述:
 * 创建时间: 2018/3/23 12:44
 * 修改内容:
 * 修改时间:
 * 修改描述:
 */
public class BaseHolder extends RecyclerView.ViewHolder {

    private View mRootView;
    private SparseArray<View> mViews;

    public BaseHolder(View itemView) {
        super(itemView);
        mRootView = itemView;
        mViews = new SparseArray<>();
    }

    /**
     * 作者 贺金龙
     * <p>
     * 方法描述: 设置相应的根布局
     * 创建时间: 2018/3/2 14:01
     */
    public View getRootView() {
        return mRootView;
    }

    /**
     * 作者 贺金龙
     * <p>
     * 方法描述: 通用获取View的方法
     * 创建时间: 2018/3/2 12:46
     */
    public <T extends View> T getView(int viewId) {
        View view = mViews.get(viewId);
        if (view == null) {
            view = mRootView.findViewById(viewId);
            mViews.put(viewId, view);
        }
        return (T) view;
    }

    /**
     * 作者 贺金龙
     * <p>
     * 方法描述: 设置相应的TextView文字
     * 创建时间: 2018/3/2 13:42
     */
    public void setTextViewStr(int resId, String str) {
        TextView textView = getView(resId);
        textView.setText(str);
    }

    /**
     * 作者 贺金龙
     * <p>
     * 方法描述: 设置TextView颜色
     * 创建时间: 2018/3/2 14:10
     */
    public void setTextColor(int resId, int color) {
        TextView textView = getView(resId);
        textView.setTextColor(color);
    }

    /**
     * 作者 贺金龙
     * <p>
     * 方法描述: 设置背景
     * 创建时间: 2018/3/2 14:13
     */
    public void setBackground(int resId, int backgroundResId) {
        View view = getView(resId);
        view.setBackgroundResource(backgroundResId);
    }
}
