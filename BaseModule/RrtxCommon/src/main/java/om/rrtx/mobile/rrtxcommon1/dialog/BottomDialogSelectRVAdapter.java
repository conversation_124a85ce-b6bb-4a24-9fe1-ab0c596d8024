package om.rrtx.mobile.rrtxcommon1.dialog;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import om.rrtx.mobile.rrtxcommon.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.R;

/**
 * <AUTHOR>
 */
public class BottomDialogSelectRVAdapter extends RecyclerView.Adapter<BaseHolder> {

    private Context mContext;
    private List<String> mList;
    private RVAdapterItemClickListener<String> mItemClickListener;
    private int currentIndex = 0;

    public BottomDialogSelectRVAdapter(Context context, List<String> list) {
        mContext = context;
        mList = list;
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.dialog_select, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {

        TextView itemNameTv = holder.getView(R.id.name);
        ConstraintLayout view = holder.getView(R.id.view);
        itemNameTv.setText(mList.get(position));

        if (currentIndex == position) {
            //itemNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            itemNameTv.setTextColor(mContext.getResources().getColor(R.color.color_000000));
            view.setBackgroundColor(mContext.getResources().getColor(R.color.common_bg_f7f8fa));
        } else {
            //itemNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            itemNameTv.setTextColor(mContext.getResources().getColor(R.color.common_text_86909C));
            view.setBackgroundColor(mContext.getResources().getColor(R.color.color_FFFFFF));
        }

        holder.itemView.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                currentIndex = position;
                notifyDataSetChanged();
                if (mItemClickListener != null) {
                    mItemClickListener.itemClickListener(mList.get(position), position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    public void setItemClickListener(RVAdapterItemClickListener<String> itemClickListener) {
        mItemClickListener = itemClickListener;
    }

    public void setCheckstandStatus(int pos) {
        currentIndex = pos;
        notifyDataSetChanged();
    }
}
