package om.rrtx.mobile.rrtxcommon1.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.R;

public class BottomDialogSelectDialog extends Dialog implements RVAdapterItemClickListener<String> {

    private Context mContext;
    private RecyclerView mSelectRv;
    private TextView mCancel;
    private TextView mConfirm;
    private BottomDialogSelectRVAdapter mAdapter;
    private RVAdapterItemClickListener<String> mClickListener;
    private int index;
    private List<String> mList =new ArrayList<>();
    private int selectPositiom=0;

    public BottomDialogSelectDialog(@NonNull Context context, List list,int checkstandStatus) {
        super(context, R.style.transparentDialog);
        mContext = context;
        mList = list;
        index = checkstandStatus;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.bottom_dialog);

        setLocation();
        initView();
        initData();
        initListener();
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams wl = window.getAttributes();
            wl.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            wl.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL);
            window.setAttributes(wl);
        }
    }

    private void initView() {
        mSelectRv = findViewById(R.id.selectRv);
        mCancel = findViewById(R.id.tv_cancel);
        mConfirm = findViewById(R.id.tv_confirm);
    }

    private void initData() {
        mSelectRv.setLayoutManager(new LinearLayoutManager(mContext) {
            @Override
            public boolean canScrollVertically() {
                return true;
            }
        });

        mAdapter = new BottomDialogSelectRVAdapter(mContext, mList);
        mSelectRv.setAdapter(mAdapter);

        setSelect(index);
    }

    private void initListener() {
        mAdapter.setItemClickListener(this);
        mCancel.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                dismiss();
            }
        });
        mConfirm.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                if (mClickListener != null) {
                    mClickListener.itemClickListener(mList.get(selectPositiom), selectPositiom);
                }

                dismiss();
            }
        });
    }

    @Override
    public void itemClickListener(String value, int position) {
        selectPositiom = position;
        /*if (mClickListener != null) {
            mClickListener.itemClickListener(value, position);
        }
        dismiss();*/
    }

    /**
     * 设置选择状态
     *
     * @param pos 收银台状态
     */
    private void setSelect(int pos) {
        mAdapter.setCheckstandStatus(pos);
    }

    public void setClickListener(RVAdapterItemClickListener<String> clickListener) {
        mClickListener = clickListener;
    }
}
