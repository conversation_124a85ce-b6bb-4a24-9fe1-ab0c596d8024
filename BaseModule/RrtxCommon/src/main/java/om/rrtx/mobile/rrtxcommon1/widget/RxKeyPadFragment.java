package om.rrtx.mobile.rrtxcommon1.widget;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;

/**
 * <AUTHOR>
 * 底部键盘的Fragment
 */
public class RxKeyPadFragment extends BaseBottomFragment implements RxKeyPadView.KeyPadCallBack {

    private RxKeyPadView mKeyPadView;
    private KeyPadFragmentBack mKeyPadFragmentBack;
    private String isTitle;

    public RxKeyPadFragment(String isTitle) {
        this.isTitle = isTitle;
    }

    @Override
    public void show(@NonNull FragmentManager manager, @Nullable String tag) {
        try {
            //在每个add事务前增加一个remove事务，防止连续的add
            manager.beginTransaction().remove(this).commit();
            super.show(manager, tag);
        } catch (Exception e) {
            //同一实例使用不同的tag会异常,这里捕获一下
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.security_TransBottomSheetDialogStyle);
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.common_fragment_key_pad;
    }

    @Override
    public BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView(View rootView) {
        mKeyPadView = rootView.findViewById(R.id.kpvmd);
        mKeyPadView.setKeyTitle(isTitle);
    }

    @Override
    public void initDate() {
        super.initDate();
    }

    @Override
    public void initListener() {
        super.initListener();
        mKeyPadView.setKeyPadCallBack(this);
    }

    public void setKeyPadFragmentBack(KeyPadFragmentBack keyPadFragmentBack) {
        mKeyPadFragmentBack = keyPadFragmentBack;
    }

    @Override
    public void inputCode(String code) {
        if (mKeyPadFragmentBack != null) {
            mKeyPadFragmentBack.inputCode(code);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mKeyPadFragmentBack != null) {
            mKeyPadFragmentBack.onKeyPadPause();
        }
    }

    public interface KeyPadFragmentBack {
        /**
         * 输入code回调
         *
         * @param code 二维码
         */
        void inputCode(String code);

        /**
         * pause回调
         */
        void onKeyPadPause();
    }
}
