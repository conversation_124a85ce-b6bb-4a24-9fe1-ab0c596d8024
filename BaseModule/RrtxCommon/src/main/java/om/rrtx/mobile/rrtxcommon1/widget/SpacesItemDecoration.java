package om.rrtx.mobile.rrtxcommon1.widget;

import android.graphics.Rect;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Recyclerview 添加间隔
 */
public class SpacesItemDecoration extends RecyclerView.ItemDecoration {
    private int space;
    private int orientation = LinearLayout.VERTICAL;
    private String type;

    public SpacesItemDecoration(int space) {
        this.space = space;
    }

    public SpacesItemDecoration(int space, String type) {
        this.space = space;
        this.type = type;
    }

    public SpacesItemDecoration(int space, int orientation) {
        this.space = space;
        this.orientation = orientation;
    }

//    public SpacesItemDecoration() {
//        this.space = CommonConstant.ITEM_SPACE_BUTTON;
//    }

    @Override
    public void getItemOffsets(Rect outRect, View view,
                               RecyclerView parent, RecyclerView.State state) {

        if (orientation == LinearLayout.VERTICAL) {
            outRect.bottom = space;
            // Add top margin only for the first item to avoid double space between items
            if (type == null) {
                if (parent.getChildPosition(view) == 0)
                    outRect.top = space;
            }
        } else {
            outRect.right = space;
        }

    }
}