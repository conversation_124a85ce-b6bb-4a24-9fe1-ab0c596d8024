package om.rrtx.mobile.rrtxcommon1.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;

/**
 * 自定义带有空页面的RecycleView
 * Created by Angle on 2017/3/6.
 */

public class EmptyRecycleView extends RecyclerView {

    /**
     * 空页面
     */
    private View mEmptyView;

    public EmptyRecycleView(Context context) {
        super(context);
    }

    public EmptyRecycleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);

        RequestOptions.bitmapTransform(new CircleCrop());
    }

    public EmptyRecycleView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    private final RecyclerView.AdapterDataObserver mObserver = new AdapterDataObserver() {
        /*每次状态改变都会调用*/
        @Override
        public void onChanged() {
            isShowEmpty();
        }

        /*插入的时候会调用*/
        @Override
        public void onItemRangeInserted(int positionStart, int itemCount) {
            isShowEmpty();
        }

        /*移除的时候会调用*/
        @Override
        public void onItemRangeRemoved(int positionStart, int itemCount) {
            isShowEmpty();
        }
    };

    /**
     * 是否展示空页面
     */
    private void isShowEmpty() {
        if (mEmptyView != null && getAdapter() != null) {
            /*判断所有条目是否为0*/
            boolean emptyViewVisible = getAdapter().getItemCount() == 0;
            /*设置空页面是否展示*/
            mEmptyView.setVisibility(emptyViewVisible ? View.VISIBLE : GONE);
            /*设置RecycleView是否显示*/
            setVisibility(emptyViewVisible ? View.GONE : View.VISIBLE);
        }
    }

    @Override
    public void setAdapter(Adapter adapter) {
        /*获取老的Adapter*/
        Adapter oldAdapter = getAdapter();
        if (oldAdapter != null) {
            oldAdapter.unregisterAdapterDataObserver(mObserver);
        }
        super.setAdapter(adapter);
        if (adapter != null) {
            adapter.registerAdapterDataObserver(mObserver);
        }

        isShowEmpty();
    }

    /**
     * 设置EmptyView，之后刷新看是否显示空页面
     */
    public void setEmptyView(View emptyView) {
        mEmptyView = emptyView;
        isShowEmpty();
    }
}
