package om.rrtx.mobile.rrtxcommon1.model;

import com.kapp.xmarketing.bean.PubBean;

import java.util.HashMap;
import java.util.Map;

import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.services.CommonService;

public class CommonModel extends BaseLoader {

    private static CommonService mService=RetrofitServiceManager.getInstance().create(CommonService.class);

    /**
     * 检查更新
     *
     * @param language     当前语言
     * @param baseObserver 回调
     */
    public static void requestLanguageSetUp(String userId, String language, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        map.put(UserConstants.Parameter.LANGUAGE, language);
        observe(mService.requestLanguageSetUp(map)).subscribe(baseObserver);
    }


    public static void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 同意协议
     */
    public static void agreeCondition(String mobile, String title, String versionNo, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.CONDITIONTYPE, "01");
        map.put(UserConstants.Parameter.CONDITIONTITLE, title);
        map.put(UserConstants.Parameter.CONDITIONVERSIONNO, versionNo);
        map.put(UserConstants.Parameter.MOBILE, mobile);
        observe(mService.agreeCondition(map)).subscribe(baseObserver);
    }
    public static void getLatestCondition(String type, BaseObserver<ConditionBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.CONDITIONTYPE, type);
        observe(mService.getLatestCondition(map)).subscribe(baseObserver);
    }
}
