package om.rrtx.mobile.rrtxcommon1.utils.toast;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.R;


public class ToastUtil {
    public static Toast mToast;
    public static Toast mCenterToast;

    private static int mTextviewId;

    /**
     * 传入文字
     */
    /*public static void show(Context context, String str) {
        if (mToast == null) {
            mToast = Toast.makeText(context.getApplicationContext(), str, Toast.LENGTH_SHORT);
        } else {
            mToast.setText(str);
        }
        try {
            if (mTextviewId == 0) {
                mTextviewId = Resources.getSystem().getIdentifier("message", "id", "android");
            }
            ((TextView) mToast.getView().findViewById(mTextviewId)).setGravity(Gravity.CENTER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mToast.show();
    }*/
    private static Handler mHandler = new Handler(Looper.getMainLooper());
    public static void show(Context context, String str) {
        mHandler.post(() -> {
            Toast toast = new Toast(context);
            View view = LayoutInflater.from(context).inflate(R.layout.layout_toast, null);
            TextView tvToast = view.findViewById(R.id.tv_toast);
            tvToast.setText(str);
            //根据自己需要对view设置text或其他样式
            toast.setView(view);
            toast.setGravity(Gravity.CENTER, 0, 0);
            toast.setDuration(Toast.LENGTH_SHORT);
            toast.show();
        });
    }

    /**
     * 传入文字
     */
    public static void show(Context context, int resId) {
        if (mToast == null) {
            mToast = Toast.makeText(context.getApplicationContext(), resId, Toast.LENGTH_SHORT);
        } else {
            mToast.setText(resId);
        }
        try {
            if (mTextviewId == 0) {
                mTextviewId = Resources.getSystem().getIdentifier("message", "id", "android");
            }
            ((TextView) mToast.getView().findViewById(mTextviewId)).setGravity(Gravity.CENTER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mToast.show();
    }


    /**
     * 传入资源文件
     */
    public static void showToasty(Context context, int resId) {
        if (mToast == null) {
            mToast = Toasty.normal(context, resId);
            mToast.setGravity(Gravity.CENTER, 0, 0);
        } else {
            mToast.setText(resId);
        }
        mToast.show();
    }

    /**
     * 传入资源文件
     */
    public static void showToasty(Context context, String showStr) {
        Toasty.normal(context, showStr).show();
    }

    /**
     * 传入文字,在中间显示
     */
    public static void showCenter(Context context, String text) {
        if (mCenterToast == null) {
            mCenterToast = Toast.makeText(context.getApplicationContext(), text, Toast.LENGTH_SHORT);
            mCenterToast.setGravity(Gravity.CENTER, 0, 0);
        } else {
            mCenterToast.setText(text);
        }
        try {
            if (mTextviewId == 0) {
                mTextviewId = Resources.getSystem().getIdentifier("message", "id", "android");
            }
            ((TextView) mCenterToast.getView().findViewById(mTextviewId)).setGravity(Gravity.CENTER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mCenterToast.show();
    }

    public static void showCenter(Context context, int resId) {
        showCenter(context, context.getResources().getString(resId));
    }

    /**
     * 传入文字，带图片
     */
    public static void showImg(Activity activity, String text, int resImg) {
        if (mToast == null) {
            mToast = Toast.makeText(BaseApp.getAPPContext(), text, Toast.LENGTH_SHORT);
        } else {
            //如果当前Toast没有消失， 直接显示内容，不需要重新设置
            mToast.setText(text);
        }
        try {
            if (mTextviewId == 0) {
                mTextviewId = Resources.getSystem().getIdentifier("message", "id", "android");
            }
            ((TextView) mToast.getView().findViewById(mTextviewId)).setGravity(Gravity.CENTER);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //添加图片的操作,这里没有设置图片和文字显示在一行的操作呢...
        LinearLayout view = (LinearLayout) mToast.getView();
        ImageView imageView = new ImageView(activity);
        imageView.setImageResource(resImg);
        view.addView(imageView);

        mToast.show();
    }
}