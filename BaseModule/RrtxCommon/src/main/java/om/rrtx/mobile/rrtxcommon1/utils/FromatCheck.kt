package om.rrtx.mobile.rrtxcommon1.utils

class FormatCheck {

    companion object {
        @JvmStatic
        fun pinFromCheck(pin: String): Boolean {
            var sameCount = 0   //重复
            var maxCount = 0    //从大到小
            var minCount = 0    //从小到大
            for (i in 0..pin.length - 2) {
                val char1 = pin[i]
                val char2 = pin[i + 1]
                if (char1 == char2) {
                    sameCount++
                }
                if (char1 - char2 == 1) {
                    maxCount++
                }
                if (char1 - char2 == -1) {
                    minCount++
                }
            }

            if (sameCount == 3 || maxCount == 3 || minCount == 3) {
                return false
            }
            return true
        }

        @JvmStatic
        fun mobileFormatCheck(text: String): Boolean {
            if (text.length < 9) return false
            return (text[0] == '7')
        }
    }
}