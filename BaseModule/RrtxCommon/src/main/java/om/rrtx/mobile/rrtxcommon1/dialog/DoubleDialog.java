package om.rrtx.mobile.rrtxcommon1.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;

import om.rrtx.mobile.rrtxcommon1.R;


/**
 * <AUTHOR>
 * 两个按钮的对话框
 */
public class DoubleDialog extends Dialog {

    private TextView mContactsTv;
    private TextView mTitle;
    private TextView mLeftTv;
    private TextView mRightTv;
    private Context mContext;
    private DoubleCallback mDoubleCallback;

    public DoubleDialog(@NonNull Context context) {
        super(context, R.style.whiteRoundDialog);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_double);

        setCancelable(false);
        setCanceledOnTouchOutside(false);

        setLocation();

        initView();

        initListener();
    }


    /**
     * 初始化控件
     */
    private void initView() {
        mContactsTv = findViewById(R.id.contentTv);
        mTitle = findViewById(R.id.titleTv);
        mLeftTv = findViewById(R.id.leftTv);
        mRightTv = findViewById(R.id.rightTv);
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }

    private void initListener() {
        mLeftTv.setOnClickListener((view -> {
            if (mDoubleCallback != null) {
                mDoubleCallback.leftCallback();
            }
        }));

        mRightTv.setOnClickListener((view) -> {
            if (mDoubleCallback != null) {
                mDoubleCallback.rightCallback();
            }
        });
    }

    /**
     * 设置底部文字
     *
     * @param leftStr 底部左侧的文字
     */
    public DoubleDialog setLeftStr(String leftStr) {
        if (!TextUtils.isEmpty(leftStr)) {
            mLeftTv.setText(leftStr);
        }
        return this;
    }

    /**
     * 设置左边文字颜色
     *
     * @param leftColor 左边文字颜色
     */
    public DoubleDialog setLeftColor(int leftColor) {
        mLeftTv.setTextColor(mContext.getResources().getColor(R.color.color_131313));
        return this;
    }

    /**
     * 设置底部文字
     *
     * @param rightStr 底部右侧的文字
     */
    public DoubleDialog setRightStr(String rightStr) {
        if (!TextUtils.isEmpty(rightStr)) {
            mRightTv.setText(rightStr);
        }
        return this;
    }

    /**
     * 设置右边文字颜色
     *
     * @param rightColor 右边文字颜色
     */
    public DoubleDialog setRightColor(int rightColor) {
        mRightTv.setTextColor(rightColor);
        return this;
    }

    /**
     * 设置中间显示的文字
     *
     * @param contentStr 中间显示的文字
     */
    public DoubleDialog setContentStr(String contentStr) {
        if (!TextUtils.isEmpty(contentStr)) {
            mContactsTv.setText(contentStr);
        }
        return this;
    }

    /**
     * 设置标题显示的文字
     *
     * @param titleTv 标题文字
     */
    public DoubleDialog setMyTitle(String titleTv) {
        if (!TextUtils.isEmpty(titleTv)) {
            mTitle.setText(titleTv);
        }
        return this;
    }

    public void setDoubleCallback(DoubleCallback doubleCallback) {
        mDoubleCallback = doubleCallback;
    }

    /**
     * 按钮监听
     */
    public interface DoubleCallback {
        /**
         * 左侧点击事件
         */
        void leftCallback();

        /**
         * 右侧点击事件
         */
        void rightCallback();

    }
}
