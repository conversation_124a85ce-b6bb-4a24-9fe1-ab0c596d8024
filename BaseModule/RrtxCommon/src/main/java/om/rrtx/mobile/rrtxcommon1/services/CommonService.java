package om.rrtx.mobile.rrtxcommon1.services;

import com.kapp.xmarketing.bean.PubBean;

import java.util.Map;

import io.reactivex.Observable;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import retrofit2.Response;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface CommonService {

    /**
     * 切换语言
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.LANGUAGESETUP)
    Observable<Response<BaseBean<Object>>> requestLanguageSetUp(@FieldMap Map<String, String> formData);


    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);


    /**
     * 获取最新的协议
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.LASTESTCONDITION)
    Observable<Response<BaseBean<ConditionBean>>> getLatestCondition(@FieldMap Map<String, String> formData);

    /**
     * 同意协议
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.AGREECONDITION)
    Observable<Response<BaseBean<Object>>> agreeCondition(@FieldMap Map<String, String> formData);


}