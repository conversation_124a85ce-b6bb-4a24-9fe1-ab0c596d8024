package om.rrtx.mobile.rrtxcommon1.net;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import retrofit2.Response;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2018/11/5 23:30
 * 描述 :
 */
public abstract class BaseObserver<T> implements Observer<Response<BaseBean<T>>> {


    private static final String SUCCESS = "0";
    private static final String LOGOUT = "-5";
    private static final String RRB = "RRB-05009001";
    private static final String JUNIOR_RRB = "RRB-08000274";
    /**
     * 输错五次密码推出到登陆页
     */
    private static final String RRB_01002050 = "RRB-01002050";
    private static final String TAG = BaseObserver.class.getSimpleName();

    private Context mContext;
    private String mTag;
    private LoadingDialog mLoadingDialog;

    public BaseObserver(Context sContext) {
        mContext = sContext;
    }

    public BaseObserver(String tag, Context sContext) {
        mContext = sContext;
        mTag = tag;
    }

    @Override
    public void onSubscribe(Disposable d) {
        /*if (!TextUtils.isEmpty(mTag)) {
            DisposableManager.getInstance().addDisposable(mTag, d);
        }*/
        showDialog(mContext);
    }

    @Override
    public void onNext(Response<BaseBean<T>> baseBean) {
        BaseBean<T> body = baseBean.body();
        dismissDialog();
        if (body != null) {
            String resStatus = body.getStatus();
            LogUtil.i(TAG, "onNext: " + resStatus);
            if (TextUtils.equals(resStatus, SUCCESS)) {
                //相应成功
                requestSuccess(body.getData());
            } else if (TextUtils.equals(resStatus, LOGOUT)) {
                if (!TextUtils.isEmpty(mTag) && UserConstants.URL.LOGOUT.equals(mTag)){
                    requestFail(body.getMessage());
                }else{
                    //token失效,这里应该判断之前是否开启指纹
                    boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                    boolean isGesture = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISGESTURE, false);
                    if (isFinger) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                                .navigation();
                    }
                    if (isGesture) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginGestureActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                                .navigation();
                    }
                }
            } else if (TextUtils.equals(resStatus, RRB)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                Log.e("BaseObserver>>>", "zfw onNext>>> 出现RRB-05009001:");
                requestFail(body.getMessage());
            } else if (TextUtils.equals(resStatus, RRB_01002050)) {
                ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                        .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                        .navigation();
                requestErrorBody(body);
            } else if (TextUtils.equals(resStatus, JUNIOR_RRB)) {
                requestErrorBody(body);
            } else {
                //相应失败
                requestErrorBody(body);
                requestFail(body.getMessage());
            }
        } else {
            String netError = ResourceHelper.getString(mContext, R.string.common_tip_network_error);
            requestFail(netError);
        }
    }

    public void requestErrorBody(BaseBean<T> body) {

    }

    /**
     * 请求成功,返回相应的数据
     *
     * @param sResData 成功的实体类
     */
    public abstract void requestSuccess(T sResData);

    /**
     * 请求成功,但是返回失败的回调
     *
     * @param sResMsg 失败信息
     */
    public abstract void requestFail(String sResMsg);

    @Override
    public void onError(Throwable e) {
        dismissDialog();
        if (e instanceof ApiException) {
            requestFail(e.getMessage());
        } else {
            LogUtil.i(TAG, "onError: " + e.toString());
            String netError = ResourceHelper.getString(mContext, R.string.common_tip_network_error);
            ToastUtil.show(mContext, netError);
            //requestFail(netError);
        }
    }

    @Override
    public void onComplete() {
        /*if (!TextUtils.isEmpty(mTag)) {
            DisposableManager.getInstance().removeDisposable(mTag);
        }*/
    }

    private void showDialog(Context context) {
        try {
            if (mLoadingDialog == null && mContext != null) {
                mLoadingDialog = new LoadingDialog(context);
            }

            if (mLoadingDialog != null && !mLoadingDialog.isShowing() && mContext != null) {
                mLoadingDialog.show();
            }
        }catch (Exception e){
            Log.e("zwh", "loading显示异常: " + e.toString());
        }
    }

    private void dismissDialog() {
        try {
            if (mLoadingDialog != null && mLoadingDialog.isShowing() && mContext != null) {
                mLoadingDialog.dismiss();
                mLoadingDialog.cancel();
            }
        } catch (Exception e) {
            Log.e("zwh", "loading关闭异常: " + e.toString());
        }
    }

//    private boolean isValidActivity() {
//        if (!isDestroyed() && !isFinishing()) {
//            return true;
//        }
//        return false;
//    }
}
