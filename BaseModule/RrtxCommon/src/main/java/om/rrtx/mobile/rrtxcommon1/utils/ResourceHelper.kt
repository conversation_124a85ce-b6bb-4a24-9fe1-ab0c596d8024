package om.rrtx.mobile.rrtxcommon1.utils

import android.content.Context
import androidx.core.content.res.ResourcesCompat

/**
 * 依赖于 ActivityController
 */
class ResourceHelper {
    companion object {

        @JvmStatic
        fun getColor(id: Int) = ResourcesCompat.getColor(
            ActivityController.getInstance().currentActivity().resources,
            id,
            null
        )

        @JvmStatic
        fun getDrawable(id: Int) = ResourcesCompat.getDrawable(
            ActivityController.getInstance().currentActivity().resources, id, null
        )

        @JvmStatic
        fun getString(id: Int) =
            ActivityController.getInstance().currentActivity().resources.getString(id)

        @JvmStatic
        @Deprecated(
            "不建议使用，context 最好为 activity，不然会出现语言错误",
            ReplaceWith("getString(id)","om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper"),
            DeprecationLevel.WARNING
        )
        fun getString(context: Context, id: Int) = context.resources.getString(id)

        fun getString(id: Int, s: String) =
            ActivityController.getInstance().currentActivity().resources.getString(id, s)

        @JvmStatic
        fun getStringArr(id: Int): Array<String> =
            ActivityController.getInstance().currentActivity().resources.getStringArray(id)
    }

}
