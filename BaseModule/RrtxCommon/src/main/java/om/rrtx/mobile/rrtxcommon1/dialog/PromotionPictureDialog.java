package om.rrtx.mobile.rrtxcommon1.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;


/**
 * <AUTHOR>
 * 推广图片弹框
 */
public class PromotionPictureDialog extends Dialog {

    private ImageView mShowIcon;
    private ImageView mClosedIv;
    private PromotionPictureCallBack mPictureCallBack;
    private String mImgUrl;
    private Context mContext;

    public PromotionPictureDialog(@NonNull Context context, String showUrl) {
        super(context, R.style.transparentDialog);
        mContext = context;
        mImgUrl = showUrl;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_promotion_picture);


        setCancelable(false);
        setCanceledOnTouchOutside(false);

        setLocation();

        initView();

        initListener();
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }

    /**
     * 初始化控件
     */
    private void initView() {
        mClosedIv = findViewById(R.id.closed);
        mShowIcon = findViewById(R.id.showIcon);

        ImageLoaderManager.getInstance().disPlayImage(mContext, mImgUrl, R.drawable.drawable_loader_bg, mShowIcon);
    }

    private void initListener() {
        mClosedIv.setOnClickListener((view -> {
            dismiss();
        }));

        mShowIcon.setOnClickListener(view -> {
            if (mPictureCallBack != null) {
                mPictureCallBack.clickShowPicture();
            }
        });
    }

    public void setPictureCallBack(PromotionPictureCallBack pictureCallBack) {
        mPictureCallBack = pictureCallBack;
    }

    /**
     * 按钮监听
     */
    public interface PromotionPictureCallBack {
        /**
         * 点击事件
         */
        void clickShowPicture();
    }
}
