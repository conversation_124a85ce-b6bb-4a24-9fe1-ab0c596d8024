package om.rrtx.mobile.rrtxcommon1.net;

import android.content.Context;
import android.content.IntentFilter;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;


/**
 * 网络的管理类
 */
public class NetworkManager {

    private static volatile NetworkManager instance;

    private NetWorkStateReceiver receiver;


    private NetworkManager() {
        receiver = new NetWorkStateReceiver();
    }

    public static NetworkManager getDefault() {
        if (instance == null) {
            synchronized (NetworkManager.class) {
                if (instance == null) {
                    instance = new NetworkManager();
                }
            }
        }
        return instance;
    }

    /**
     * 设置相应的监听
     *
     * @param listener 相应的网络监听
     */
    public void setListener(NetWorkListener listener) {
        receiver.setListener(listener);
    }

    /**
     * 绑定相应的服务
     *
     * @param context 上下文
     */
    public void registerObserver(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(BaseConstants.ANDROID_NET_CHANGE_ACTION);
        context.registerReceiver(receiver, filter);
    }

    /**
     * 解绑相应的服务
     *
     * @param context 上下文
     */
    public void unRegisterObserver(Context context) {
        if (receiver != null) {
            context.unregisterReceiver(receiver);
        }
    }

}
