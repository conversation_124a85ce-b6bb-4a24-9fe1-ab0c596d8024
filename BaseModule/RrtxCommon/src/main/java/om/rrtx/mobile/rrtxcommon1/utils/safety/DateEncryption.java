package om.rrtx.mobile.rrtxcommon1.utils.safety;

import android.text.TextUtils;
import android.util.Log;

import org.json.JSONObject;

import java.security.MessageDigest;
import java.util.Map;

import cn.hutool.core.util.HexUtil;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;

/**
 * 加解密工具类
 *
 * <AUTHOR>
 */
public class DateEncryption {

    private static final String TAG = DateEncryption.class.getSimpleName();

    private static final String SHA256 = "SHA-256";
    private static final String MD5 = "MD5";

    public static String encode(Map<String, Object> map) {
        JSONObject jsonObject = new JSONObject(map);
        //这里注意是直接获取数据并添加上加密信息
        LogUtil.i(TAG, "encode: "+ enData(jsonObject.toString().replaceAll("\\\\", "")));
        return jsonObject.toString().replaceAll("\\\\", "") + enData(jsonObject.toString().replaceAll("\\\\", ""));
    }

    /**
     * 报文加密
     *
     * @param strParam 待加密的字符串
     */
    private static String enData(String strParam) {
        if (!TextUtils.isEmpty(strParam)) {
            try {
                byte[] bytes = strParam.getBytes("utf-8");
                // SHA 加密开始
                // 创建加密对象 并傳入加密類型
                MessageDigest messageDigest = MessageDigest.getInstance(SHA256);
                // 传入要加密的字符串
                messageDigest.update(bytes);
                // 得到 byte 類型结果
                bytes = messageDigest.digest();
                //转换成十六进制
                LogUtil.e(TAG, "enData: "+ new String(HexUtil.encodeHex(bytes)) );
                bytes = new String(HexUtil.encodeHex(bytes)).getBytes();
                messageDigest = MessageDigest.getInstance(MD5);
                // 传入要加密的字符串
                messageDigest.update(bytes);
                // 得到 byte 類型结果
                bytes = messageDigest.digest();
                return new String(HexUtil.encodeHex(bytes)).toUpperCase();
            } catch (Exception e) {
                LogUtil.e(TAG, "enData: 加密异常" + e.toString());
                return "";
            }
        } else {
            return "";
        }
    }
}


