package om.rrtx.mobile.rrtxcommon1.net;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;


/**
 * 监听网络变化的广播
 */
public class NetWorkStateReceiver extends BroadcastReceiver {

    private NetType mNetType;
    private NetWorkListener mNetWorkListener;

    public NetWorkStateReceiver() {
        //重置网络类型为没有类型
        mNetType = NetType.NONE;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        //网络监听
        if (intent == null || intent.getAction() == null) {
            LogUtil.i("done", "onReceive: 异常");
            return;
        }

        //网络状态的处理
        if (intent.getAction().equalsIgnoreCase(BaseConstants.ANDROID_NET_CHANGE_ACTION)) {
            LogUtil.i("done", "网络发生了改变");

            mNetType = NetWorkUtils.getNetType(context);

            if (NetWorkUtils.isNetworkConnected(context)) {
                LogUtil.i("done", "网络连接成功");
                if (mNetWorkListener != null) {
                    mNetWorkListener.onConnect(mNetType);
                }
            } else {
                LogUtil.i("done", "网络连接失败");
                if (mNetWorkListener != null) {
                    mNetWorkListener.onDisConnect();
                }
            }
        }
    }

    /**
     * 设置相应的监听
     *
     * @param listener 监听
     */
    public void setListener(NetWorkListener listener) {
        mNetWorkListener = listener;
    }
}
