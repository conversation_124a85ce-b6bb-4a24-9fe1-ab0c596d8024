package om.rrtx.mobile.rrtxcommon1.base;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.List;

public class BaseVp2Adapter extends FragmentStateAdapter {
    private List<Fragment> mList;

    public BaseVp2Adapter(FragmentActivity fa, List<Fragment> lists) {
        super(fa);
        mList = lists;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return mList.get(position);
    }


    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }
}