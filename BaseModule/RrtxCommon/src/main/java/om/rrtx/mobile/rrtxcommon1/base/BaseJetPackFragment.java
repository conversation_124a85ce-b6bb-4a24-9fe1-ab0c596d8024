package om.rrtx.mobile.rrtxcommon1.base;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;


/**
 * <AUTHOR>
 */
public abstract class BaseJetPackFragment<DB extends ViewDataBinding> extends Fragment {
    public AppCompatActivity mActivity;
    public DB dataBinding;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        mActivity = (AppCompatActivity) context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        //创建db对象
        dataBinding = DataBindingUtil.inflate(inflater, createContentView(), container, false);

        initView(dataBinding.getRoot());

        //初始化工作空间
        initWorkspaceAction();

        return dataBinding.getRoot();
    }

    public void initView(View view) {

    }


    /**
     * 创建布局
     *
     * @return 布局信息
     */
    public abstract @LayoutRes
    int createContentView();

    /**
     * 初始化工作空间
     */
    public abstract void initWorkspaceAction();

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        initData();
        initListener();
    }

    public void initData() {

    }

    public void initListener() {

    }

//    /**
//     * 给当前BaseFragment用的
//     */
//    protected ViewModelProvider getAppViewModelProvider() {
//        return ((BaseAPP) mActivity.getApplicationContext()).getAppViewModelProvider(mActivity);
//    }

    /**
     * 给所有的fragment提供的函数，可以顺利的拿到 ViewModel
     */
    protected ViewModelProvider getFragmentViewModelProvider(Fragment fragment) {
        return new ViewModelProvider(fragment, fragment.getDefaultViewModelProviderFactory());
    }

    /**
     * 给所有的fragment提供的函数，可以顺利的拿到 ViewModel
     */
    protected ViewModelProvider getActivityViewModelProvider(AppCompatActivity activity) {
        return new ViewModelProvider(activity, activity.getDefaultViewModelProviderFactory());
    }

}
