package om.rrtx.mobile.rrtxcommon1.bean;

/**
 * <AUTHOR> zfw
 * @time   : 2023/3/20 10:13
 * @desc   : 协议Bean
 */
public class ConditionBean {

    //协议名称
    private String conditionTitle;
    //协议版本
    private String conditionVersionNo;
    //协议内容
    private String conditionContent;
    //协议类型
    private String conditionType;

    public String getConditionTitle() {
        return conditionTitle;
    }

    public void setConditionTitle(String conditionTitle) {
        this.conditionTitle = conditionTitle;
    }

    public String getConditionVersionNo() {
        return conditionVersionNo;
    }

    public void setConditionVersionNo(String conditionVersionNo) {
        this.conditionVersionNo = conditionVersionNo;
    }

    public String getConditionContent() {
        return conditionContent;
    }

    public void setConditionContent(String conditionContent) {
        this.conditionContent = conditionContent;
    }

    public String getConditionType() {
        return conditionType;
    }

    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    @Override
    public String toString() {
        return "ConditionBean{" +
                "conditionTitle='" + conditionTitle + '\'' +
                ", conditionVersionNo='" + conditionVersionNo + '\'' +
                ", conditionContent='" + conditionContent + '\'' +
                ", conditionType='" + conditionType + '\'' +
                '}';
    }
}
