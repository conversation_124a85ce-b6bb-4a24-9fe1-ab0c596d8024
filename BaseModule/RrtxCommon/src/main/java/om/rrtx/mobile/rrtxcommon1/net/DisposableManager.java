package om.rrtx.mobile.rrtxcommon1.net;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/4/29 14:58
 * 描述 : 请求的管理类
 */
public class DisposableManager {
    private DisposableManager() {
        sMap = new HashMap<>();
    }

    private static Map<String, Disposable> sMap;

    private static DisposableManager sDisposableManager;

    public static DisposableManager getInstance() {
        if (sDisposableManager == null) {
            synchronized (DisposableManager.class) {
                if (sDisposableManager == null) {
                    sDisposableManager = new DisposableManager();
                }
            }
        }
        return sDisposableManager;
    }

    /**
     * 添加相应的Disposable
     *
     * @param tag        表示页面的
     * @param disposable disposable
     */
    public void addDisposable(String tag, Disposable disposable) {
        sMap.put(tag, disposable);
    }

    /**
     * 移除某一个Disposable
     *
     * @param tag 标识
     */
    public void removeDisposable(String tag) {
        if (!sMap.isEmpty()) {
            sMap.remove(tag);
        }
    }

    /**
     * 移除所有的Disposable
     */
    public void removeAllDisposable() {
        if (!sMap.isEmpty()) {
            sMap.clear();
        }
    }

    /**
     * 取消一个请求
     *
     * @param tag 标识
     */
    public void cancelDisposable(String tag) {
        if (sMap.isEmpty()) {
            return;
        }
        Disposable disposable = sMap.get(tag);

        if (disposable == null) {
            return;
        }

        disposable.dispose();
        sMap.remove(tag);

    }

    /**
     * 取消所有请求
     */
    public void cancelAllDisposable() {
        if (sMap.isEmpty()) {
            return;
        }
        Set<String> keys = sMap.keySet();
        for (String apiKey : keys) {
            cancelDisposable(apiKey);
        }
    }
}
