package om.rrtx.mobile.rrtxcommon1.net;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import retrofit2.Response;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2018/11/5 23:30
 * 描述 :
 */
public abstract class BaseNoDialogObserver<T> implements Observer<Response<BaseBean<T>>> {

    private static final String SUCCESS = "0";
    private Context mContext;
    private String mTag;
    private static final String LOGOUT = "-5";
    private static final String RRB = "RRB-05009001";
    private static final String TAG = BaseNoDialogObserver.class.getSimpleName();

    public BaseNoDialogObserver(Context sContext) {
        mContext = sContext;

    }

    public BaseNoDialogObserver(String tag, Context sContext) {
        mContext = sContext;
        mTag = tag;
    }

    @Override
    public void onSubscribe(Disposable d) {
        if (!TextUtils.isEmpty(mTag)) {
            DisposableManager.getInstance().addDisposable(mTag, d);
        }
    }

    @Override
    public void onNext(Response<BaseBean<T>> baseBean) {
        BaseBean<T> body = baseBean.body();
        LogUtil.i(TAG, "onNext: " + baseBean.toString());
        if (body != null) {
            String resStatus = body.getStatus();
            if (TextUtils.equals(resStatus, SUCCESS)) {
                //相应成功
                requestSuccess(body.getData());
            } else if (TextUtils.equals(resStatus, LOGOUT)) {
                //token失效,这里应该判断之前是否开启指纹
                boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                if (isFinger) {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                            .navigation();
                } else {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                            .navigation();
                }
            } else if (TextUtils.equals(resStatus, RRB)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                Log.e("BaseObserver>>>", "zfw onNext>>> 出现RRB-05009001:");
                requestFail(RRB);
            } else {
                //相应失败
                requestErrorBody(body);
                requestFail(body.getMessage());
            }
        } else {
            String netError = ResourceHelper.getString(BaseApp.getAPPContext(), R.string.common_tip_network_error);
            requestFail(netError);
        }
    }

    public void requestErrorBody(BaseBean<T> body) {

    }


    /**
     * 请求成功,但是返回失败的回调
     *
     * @param sResMsg 失败信息
     */
    public abstract void requestFail(String sResMsg);

    /**
     * 请求成功,返回相应的数据
     *
     * @param sResData 成功的实体类
     */
    public abstract void requestSuccess(T sResData);

    @Override
    public void onError(Throwable e) {
        if (e instanceof ApiException) {
            requestFail(e.getMessage());
        } else {
            LogUtil.i(TAG, "onError: " + e.toString());
            String netError = ResourceHelper.getString(mContext, R.string.common_tip_network_error);
            requestFail(netError);
        }
    }

    @Override
    public void onComplete() {
        if (!TextUtils.isEmpty(mTag)) {
            DisposableManager.getInstance().removeDisposable(mTag);
        }
    }
}
