<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/mobileTitle_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24pt"
            android:text="@string/mobile_No"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/mobile_bg"
            cornerBackgroundRadius="@{10}"
            android:layout_width="match_parent"
            android:layout_height="100pt"
            android:layout_marginTop="12pt"
            app:layout_constraintTop_toBottomOf="@id/mobileTitle_tv" />

        <TextView
            android:id="@+id/area_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/jia_263"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            android:layout_marginLeft="24pt"
            app:layout_constraintBottom_toBottomOf="@id/mobile_bg"
            app:layout_constraintLeft_toLeftOf="@id/mobile_bg"
            app:layout_constraintTop_toTopOf="@id/mobile_bg" />

        <View
            android:id="@+id/monbileLine"
            android:layout_width="1pt"
            android:layout_height="0pt"
            android:layout_marginLeft="24pt"
            android:layout_marginTop="20pt"
            android:layout_marginBottom="20pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintBottom_toBottomOf="@id/mobile_bg"
            app:layout_constraintLeft_toRightOf="@id/area_tv"
            app:layout_constraintTop_toTopOf="@id/mobile_bg" />

        <EditText
            android:id="@+id/mobile_ed"
            android:layout_width="0pt"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:layout_marginRight="24pt"
            android:gravity="left"
            android:inputType="number"
            android:maxLength="9"
            android:background="@null"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            android:textCursorDrawable="@drawable/common_cursor"
            app:layout_constraintBottom_toBottomOf="@id/mobile_bg"
            app:layout_constraintLeft_toRightOf="@id/monbileLine"
            app:layout_constraintRight_toLeftOf="@id/addressBock_iv"
            app:layout_constraintTop_toTopOf="@id/mobile_bg" />

        <ImageView
            android:id="@+id/addressBock_iv"
            android:layout_width="43pt"
            android:layout_height="43pt"
            android:layout_marginRight="30pt"
            android:background="@drawable/ic_address_book"
            app:layout_constraintBottom_toBottomOf="@id/mobile_bg"
            app:layout_constraintRight_toRightOf="@id/mobile_bg"
            app:layout_constraintTop_toTopOf="@id/mobile_bg" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>