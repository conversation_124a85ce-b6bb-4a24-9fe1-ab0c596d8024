<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/lin_root"
    android:gravity="bottom|center_horizontal"
    android:layout_marginBottom="200pt"
    android:background="@drawable/bg_toast_shape"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/tv_toast"
        android:gravity="center"
        android:layout_margin="20pt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#ffffff"
        />
</LinearLayout>