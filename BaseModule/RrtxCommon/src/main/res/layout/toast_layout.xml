<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/toast_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="false"
    android:gravity="center"
    android:orientation="horizontal"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/toast_icon"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginEnd="16pt"
        android:layout_marginRight="16pt"
        android:contentDescription="@string/base_app_name" />

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center" />

</LinearLayout>