<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/view"
    android:layout_width="match_parent"
    android:layout_height="80pt"
    android:layout_marginTop="16pt"
    android:background="@color/common_bg_f7f8fa">

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textColor="@color/color_000000"
        android:textSize="32pt"
        tools:text="asdadjaskd" />

</androidx.constraintlayout.widget.ConstraintLayout>