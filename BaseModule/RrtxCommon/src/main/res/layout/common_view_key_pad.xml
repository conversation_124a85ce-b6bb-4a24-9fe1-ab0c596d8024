<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/keyTitle"
        android:layout_width="match_parent"
        android:layout_height="60pt"
        android:background="@color/color_FFFFFF">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_marginLeft="30pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/security_ic_suo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/titleTv"
            android:layout_width="0pt"
            android:layout_height="60pt"
            android:layout_marginLeft="10pt"
            android:gravity="center_vertical"
            android:text="@string/common_keyboard_tip"
            android:textColor="@color/color_131313"
            android:textSize="22pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/icon"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/common_custom_keyboard" />
</LinearLayout>