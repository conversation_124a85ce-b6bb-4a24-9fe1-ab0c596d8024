<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootCl"
    android:layout_width="638pt"
    android:layout_height="578pt">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/ic_update" />

    <ImageView
        android:id="@+id/updateTv"
        android:layout_width="400pt"
        android:layout_height="90pt"
        android:layout_marginBottom="40pt"
        android:background="@drawable/drawable_btn_updata"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/contentTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginRight="30pt"
        android:layout_marginBottom="40pt"
        android:gravity="center"
        android:text="@string/update_label_force_update_tip"
        android:textColor="@color/color_404040"
        android:textSize="34pt"
        app:layout_constraintBottom_toTopOf="@id/updateTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="400pt"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40pt"
        android:gravity="center"
        android:text="@string/update_label_prompt"
        android:textColor="@color/color_404040"
        android:textSize="36pt"
        app:layout_constraintBottom_toTopOf="@id/contentTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>