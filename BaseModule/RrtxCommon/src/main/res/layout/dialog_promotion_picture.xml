<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="30pt"
    android:layout_marginRight="30pt">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/closed"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginRight="10pt"
        android:src="@drawable/drawable_icon_closed"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/showIcon"
        android:layout_width="690pt"
        android:layout_height="800pt"
        android:layout_marginTop="10pt"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:shapeAppearanceOverlay="@style/roundedCornerStyle"
        app:layout_constraintTop_toBottomOf="@id/closed" />

</androidx.constraintlayout.widget.ConstraintLayout>