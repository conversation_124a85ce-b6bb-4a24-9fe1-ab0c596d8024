<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:layout_marginLeft="30pt"
    android:layout_width="75dp"
    android:layout_height="75dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:lottie_autoPlay="true"
        app:lottie_fileName="json/loading.json"
        app:lottie_loop="true" />
</androidx.constraintlayout.widget.ConstraintLayout>