<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android" >
    <!-- 填充 -->
    <solid android:color="#b3000000" /> <!-- 定义填充的颜色值 -->

    <!-- 描边 -->
    <stroke
        android:width="0dp"
        android:color="#fff5f5f5" /> <!-- 定义描边的宽度和描边的颜色值 -->

    <!-- 圆角 -->
    <corners
        android:bottomLeftRadius="50dp"
        android:bottomRightRadius="50dp"
        android:topLeftRadius="50dp"
        android:topRightRadius="50dp" /> <!-- 设置四个角的半径 -->

    <!-- 间隔 -->
    <padding
        android:bottom="0dp"
        android:left="0dp"
        android:right="0dp"
        android:top="0dp" /> <!-- 设置各个方向的间隔 -->

</shape>
