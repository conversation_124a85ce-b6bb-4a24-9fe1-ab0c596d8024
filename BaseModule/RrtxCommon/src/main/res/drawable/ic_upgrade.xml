<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
  <path
      android:pathData="M0,100a100,100 0,1 0,200 0a100,100 0,1 0,-200 0z"
      android:fillColor="#F3881E"
      android:fillAlpha="0.04"/>
  <path
      android:pathData="M20,100a80,80 0,1 0,160 0a80,80 0,1 0,-160 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="100"
          android:centerY="100"
          android:gradientRadius="80"
          android:type="radial">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="1" android:color="#1EF3881E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M40,100a60,60 0,1 0,120 0a60,60 0,1 0,-120 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="100"
          android:startY="40"
          android:endX="100"
          android:endY="160"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFAB58"/>
        <item android:offset="1" android:color="#FFF3881E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M120.92,90.86L102.17,72.11C101.45,71.39 100.49,71 99.53,71C98.57,71 97.61,71.36 96.86,72.11L78.11,90.86C75.74,93.23 77.42,97.25 80.78,97.25L88.28,97.25L88.28,109.72C88.28,111.79 89.96,113.47 92.03,113.47L107.03,113.47C109.1,113.47 110.78,111.79 110.78,109.72L110.78,97.25L118.28,97.25C121.61,97.25 123.26,93.23 120.92,90.86ZM108.83,117.25L90.08,117.25C89.06,117.25 88.22,118.09 88.22,119.11C88.22,120.13 89.06,120.97 90.08,120.97L108.86,120.97C109.88,120.97 110.72,120.13 110.72,119.11C110.72,118.09 109.88,117.25 108.83,117.25ZM108.83,124.66L90.08,124.66C89.06,124.66 88.22,125.5 88.22,126.52C88.22,127.54 89.06,128.38 90.08,128.38L108.86,128.38C109.88,128.38 110.72,127.54 110.72,126.52C110.72,125.5 109.88,124.66 108.83,124.66Z"
      android:fillColor="#FFFFFF"/>
</vector>
