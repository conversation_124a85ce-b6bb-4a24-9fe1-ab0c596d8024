<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础主题-->
    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/common_ye_F3881E</item>
        <item name="colorPrimaryDark">@color/common_ye_F3881E</item>
        <item name="colorAccent">@color/common_ye_F3881E</item>
    </style>

    <style name="APPTheme" parent="BaseTheme">
        <item name="android:fitsSystemWindows">true</item>
    </style>
    <!--解决半透明背景-->
    <style name="security_TransBottomSheetDialogStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="bottomSheetStyle">@style/security_CustomBottomSheetStyle</item>
    </style>
    <style name="security_CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <style name="APPThemeNoState" parent="BaseTheme">
        <!--默认的颜色-->
        <item name="colorControlNormal">@color/color_AEB3C0</item>
        <!--整体的颜色-->
        <item name="colorAccent">@color/common_bg_f7f8fa</item>
        <!--文字的提示颜色-->
        <item name="android:textColorHint">@color/color_AEB3C0</item>
    </style>

    <!--Material主题-->
    <style name="MaterialTheme" parent="Theme.MaterialComponents.Light.NoActionBar" />

    <style name="AppBottomSheet" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppBottomSheetStyle</item>
    </style>

    <style name="AppBottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@android:color/transparent</item>
    </style>

    <!--输入框的主题-->
    <style name="editTextTheme" parent="APPThemeNoState">
        <!--默认的颜色-->
        <item name="colorControlNormal">@color/color_AEB3C0</item>
        <!--整体的颜色-->
        <item name="colorAccent">@color/color_AEB3C0</item>
        <!--文字的提示颜色-->
        <item name="android:textColorHint">@color/color_AEB3C0</item>
    </style>

    <!--全屏对话框-->
    <style name="fullScreenDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:typeface">monospace</item>
    </style>

    <!--圆角对话框-->
    <style name="whiteRoundDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@drawable/drawable_white_round</item>
        <item name="android:windowBackground">@drawable/drawable_white_round</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!--透明对话框-->
    <style name="transparentDialogDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>


    <style name="TransBottomSheetDialogStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="transparentDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!--透明对话框的主题-->
    <style name="transparentBottomDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:typeface">monospace</item>
    </style>

    <style name="roundedCornerStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">30pt</item>
    </style>

    <style name="smallRoundedCornerStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10pt</item>
    </style>

    <style name="NextTvStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">80pt</item>
        <item name="android:textSize">32pt</item>
        <item name="android:textColor">@color/color_FFFFFF</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/common_btn_bg</item>
    </style>
</resources>