<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="SWImageView">
        <attr name="borderRadius" format="dimension"/>
        <attr name="type">
            <enum name="normal" value="-1"/>
            <enum name="circle" value="0"/>
            <enum name="round" value="1"/>
        </attr>
        <attr name="borderWidth" format="dimension" />
        <attr name="borderColor" format="reference|color" />
    </declare-styleable>

    <!--验证码的属性-->
    <declare-styleable name="verCodeEditText">
        <attr name="figures" format="integer" />
        <attr name="verCodeMargin" format="dimension" />
        <attr name="bottomLineSelectedColor" format="reference" />
        <attr name="bottomLineNormalColor" format="reference" />
        <attr name="bottomLineHeight" format="dimension" />
        <attr name="selectedBackgroundColor" format="reference" />
        <attr name="unselectedBackgroundColor" format="reference" />
        <attr name="cursorDuration" format="integer" />
        <attr name="cursorWidth" format="dimension" />
        <attr name="cursorColor" format="color|reference" />
        <attr name="is_password" format="boolean" />
    </declare-styleable>


</resources>