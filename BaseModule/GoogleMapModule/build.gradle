apply plugin: 'com.android.library'

//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"

    //地图模块
    api 'com.google.android.libraries.places:places:2.4.0'
//    api 'com.google.android.gms:play-services-maps:17.0.0'
    //GoogleMap     https://developers.google.com/maps/documentation/android-sdk
    implementation   'com.google.android.gms:play-services-maps:18.0.2'
    implementation   'com.google.android.gms:play-services-location:21.0.1'
}