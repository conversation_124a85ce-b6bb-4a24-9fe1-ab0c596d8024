package com.rrtx.googlemapmodule;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.location.Location;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.libraries.places.api.Places;
import com.google.android.libraries.places.api.model.Place;
import com.google.android.libraries.places.api.model.PlaceLikelihood;
import com.google.android.libraries.places.api.net.FindCurrentPlaceRequest;
import com.google.android.libraries.places.api.net.FindCurrentPlaceResponse;
import com.google.android.libraries.places.api.net.PlacesClient;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GoogleMapManager {

    private static GoogleMapManager sGoogleMapManager;

    private GoogleMapManager() {
    }

    public static GoogleMapManager getInstance() {
        if (sGoogleMapManager == null) {
            synchronized (GoogleMapManager.class) {
                if (sGoogleMapManager == null) {
                    sGoogleMapManager = new GoogleMapManager();
                }
            }
        }
        return sGoogleMapManager;
    }

    private PlacesClient sPlacesClient;

    /**
     * 初始化地图的操作
     *
     * @param application 上下文
     */
    public void initMap(Application application) {
        try {
            ApplicationInfo applicationInfo = application.getPackageManager().getApplicationInfo(application.getPackageName(), PackageManager.GET_META_DATA);
            String googleMapKey = applicationInfo.metaData.getString("google_map_key");
            if (!TextUtils.isEmpty(googleMapKey)) {
                Places.initialize(application, googleMapKey);
            }
            // Create a new PlacesClient instance
            sPlacesClient = Places.createClient(application.getApplicationContext());
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取位置信息
     *
     * @param activity 上下文
     * @return 位置信息
     */
    public void getLocal(Activity activity, LocalCallBack localCallBack) {
        FusedLocationProviderClient fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(activity);
        @SuppressLint("MissingPermission") Task<Location> locationResult = fusedLocationProviderClient.getLastLocation();
        locationResult.addOnCompleteListener(activity, new OnCompleteListener<Location>() {
            @Override
            public void onComplete(@NonNull Task<Location> task) {
                if (task.isSuccessful()) {
                    Location result = task.getResult();
                    if (localCallBack != null) {
                        localCallBack.getLocalBack(result);
                    }
                } else {
                    Log.e("TAG", "Exception: %s", task.getException());
                }
            }
        });
    }


    public interface LocalCallBack {
        void getLocalBack(Location location);
    }
}
