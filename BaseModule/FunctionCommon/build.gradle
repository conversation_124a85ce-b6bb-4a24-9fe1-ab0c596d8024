apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name

        buildConfigField("boolean", "enableLog", "true")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled true
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    resourcePrefix "common_"
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"

    //基础类库
    api project(path: ':BaseModule:RrtxCommon')
    implementation 'com.google.android.material:material:1.3.0'

    //butterKnife
    annotationProcessor "com.jakewharton:butterknife-compiler:${libs.butterknife_compiler}"

    //arouter
    annotationProcessor "com.alibaba:arouter-compiler:${libs.arouter_compiler}"

    api project(path: ':flutter')

    implementation "androidx.lifecycle:lifecycle-runtime:${libs.lifecycle_runtime}"
    implementation "androidx.lifecycle:lifecycle-common-java8:${libs.lifecycle_common_java8}"
    implementation "androidx.lifecycle:lifecycle-extensions:${libs.lifecycle_extensions}"
    implementation "androidx.lifecycle:lifecycle-viewmodel:${libs.lifecycle_viewmodel}"
    implementation "androidx.lifecycle:lifecycle-livedata:${libs.lifecycle_livedata}"
//    implementation "androidx.navigation:navigation-ui:${libs.navigation_ui}"

    kapt "com.jakewharton:butterknife-compiler:${libs.butterknife_compiler}"
    kapt "com.alibaba:arouter-compiler:${libs.arouter_compiler}"
}
