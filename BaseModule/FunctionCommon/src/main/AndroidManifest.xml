<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="om.rrtx.mobile.functioncommon">

    <uses-permission android:name="android.permission.VIBRATE" />
    <application>
        <activity
            android:name=".activity.SelectCurrencyAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.AddBankActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.AddBankSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.SelectBankCardActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.ComResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.DebitResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.UpdateJuniorResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />

        <activity
            android:name=".activity.ConditionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.VerCodeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.RegisterVerCodeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />

    </application>

</manifest>