package om.rrtx.mobile.functioncommon.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.CommonService;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.view.AddBankView;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 转账页面的P层
 */
public class AddBankPresenter extends BasePresenter<AddBankView> {

    private CommonModel mCommonModel;
    private Context mContext;

    public AddBankPresenter(Context context) {
        mCommonModel = new CommonModel();
        mContext = context;
    }

    /**
     * 删除银行卡
     *
     * @param cardNo
     */
    public void requestBindBank(String cardNo, String cardType, String bankNo) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.requestBindBank(userId, cardNo, cardType, bankNo, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().bindBankSuccess(cardNo);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.requestBindBank(userId, cardNo, cardType, bankNo, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().bindBankSuccess(cardNo);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }
}
