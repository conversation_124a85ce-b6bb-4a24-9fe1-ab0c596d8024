package om.rrtx.mobile.functioncommon.adapter

import android.view.View
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.databinding.CommonItemOptionBinding
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.base.BaseRecyclerViewAdapter
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean

class OptionsAdapter : BaseRecyclerViewAdapter<InfoItemBean, CommonItemOptionBinding>() {

    private var selectPosition = -1

    fun getCurPosition() = selectPosition
    override fun getItemId() = R.layout.common_item_option
    override fun handleItemData(binding: CommonItemOptionBinding, position: Int) {
        binding.apply {
            val bean = data[position]
            textTv.isSelected = selectPosition == position
            textTv.text = bean.title
            root.setOnClickListener(object : CustomClickListener() {
                override fun onSingleClick(p0: View?) {
                    selectPosition = position
                    bean.callBack1!!(position)
                    notifyDataSetChanged()
                }
            })
        }
    }
}