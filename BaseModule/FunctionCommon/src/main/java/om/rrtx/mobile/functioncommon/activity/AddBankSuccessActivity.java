package om.rrtx.mobile.functioncommon.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.bean.SelectBankBean;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 添加银行卡成功
 */
public class AddBankSuccessActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.text1Tv)
    TextView mText1Tv;
    @BindView(R2.id.text2Tv)
    TextView mText2Tv;
    @BindView(R2.id.text3Tv)
    TextView mText3Tv;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    private SelectBankBean mSelectBankBean;
    private String mCardNo;

    public static void jumpAddBankSuccess(Context context, String toJson, String cardNo) {
        Intent intent = new Intent(context, AddBankSuccessActivity.class);
        intent.putExtra(CommonConstants.Transmit.DETAILSBEAN, toJson);
        intent.putExtra(CommonConstants.Transmit.CARDNO, cardNo);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String selectBeanJson = getIntent().getStringExtra(CommonConstants.Transmit.DETAILSBEAN);
        mCardNo = getIntent().getStringExtra(CommonConstants.Transmit.CARDNO);
        if (!TextUtils.isEmpty(selectBeanJson)) {
            mSelectBankBean = new Gson().fromJson(selectBeanJson, SelectBankBean.class);
        }

    }

    @Override
    protected int createContentView() {
        return R.layout.common_activity_add_bank_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setVisibility(View.VISIBLE);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bankcard_title_bankcard_bind);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
    }


    @Override
    public void initDate() {
        super.initDate();

        if (mSelectBankBean != null) {

            String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
            mText1Tv.setText(userName);

            mText2Tv.setText(mSelectBankBean.getBankName());

            mText3Tv.setText(StringUtils.stringBankModileMask(mCardNo));
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mDoneTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                //发送广播刷新页面
                Intent intent = new Intent(CommonConstants.REFRESHBANKSACTION);
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
                finish();
            }
        });
    }
}
