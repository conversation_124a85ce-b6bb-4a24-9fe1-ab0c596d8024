package om.rrtx.mobile.functioncommon.dialog.type;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.WithdrawalOrderBean;
import om.rrtx.mobile.functioncommon.dialog.CashierBankListFragment;
import om.rrtx.mobile.functioncommon.dialog.CashierPayImpl;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收银台转账的实现
 */
public class CashierCashOutTransferImpl implements CashierPayImpl {
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean bean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public CashierCashOutTransferImpl(AppCompatActivity activity, CashierOrderInfoBean cashierOrderInfoBean, PayMethodLiveData payMethodLiveData) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        bean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
    }

    @Override
    public void showInfo(View rootView) {
        TextView amtTv = rootView.findViewById(R.id.amtTv);
        TextView tab1 = rootView.findViewById(R.id.tab1);
        TextView tab1Tv = rootView.findViewById(R.id.tab1Tv);
        TextView tab2 = rootView.findViewById(R.id.tab2);
        TextView tab2Tv = rootView.findViewById(R.id.tab2Tv);
        TextView paymentMethod = rootView.findViewById(R.id.paymentMethod);
        TextView paymentMethodTv = rootView.findViewById(R.id.paymentMethodTv);
        ImageView paymentMethodIv = rootView.findViewById(R.id.paymentMethodIv);
        View paymentMethodView = rootView.findViewById(R.id.paymentMethodView);

        String currency = CurrencyUtils.setCurrency(mActivity, bean.getCurrency()) + " ";
        //设置订单金额
        amtTv.setText(currency + StringUtils.formatAmount(bean.getOrderAmt()));
        //设置商户名称
        tab1.setVisibility(View.VISIBLE);
        tab1Tv.setVisibility(View.VISIBLE);
        tab1.setText(R.string.order_info);
        tab1Tv.setText(bean.getOrderInfo());
        tab2.setVisibility(View.VISIBLE);
        tab2Tv.setVisibility(View.VISIBLE);
        tab2.setText(R.string.branch_name);
        tab2Tv.setText(bean.getBankName()+"("+bean.getBankNo()+")");

        paymentMethod.setVisibility(View.VISIBLE);
        paymentMethodTv.setVisibility(View.VISIBLE);
        paymentMethodTv.setText(mActivity.getResources().getString(R.string.oneMoney) + "-" + bean.getCurrency());
        paymentMethodTv.setEnabled(false);
        paymentMethodTv.setTextColor(mActivity.getResources().getColor(R.color.color_212121));
        paymentMethodIv.setVisibility(View.GONE);
        paymentMethodView.setVisibility(View.GONE);

        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            pay(psd);
        });
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        CashierBankListFragment cashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(cashierOrderInfoBean));
        cashierBankListFragment.show(baseBottomFragment.getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
    }

    @Override
    public void getPayMethod() {
    }

    @Override
    public void nextClick(String isFreePwd, BaseBottomFragment baseBottomFragment) {
        mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(bean));
        mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
    }

    private void pay(String psd) {
        if (mIsRequestFinish || psd == null) {
            return;
        }
        mIsRequestFinish = true;

        mCommonModel.requestCashOutOrder(
                bean.getBankNo(), bean.getOrderAmt(), psd, CommonConstants.PaymentPasswordType.PSD, bean.getPaymentToken(),
                bean.getCurrency(),
                new BaseObserver<WithdrawalOrderBean>(mActivity) {
                    @Override
                    public void requestSuccess(WithdrawalOrderBean sResData) {
                        if (mPayPsdBottomFragment != null) {
                            mPayPsdBottomFragment.dismiss();
                        }
                        CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                        mIsRequestFinish = false;
                    }

                    @Override
                    public void requestFail(String sResMsg) {
                        if (mPayPsdBottomFragment != null) {
                            mPayPsdBottomFragment.cancelPassword();
                        }
                        ToastUtil.show(mActivity, sResMsg);
                        mIsRequestFinish = false;
                    }
                });

        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.cancelPassword();
        }
    }

    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
