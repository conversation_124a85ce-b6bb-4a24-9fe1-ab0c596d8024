package om.rrtx.mobile.functioncommon.activity;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.adapter.SelectBankRVAdapter;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.bean.SelectBankBean;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;

public class SelectBankCardActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.contentRv)
    RecyclerView mContentRv;
    private SelectBankRVAdapter mAdapter;
    private SelectBankBean mSelectBankBean;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String selectBeanJson = getIntent().getStringExtra(CommonConstants.Transmit.DETAILSBEAN);
        if (!TextUtils.isEmpty(selectBeanJson)) {
            mSelectBankBean = new Gson().fromJson(selectBeanJson, SelectBankBean.class);
        }
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.common_activity_select_bank_card;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.common_ic_back_black);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bankcard_title_bankcard_bind);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        mContentRv.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new SelectBankRVAdapter(mContext);
        mContentRv.setAdapter(mAdapter);
    }

    @Override
    public void initDate() {
        super.initDate();
        if (mSelectBankBean != null) {
            mAdapter.setSelectItem(mSelectBankBean);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                finish();
            }
        });

        mAdapter.setClickListener(new RVAdapterItemClickListener<SelectBankBean>() {
            @Override
            public void itemClickListener(SelectBankBean selectBankBean, int position) {
                Intent intent = new Intent();
                String selectBeanJson = new Gson().toJson(selectBankBean);
                intent.putExtra(CommonConstants.Transmit.DETAILSBEAN, selectBeanJson);
                setResult(RESULT_OK, intent);
                finish();
            }
        });
    }
}
