package om.rrtx.mobile.functioncommon.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.adapter.CurrencyListRvAdapter;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;


/**
 * <AUTHOR> zfw
 * @time : 2023/9/8 10:47
 * @desc :
 */
public class CustomBottomDialog extends Dialog implements RVAdapterItemClickListener<String> {
    private int selectPositiom = 0;
    private String selectValue = "For First Term";
    TextView mConfirmTv;
    TextView mCancelTv;
    RecyclerView mListRv;
    private String mType;
    private Context mContext;
    private CommonModel mCommonModel;
    private CurrencyListRvAdapter mAdapter;
    OnItemClickListener listener;

    public CustomBottomDialog(@NonNull Context context, String type) {
        super(context, R.style.transparentBottomDialog);
        mContext = context;
        this.mType = type;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.common_dialog_currency_list);

        setCancelable(true);
        setCanceledOnTouchOutside(true);

        setLocation();

        initView();

        initListener();

        initData();
    }

    private void initData() {
        mCommonModel = new CommonModel();
        if (!TextUtils.isEmpty(mType) && "2".equals(mType)) {
            List<String> mList = new ArrayList<>();
            mList.add(mContext.getString(R.string.first_term));
            mList.add(mContext.getString(R.string.second_term));
            mList.add(mContext.getString(R.string.third_term));
            mAdapter.setDataList(mList);
        }
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.BOTTOM);
            window.setAttributes(WL);
        }
    }

    /**
     * 初始化控件
     */
    private void initView() {
        mCancelTv = findViewById(R.id.tv_cancel);
        mConfirmTv = findViewById(R.id.tv_confirm);
        mListRv = findViewById(R.id.selectRv);

    }

    private void initListener() {
        mListRv.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new CurrencyListRvAdapter(mContext);
        mAdapter.setListener(this);
        mListRv.setAdapter(mAdapter);
        mCancelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mConfirmTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                dismiss();
                if (listener != null) {
                    listener.onSelect(selectValue, String.valueOf(selectPositiom+1));
                }
            }
        });
    }

    @Override
    public void itemClickListener(String s, int position) {
        selectPositiom=position;
        selectValue=s;
        /*dismiss();
        if (listener != null) {
            listener.onSelect(s, String.valueOf(position+1));
        }*/
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    public interface OnItemClickListener {
        void onSelect(String value, String position);
    }
}
