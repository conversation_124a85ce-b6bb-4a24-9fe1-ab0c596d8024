package om.rrtx.mobile.functioncommon.bean;

/**
 * <AUTHOR>
 * 收银台支付订单实体类
 * 这里主要是把收银台进行支付所需要的参数进行整理!
 */
public class CashierOrderInfoBean {

    private boolean isFixMethod;
    /**
     * 订单支付类型
     * 这个主要是为了区分相应的支付类型
     */
    private String payType;
    /**
     * 商户号
     */
    private String merNo;
    /**
     * 商户名称
     */
    private String merName;
    /**
     * 支付类型
     */
    private String paymentProduct;
    /**
     * 订单金额 -> 支付方式需要使用
     */
    private String orderAmt;
    private String cashbackAmt;
    /**
     * 订单金额 -> 支付方式需要使用
     */
    private String orderAmount;
    /**
     * 外部订单号 -> 这个先算作订单号吧
     */
    private String outOrderNo;
    /**
     * 外部订单号 -> orderSource为1时必须
     */
    private String orderSource;
    /**
     * 订单信息
     */
    private String orderInfo;

    private String setmeal;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 银行卡算是类型吧
     */
    private String bankNo;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 支付总额
     */
    private String actAmount;
    /**
     * 费率金额
     */
    private String transFeeAmt;

    /**
     * 税费金额
     */
    private String transTaxAmt;
    private String userId;
    /**
     * 费率金额
     */
    private String payFeeAmount;
    /**
     * 商户费率
     */
    private String merFeeAmount;
    /**
     * 币种
     */
    private String currency;
    /**
     * 事务类型
     */
    private String transType;

    /**
     * 转账-> 电话
     */
    private String mobile;
    /**
     * 转账-> 留言
     */
    private String remark;
    private String remittanceCode;
    /**
     * 转账-> 支付凭证
     */
    private String transferToken;
    /**
     * 支付-> 二维码
     */
    private String qrCode;
    /**
     * 支付-> 支付的token
     */
    private String paymentToken;

    private String orderNo;

    private String paymentType;
    /**
     * 卡号
     */
    private String outType;


    private String isEnoughPay;
    private String meterNo;
    private String billSubType;
    private String planCode;
    private String bundleId;

    public boolean isFixMethod() {
        return isFixMethod;
    }

    public void setFixMethod(boolean fixMethod) {
        isFixMethod = fixMethod;
    }

    /*public boolean getIsFixMethod() {
        return isFixMethod;
    }*/

    private String afterDisCountAmt;


    public String getUserId() {
        return userId;
    }

    public String getAfterDisCountAmt() {
        return afterDisCountAmt;
    }

    public void setAfterDisCountAmt(String afterDisCountAmt) {
        this.afterDisCountAmt = afterDisCountAmt;
    }

    public String getCashbackAmt() {
        return cashbackAmt;
    }

    public void setCashbackAmt(String cashbackAmt) {
        this.cashbackAmt = cashbackAmt;
    }

    public String getBillSubType() {
        return billSubType;
    }

    public String getPlanCode() {
        return planCode;
    }

    public String getBundleId() {
        return bundleId;
    }

    public String getSetmeal() {
        return setmeal;
    }

    public String getOrderAmount() {
        return orderAmount;
    }


    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getPaymentProduct() {
        return paymentProduct;
    }

    public void setPaymentProduct(String paymentProduct) {
        this.paymentProduct = paymentProduct;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getActAmount() {
        return actAmount;
    }

    public void setActAmount(String actAmount) {
        this.actAmount = actAmount;
    }

    public String getTransFeeAmt() {
        return transFeeAmt;
    }

    public String getTransTaxAmt() {
        return transTaxAmt;
    }

    public void setTransFeeAmt(String transFeeAmt) {
        this.transFeeAmt = transFeeAmt;
    }

    public void setTransTaxAmt(String transTaxAmt) {
        this.transTaxAmt = transTaxAmt;
    }

    public String getPayFeeAmount() {
        return payFeeAmount;
    }


    public String getMerFeeAmount() {
        return merFeeAmount;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getRemittanceCode() {
        return remittanceCode;
    }

    public void setRemittanceCode(String remittanceCode) {
        this.remittanceCode = remittanceCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTransferToken() {
        return transferToken;
    }


    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getPaymentToken() {
        return paymentToken;
    }


    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getOutType() {
        return outType;
    }

    public void setOutType(String outType) {
        this.outType = outType;
    }

    public CashierOrderInfoBean(Builder builder) {
        this.payType = builder.payType;
        this.merNo = builder.merNo;
        this.merName = builder.merName;
        this.paymentProduct = builder.paymentProduct;
        this.orderAmt = builder.orderAmt;
        this.outOrderNo = builder.outOrderNo;
        this.orderInfo = builder.orderInfo;
        this.bankName = builder.bankName;
        this.cardNo = builder.cardNo;
        this.actAmount = builder.actAmount;
        this.transFeeAmt = builder.transFeeAmt;
        this.transTaxAmt = builder.transTaxAmt;
        this.payFeeAmount = builder.payFeeAmount;
        this.transType = builder.transType;
        this.currency = builder.currency;
        this.mobile = builder.mobile;
        this.remark = builder.remark;
        this.transferToken = builder.transferToken;
        this.qrCode = builder.qrCode;
        this.paymentToken = builder.paymentToken;
        this.bankNo = builder.bankNo;
        this.orderSource = builder.orderSource;
        this.orderNo = builder.orderNo;
        this.paymentType = builder.paymentType;
        this.outType = builder.outType;
        this.isEnoughPay = builder.isEnoughPay;
        this.merNo = builder.merNo;
        this.setmeal = builder.Setmeal;
        this.billSubType = builder.billSubType;
        this.planCode = builder.planCode;
        this.bundleId = builder.bundleId;
        this.cashbackAmt = builder.cashbackAmt;
        this.afterDisCountAmt = builder.afterDisCountAmt;
        this.isFixMethod = builder.isFixMethod;
        this.userId = builder.userId;
    }


    public static class Builder {
        private String billSubType;
        private String planCode = "";
        private String bundleId = "";

        private String userId = "";
        /**
         * 订单支付类型
         */
        private String payType;
        /**
         * 商户号
         */
        private String merNo;
        /**
         * 商户名称
         */
        private String merName;
        /**
         * 支付类型
         */
        private String paymentProduct;
        /**
         * 订单金额 -> 支付方式需要使用
         */
        private String orderAmt;
        /**
         * 外部订单号 -> orderSource为1时必须
         */
        private String outOrderNo;
        /**
         * 支付-> 这个先算作订单号吧
         */
        private String orderSource;
        /**
         * 订单信息
         */
        private String orderInfo;
        /**
         * 银行卡名称
         */
        private String bankName;
        /**
         * 银行卡算是类型吧
         */
        private String bankNo;
        /**
         * 卡号
         */
        private String cardNo;
        /**
         * 支付总额 实付
         */
        private String actAmount;

        /**
         * 优惠后金额
         */
        private String afterDisCountAmt;

        /**
         * 默认固定
         */
        private boolean isFixMethod = true;

        /**
         * 费率金额
         */
        private String transFeeAmt;

        /**
         * 税率金额
         */
        private String transTaxAmt;

        /**
         * 费率金额
         */
        private String payFeeAmount;

        /**
         * 商户费率
         */
        private String merFeeAmount;
        /**
         * 币种
         */
        private String currency;
        /**
         * 事务类型
         */
        private String transType;
        /**
         * 转账-> 电话
         */
        private String mobile;
        /**
         * 转账-> 留言
         */
        private String remark;
        /**
         * 转账-> 支付凭证
         */
        private String transferToken;
        /**
         * 支付-> 二维码
         */
        private String qrCode;
        /**
         * 支付-> 支付的token
         */
        private String paymentToken;

        private String orderNo;

        private String paymentType;
        private String outType;


        private String isEnoughPay;

        private String meterNo;
        private String Setmeal;

        private String cashbackAmt;


        public Builder setIsFixMethod(boolean isFixMethod) {
            this.isFixMethod = isFixMethod;
            return this;
        }

        public String getAfterDisCountAmt() {
            return afterDisCountAmt;
        }

        public Builder setAfterDisCountAmt(String afterDisCountAmt) {
            this.afterDisCountAmt = afterDisCountAmt;
            return this;
        }

        public String getCashbackAmt() {
            return cashbackAmt;
        }

        public Builder setCashbackAmt(String cashbackAmt) {
            this.cashbackAmt = cashbackAmt;
            return this;
        }

        public Builder setBillSubType(String billSubType) {
            this.billSubType = billSubType;
            return this;
        }

        public Builder setPlanCode(String planCode) {
            this.planCode = planCode;
            return this;
        }

        public Builder setBundleId(String bundleId) {
            this.bundleId = bundleId;
            return this;
        }

        public Builder setSetmeal(String setmeal) {
            Setmeal = setmeal;
            return this;
        }

        public Builder setMeterNo(String meterNo) {
            this.meterNo = meterNo;
            return this;
        }

        public String getEnoughPay() {
            return isEnoughPay;
        }

        public Builder setEnoughPay(String enoughPay) {
            isEnoughPay = enoughPay;
            return this;
        }

        public Builder setMerNo(String merNo) {
            this.merNo = merNo;
            return this;
        }

        public Builder setPaymentProduct(String paymentProduct) {
            this.paymentProduct = paymentProduct;
            return this;
        }

        public Builder setOrderAmt(String orderAmt) {
            this.orderAmt = orderAmt;
            return this;
        }

        public Builder setOutOrderNo(String outOrderNo) {
            this.outOrderNo = outOrderNo;
            return this;
        }

        public Builder setOrderInfo(String orderInfo) {
            this.orderInfo = orderInfo;
            return this;
        }

        public Builder setUserId(String userId) {
            this.userId = userId;
            return this;
        }

        public Builder setBankName(String bankName) {
            this.bankName = bankName;
            return this;
        }

        public Builder setCardNo(String cardNo) {
            this.cardNo = cardNo;
            return this;
        }

        public Builder setActAmt(String actAmount) {
            this.actAmount = actAmount;
            return this;
        }

        public Builder setFeeAmount(String feeAmount) {
            this.transFeeAmt = feeAmount;
            return this;
        }

        public Builder setTransTaxAmt(String transTaxAmt) {
            this.transTaxAmt = transTaxAmt;
            return this;
        }

        public Builder setTransFeeAmt(String transTaxAmt) {
            this.transFeeAmt = transTaxAmt;
            return this;
        }

        public Builder setPayFeeAmount(String payFeeAmount) {
            this.payFeeAmount = payFeeAmount;
            return this;
        }

        public Builder setMerFeeAmount(String merFeeAmount) {
            this.merFeeAmount = merFeeAmount;
            return this;
        }

        public Builder setPayType(String payType) {
            this.payType = payType;
            return this;
        }

        public Builder setCurrency(String currency) {
            this.currency = currency;
            return this;
        }

        public Builder setMerName(String merName) {
            this.merName = merName;
            return this;
        }

        public Builder setMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Builder setRemark(String remark) {
            this.remark = remark;
            return this;
        }

        public Builder setTransferToken(String transferToken) {
            this.transferToken = transferToken;
            return this;
        }

        public Builder setTransType(String transType) {
            this.transType = transType;
            return this;
        }

        public Builder setQrCode(String qrCode) {
            this.qrCode = qrCode;
            return this;
        }

        public Builder setPaymentToken(String paymentToken) {
            this.paymentToken = paymentToken;
            return this;
        }

        public Builder setBankNo(String bankNo) {
            this.bankNo = bankNo;
            return this;
        }

        public Builder setOrderSource(String orderSource) {
            this.orderSource = orderSource;
            return this;
        }

        public Builder setOrderNo(String orderNo) {
            this.orderNo = orderNo;
            return this;
        }

        public Builder setPaymentType(String paymentType) {
            this.paymentType = paymentType;
            return this;
        }

        public Builder setOutType(String outType) {
            this.outType = outType;
            return this;
        }

        public CashierOrderInfoBean builder() {
            return new CashierOrderInfoBean(this);
        }
    }
}
