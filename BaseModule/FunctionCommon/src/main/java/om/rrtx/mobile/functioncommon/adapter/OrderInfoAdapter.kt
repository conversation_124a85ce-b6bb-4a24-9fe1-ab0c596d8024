package om.rrtx.mobile.functioncommon.adapter

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.bean.OrderInfoItemBean
import om.rrtx.mobile.functioncommon.databinding.ItemOrderInfoBinding
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper

class OrderInfoAdapter :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var context: Context

    private var data = arrayListOf<OrderInfoItemBean>()
    fun setData(data: ArrayList<OrderInfoItemBean>) {
        this.data = data
        notifyDataSetChanged()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemOrderInfoBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemOrderInfoBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.titleTv.text = bean.title
        val hint = bean.hint
        // 这里是保险起见，正常n/a数据在传入前就处理了，被这段逻辑处理的n/a都是异常数据
        binding.contentTv.text = if (TextUtils.isEmpty(bean.content)) "N/A" else bean.content
        if (hint != "" || (bean.isEnough != null && bean.isEnough != "1")) {
            binding.hintTv.visibility = View.VISIBLE
            if (bean.hint != "") {
                binding.hintTv.text = hint
                binding.hintTv.setTextColor(ResourceHelper.getColor(R.color.common_text_86909C))
            } else {
                binding.hintTv.setTextColor(ResourceHelper.getColor(R.color.common_red_F23D1D))
            }
        } else {
            binding.hintTv.visibility = View.GONE
        }

        if (bean.isFixMethod) {
            binding.moreIv.visibility = View.GONE
        } else {
            binding.moreIv.visibility = View.VISIBLE
            binding.root.setOnClickListener(object : CustomClickListener() {
                override fun onSingleClick(view: View?) {
                    bean.callback()
                }
            })
        }


    }
}