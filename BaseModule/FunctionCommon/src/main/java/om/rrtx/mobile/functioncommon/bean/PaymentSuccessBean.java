package om.rrtx.mobile.functioncommon.bean;

/**
 * <AUTHOR>
 */
public class PaymentSuccessBean {

    /**
     * merNo : 8154915410110590983
     * merName : shanghuershisi
     * orderAmount : 112.00
     * actAmount : 122.00
     * feeAmount : 10.00
     * merFeeAmount : 0.00
     * payFeeAmount : 10.00
     */

    private String transStatus;
    private String merNo;
    private String merName;
    private String orderAmount;
    private String actAmount;
    private String feeAmount;
    private String merFeeAmount;
    private String payFeeAmount;
    private String currency;
    private String orderStatus;
    private String paymentAmt;
    private String trxStatus;
    private String buyToken;
    private String token;

    public String getTransStatus() {
        return transStatus;
    }

    public void setTransStatus(String transStatus) {
        this.transStatus = transStatus;
    }

    public String getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(String trxStatus) {
        this.trxStatus = trxStatus;
    }

    public String getBuyToken() {
        return buyToken;
    }

    public void setBuyToken(String buyToken) {
        this.buyToken = buyToken;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getPaymentAmt() {
        return paymentAmt;
    }

    public void setPaymentAmt(String paymentAmt) {
        this.paymentAmt = paymentAmt;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getActAmount() {
        return actAmount;
    }

    public void setActAmount(String actAmount) {
        this.actAmount = actAmount;
    }

    public String getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(String feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getMerFeeAmount() {
        return merFeeAmount;
    }

    public void setMerFeeAmount(String merFeeAmount) {
        this.merFeeAmount = merFeeAmount;
    }

    public String getPayFeeAmount() {
        return payFeeAmount;
    }

    public void setPayFeeAmount(String payFeeAmount) {
        this.payFeeAmount = payFeeAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
