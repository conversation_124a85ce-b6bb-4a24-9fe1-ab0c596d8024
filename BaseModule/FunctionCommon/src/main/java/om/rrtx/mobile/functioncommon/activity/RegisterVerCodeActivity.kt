package om.rrtx.mobile.functioncommon.activity

import android.app.Activity
import android.content.Intent
import android.text.method.PasswordTransformationMethod
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.common_base_title.backIv
import kotlinx.android.synthetic.main.common_base_title.leftBg
import kotlinx.android.synthetic.main.common_base_title.titleTv
import kotlinx.android.synthetic.main.common_register_ver_code.code
import kotlinx.android.synthetic.main.common_register_ver_code.hint2_tv
import kotlinx.android.synthetic.main.common_register_ver_code.mobile_tv
import kotlinx.android.synthetic.main.common_register_ver_code.register_con
import kotlinx.android.synthetic.main.common_register_ver_code.tv_all_step
import kotlinx.android.synthetic.main.common_register_ver_code.tv_cur_page
import kotlinx.android.synthetic.main.common_register_ver_code.tv_cur_step
import kotlinx.android.synthetic.main.common_register_ver_code.tv_down_time
import kotlinx.android.synthetic.main.common_register_ver_code.tv_send
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.databinding.CommonRegisterVerCodeBinding
import om.rrtx.mobile.functioncommon.utils.CodeDownTimerUtils
import om.rrtx.mobile.functioncommon.vm.CommonViewModel
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.RxKeyPadFragment
import om.rrtx.mobile.rrtxcommon1.widget.RxKeyPadView
import om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText

class RegisterVerCodeActivity : BaseVVMActivity<CommonViewModel, CommonRegisterVerCodeBinding>(),
    VerificationCodeEditText.VerificationCallBack,
    RxKeyPadView.KeyPadCallBack,
    RxKeyPadFragment.KeyPadFragmentBack {

    private var mobile: String = ""
    private var mflag: String = ""
    private var verCode: String = ""

    private lateinit var mKeyPadFragment: RxKeyPadFragment

    override fun createContentView() = R.layout.common_register_ver_code

    override fun doGetExtra() {
        mobile = intent.getStringExtra(BaseConstants.Transmit.MOBILE).toString()
        mflag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun initView() {
        when (mflag) {
            BaseConstants.JumpFlag.Register_Account -> {
                titleTv.setText(R.string.register_title_user_registration)
                hint2_tv.setText(R.string.send_ver_code)
                register_con.visibility = View.VISIBLE
                tv_cur_step.setText(R.string.mobile_Verification)
                tv_cur_page.text = "3"
                tv_all_step.text = "/6"
                code.isCursorVisible = true
                code.requestFocus()
            }

            BaseConstants.JumpFlag.Register_junior_Account -> {
                titleTv.setText(R.string.register_junior)
                hint2_tv.setText(R.string.send_junior_code)
                register_con.visibility = View.VISIBLE
                tv_cur_step.setText(R.string.mobile_Verification)
                tv_cur_page.text = "2"
                tv_all_step.text = "/5"
                code.isCursorVisible = true
                code.requestFocus()
            }
        }
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)

        mobile_tv.text = getString(R.string.jia_263)+ " " + mobile
        /*if (BaseConstants.JumpFlag.Register_junior_Account == mflag) {
            mobile_tv.text = getString(R.string.jia_263)+ " " + mobile
        } else {
            mobile_tv.text = StringUtils.stringMask(getString(R.string.nommal_263), mobile)
        }*/

        //让EditText失去焦点，然后获取点击事件
        //code.isFocusable = false
        code.setIsPassword(false)
        code.transformationMethod = PasswordTransformationMethod.getInstance()

        code.setVerificationCallBack(this)
    }

    override fun initData() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()

        sendCode()

        leftBg.setOnClickListener {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        code.setText("")
        code.showKeyBoard()
    }

    override fun initClickListener() {

        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    tv_send -> {
                        sendCode()
                        tv_down_time.visibility = View.VISIBLE
                        tv_send.visibility = View.GONE
                    }
                }
            }

        }.apply {
            tv_send.setOnClickListener(this)
        }
    }

    override fun initVMListener() {
        viewModel.mCodeBeanLD.observe(this) {
            if (it.smsCode != null) verCode = it.smsCode
        }
        viewModel.mCodeVerifyLD.observe(this) {
            if (BaseConstants.JumpFlag.Register_junior_Account == mflag){
                jumpSetPin()
            }else{
                ARouter.getInstance().build(ARouterPath.LoginPath.ReguarUserInfoActivity)
                    .withString(BaseConstants.Transmit.JUMPFLAG, mflag).navigation()
            }
        }
    }

    private fun jumpSetPin() {
        ARouter.getInstance()
            .build(ARouterPath.SecurityPath.SetRegisterPinActivity)
            .withString(BaseConstants.Transmit.JUMPFLAG, mflag)
            .withString(BaseConstants.Transmit.PINPAYMENT, BaseConstants.Check.SETFIRST)
            .navigation()
    }

    private fun sendCode() {
        viewModel.requestCode(mobile, "Registration")
        startDownTime()
    }

    private fun startDownTime() {
        tv_send.visibility = View.GONE
        /*downTask = Observable.interval(1, TimeUnit.SECONDS).subscribeOn(Schedulers.io())
            .unsubscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe {
                if (it.toInt() == 60) {
                    downTask.dispose()
                    tv_down_time.visibility = View.GONE
                    tv_send.visibility = View.VISIBLE
                } else {
                    tv_down_time.text = "${(60 - it)}s"
                }
            }*/
        val countDownTimerUtils = CodeDownTimerUtils(tv_down_time, tv_send, 60000, 1000)
        countDownTimerUtils.start()
    }

    override fun onDestroy() {
        super.onDestroy()
        //if (!downTask.isDisposed) downTask.dispose()
    }

    companion object {
        fun jump(context: Activity, mobile: String, flag: String) {
            val intent = Intent(context, RegisterVerCodeActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.MOBILE, mobile)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, flag)
            context.startActivity(intent)
        }
    }

    override fun inputCode(input: String?) {
        code.setText(input)
        if (input!!.length == 6) {
            mKeyPadFragment.dismiss()
            verCode = input
            if (input == verCode) {
                viewModel.requestValidateSms(mobile, input)
            } else {
                ToastUtil.show(mContext, ResourceHelper.getString(R.string.please_check))
            }
            code.text!!.clear()
        }
    }

    override fun onKeyPadPause() {
        if (code.length() < 6) {
            code.setText("")
        }
    }

    override fun showKeyBoard() {
        mKeyPadFragment = RxKeyPadFragment("1")
        mKeyPadFragment.setKeyPadFragmentBack(this)
        code.setText("")
        mKeyPadFragment.show(supportFragmentManager, "")
    }
}