package om.rrtx.mobile.functioncommon.bean;

import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class OrderInfoBean {

    /**
     * MERNO : 8154915410110590983
     * merName : shanghuershisi
     * amt : 123.00
     * orderNo : 8161591893419885078
     * outOrderNo : 123
     * orderStatus : 10
     * commodityRemark : 123
     * currency : USD
     */

    private String recMerNo;
    private String merNo;
    private String merName;
    private String amt;
    private String orderAmt;
    private String orderNo;
    private String outOrderNo;
    private String orderStatus;
    private String commodityRemark;
    private String currency;
    private String trxOrderNo;
    private String transOrderNo = "";
    private String merTradeName = "";
    private String payAmt;
    private String feeAmt;

    public String getRecMerNo() {
        return recMerNo;
    }

    public void setRecMerNo(String recMerNo) {
        this.recMerNo = recMerNo;
    }

    public String getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(String payAmt) {
        this.payAmt = payAmt;
    }

    public String getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(String feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getMerTradeName() {
        return merTradeName;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getTrxOrderNo() {
        return trxOrderNo;
    }

    public void setTrxOrderNo(String trxOrderNo) {
        this.trxOrderNo = trxOrderNo;
    }

    public String getTransOrderNo() {
        return transOrderNo;
    }

    public void setTransOrderNo(String transOrderNo) {
        this.transOrderNo = transOrderNo;
    }


    public void setMerTradeName(String merTradeName) {
        this.merTradeName = merTradeName;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getMerName() {
        if (!StringUtils.isValidString(merName)) return merTradeName;
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCommodityRemark() {
        return commodityRemark;
    }

    public void setCommodityRemark(String commodityRemark) {
        this.commodityRemark = commodityRemark;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "OrderInfoBean{" +
                "recMerNo='" + recMerNo + '\'' +
                ", merNo='" + merNo + '\'' +
                ", merName='" + merName + '\'' +
                ", amt='" + amt + '\'' +
                ", orderAmt='" + orderAmt + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", outOrderNo='" + outOrderNo + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", commodityRemark='" + commodityRemark + '\'' +
                ", currency='" + currency + '\'' +
                ", trxOrderNo='" + trxOrderNo + '\'' +
                ", transOrderNo='" + transOrderNo + '\'' +
                ", merTradeName='" + merTradeName + '\'' +
                ", payAmt='" + payAmt + '\'' +
                ", feeAmt='" + feeAmt + '\'' +
                '}';
    }
}
