package om.rrtx.mobile.functioncommon.dialog.type;

import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;

/**
 * <AUTHOR>
 * 收银台支付的接口实现
 */
public interface CashierPayImpl1 {

    void initData();
    /**
     * 点击支付类型的选择
     *
     * @param baseBottomFragment   因为要启动相应的Dialog所以这里需要创建Fragment来创建FragmentManager
     * @param cashierOrderInfoBean 订单信息,这里可能存在添加银行的操作可能需要重新请求相应的获取支付订单接口
     */
    void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean);

    /**
     * 获取支付方式
     */
    void getPayMethod();

    /**
     * 下一步按钮
     */
    void nextClick(String isFreePwd,BaseBottomFragment baseBottomFragment);

    void onClose();
}
