package om.rrtx.mobile.functioncommon.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;

/**
 * <AUTHOR>
 * 货币适配器
 */
public class CurrencyListRvAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<String> mList;
    private int currentIndex = 0;
    private RVAdapterItemClickListener<String> mListener;

    public CurrencyListRvAdapter(Context context) {
        mContext = context;
        mList = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.common_item_currency_list, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        String curStr = mList.get(position);
        TextView curTv = holder.getView(R.id.curTv);
        curTv.setText(curStr);
        ConstraintLayout view = holder.getView(om.rrtx.mobile.rrtxcommon1.R.id.view);

        if (currentIndex == position) {
            //curTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            curTv.setTextColor(mContext.getResources().getColor(om.rrtx.mobile.rrtxcommon1.R.color.color_000000));
            view.setBackgroundColor(mContext.getResources().getColor(om.rrtx.mobile.rrtxcommon1.R.color.common_bg_f7f8fa));
        } else {
            //curTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            curTv.setTextColor(mContext.getResources().getColor(om.rrtx.mobile.rrtxcommon1.R.color.common_text_86909C));
            view.setBackgroundColor(mContext.getResources().getColor(om.rrtx.mobile.rrtxcommon1.R.color.color_FFFFFF));
        }

        holder.itemView.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                currentIndex = position;
                notifyDataSetChanged();
                LogUtil.e("done", "itemClickListener  curStr: " + curStr);
                if (mListener != null) {
                    mListener.itemClickListener(curStr, position);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    public void setDataList(List<String> strList) {
        mList = strList;
        notifyDataSetChanged();
    }

    public void setListener(RVAdapterItemClickListener<String> listener) {
        mListener = listener;
    }
}
