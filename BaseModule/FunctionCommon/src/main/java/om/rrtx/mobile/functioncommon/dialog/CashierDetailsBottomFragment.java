package om.rrtx.mobile.functioncommon.dialog;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;

import com.google.gson.Gson;

import java.math.BigDecimal;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.dialog.type.CashierAAPaymentImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierAirtimeOrBundleTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierBalanceTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierBillPayTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierCashOutTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierPaymentImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierQueryTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierTopUpImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierTransferImpl;
import om.rrtx.mobile.functioncommon.dialog.type.CashierWithdrawalImpl;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收银台详情页面弹框
 * <p>
 * 因为这里需要适配之后的收银台,所以应该具备以下功能
 * 1. 知道那个页面来的(相当于吊起收银台的类型)
 * 2. 相应的数据,这里我准备封装成json
 * 3. 跳转页面的类型,知道返回的页面是什么
 * <p>
 * 添加了如下参数:
 * 1. 支付类型 -> PAYMENTPRODUCT
 * 2. 订单号 -> payOrderNo
 * 3. 订单类型 -> orderSource
 * 4. 跳转标识 -> JumpFlag
 */
public class CashierDetailsBottomFragment extends BaseBottomFragment {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.paymentMethodTv)
    TextView mPaymentMethodTv;
    @BindView(R2.id.payTv)
    TextView mPayTv;
    @BindView(R2.id.amtTv)
    TextView mAmtTv;
    @BindView(R2.id.order_tab)
    TextView mOrderTab;
    @BindView(R2.id.orderTv)
    TextView mOrderTv;
    @BindView(R2.id.fee_tab)
    TextView mFeeTab;
    @BindView(R2.id.tax_tab)
    TextView mTaxTab;
    @BindView(R2.id.feeTv)
    TextView mFeeTv;
    @BindView(R2.id.taxTv)
    TextView mTaxTv;
    @BindView(R2.id.paymentMethodIv)
    ImageView mPaymentMethodIv;
    private PayMethodLiveData mPayMethodLiveData;
    private DoubleDialog mClosedDialog;
    /**
     * 订单信息的json,主要的目的就是用于展示,和下一步的结算
     */
    private String mOrderInfoJson;
    private CashierBankListFragment mCashierBankListFragment;
    private PaymentBean mPaymentBean;
    /**
     * 传递过来的json转换成的实体类
     */
    private CashierOrderInfoBean mCashierOrderInfoBean;
    /**
     * 支付类型
     */
    private CashierPayImpl cashierPay;

    private View mRootView;

    private String mCurrency;

    public static boolean mIsFirst = true;
    private PaymentBean.PaymentMethodListBean mSelectPaymentMethod;
    private PaymentBean.PaymentTypeListBean mSelectPaymentType;

    public static CashierDetailsBottomFragment newInstance(String orderInfoJson) {
        Bundle args = new Bundle();
        //这里的orderInfo就需要包含订单信息了
        args.putString(CommonConstants.Transmit.CASHIERORDERINFO, orderInfoJson);
        CashierDetailsBottomFragment fragment = new CashierDetailsBottomFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        try {
            //在每个add事务前增加一个remove事务，防止连续的add
            manager.beginTransaction().remove(this).commit();
            super.show(manager, tag);
        } catch (Exception e) {
            //同一实例使用不同的tag会异常,这里捕获一下
            e.printStackTrace();
        }
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.common_dialog_fragment_details_bottom;
    }

    @Override
    public BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView(View rootView) {
        setCancelable(false);

        mRootView = rootView;

        //设置标题
        mTitleTv.setText(getString(R.string.Payment_Details));

        //订单信息
        Bundle arguments = getArguments();
        if (arguments != null) {
            mOrderInfoJson = arguments.getString(CommonConstants.Transmit.CASHIERORDERINFO);
            LogUtil.e("done", "initView: " + mOrderInfoJson);
        }
    }

    @Override
    public void initDate() {
        super.initDate();

        if (getActivity() != null) {
            mPayMethodLiveData = new ViewModelProvider(getActivity()).get(PayMethodLiveData.class);
            //直接展示订单信息
            if (!TextUtils.isEmpty(mOrderInfoJson)) {
                LogUtil.e("TAG", "initDate: " + mOrderInfoJson);
                mCashierOrderInfoBean = new Gson().fromJson(mOrderInfoJson, CashierOrderInfoBean.class);
                mCurrency = mCashierOrderInfoBean.getCurrency();
                String payType = mCashierOrderInfoBean.getPayType();
                if (TextUtils.isEmpty(payType)) {
                    throw new RuntimeException("没有配置payType");
                }

                switch (payType) {
                    case CommonConstants.CashierPaymentType.Cashier_Transfer:
                        //转账
                        cashierPay = new CashierTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        //获取支付方式
                        cashierPay.getPayMethod();
                        break;
                    case CommonConstants.CashierPaymentType.Cashier_Payment:
                    case CommonConstants.CashierPaymentType.Cashier_Mer:
                        //支付
                        cashierPay = new CashierPaymentImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        //获取支付方式
                        cashierPay.getPayMethod();
                        break;
                    case CommonConstants.CashierPaymentType.Cashier_AASplit:
                        //AA转账
                        cashierPay = new CashierAAPaymentImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        //获取支付方式
                        cashierPay.getPayMethod();
                        break;
                    case CommonConstants.CashierPaymentType.ZIPIT:
                    case CommonConstants.CashierPaymentType.Cashier_Withdrawal:
                        //提现
                        cashierPay = new CashierWithdrawalImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        cashierPay.getPayMethod();
                        break;
                    case CommonConstants.CashierPaymentType.CASH_OUT:
                        cashierPay = new CashierCashOutTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        mPayTv.setEnabled(true);
                        mOrderTab.setVisibility(View.GONE);
                        mOrderTv.setVisibility(View.GONE);
                        break;
                    case CommonConstants.CashierPaymentType.Cashier_TopUp:
                        // 充值
                        cashierPay = new CashierTopUpImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        //获取支付方式
                        cashierPay.getPayMethod();
                        break;
                    case CommonConstants.CashierPaymentType.JUNIOR_Query_Transfer:
                    case CommonConstants.CashierPaymentType.Query_Transfer:
                        // 交易查询
                        cashierPay = new CashierQueryTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        break;
                    case CommonConstants.CashierPaymentType.JUNIOR_Query_BALANCE:
                    case CommonConstants.CashierPaymentType.Query_BALANCE:
                        // 余额查询
                        cashierPay = new CashierBalanceTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        break;
                    case CommonConstants.CashierPaymentType.ZESA_FEE:
                        // 电费
                        cashierPay = new CashierBillPayTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        break;
                    case CommonConstants.CashierPaymentType.AIRTIME_BUNDLE:
                        // 话费
                        cashierPay = new CashierAirtimeOrBundleTransferImpl((AppCompatActivity) getActivity(), mCashierOrderInfoBean, mPayMethodLiveData);
                        cashierPay.showInfo(mRootView);
                        break;
                    default:
                }
            }

            //监听支付类型的改变
            mPayMethodLiveData.getPaymentTypeLV().observe(getActivity(), paymentTypeBean -> {
                //对支付方式进行复制
                mPaymentBean = paymentTypeBean;
                //处理支付类型
//                paymentTypeBean.getPaymentMethodList().get(0).setIsBalanceEnough("0");
                if (mPaymentBean.getPaymentMethodList().size()>0) {
                    mSelectPaymentMethod = PaymentTypeHelper.getSelectPayMethod(mPaymentBean.getPaymentMethodList());
                }
                mSelectPaymentType = PaymentTypeHelper.getPaymentType(mSelectPaymentMethod, mPaymentBean.getPaymentTypeList());
                addTaxAndFee(paymentTypeBean);
                // 不论余额是否足够都需要显示
                setTaxAndFee();

                mPayTv.setEnabled(mSelectPaymentMethod.getIsBalanceEnough().equals("1"));

                //设置支付名称
                mPaymentMethodTv.setText(PaymentTypeHelper.getPaymentMethodName(mContext, mSelectPaymentMethod));
                if (isAdded()) {
                    mPaymentMethodTv.setTextColor(getResources().getColor(R.color.color_212121));
                }


                //这里要设置支付方式
                mCashierOrderInfoBean.setPaymentType(mSelectPaymentMethod.getPaymentType());
                if (StringUtils.isValidString(mSelectPaymentMethod.getCardId())) {
                    mCashierOrderInfoBean.setCardNo(mSelectPaymentMethod.getCardId());
                }

                String isFreePwd = mSelectPaymentMethod.getFreePwd();
                if (StringUtils.isValidString(isFreePwd) && isFreePwd.equals("1")) {
                    mPayTv.setText(R.string.no_IN_Payment);
                } else {
                    mPayTv.setText(R.string.pay);
                }

                if (CommonConstants.CashierPaymentType.ZIPIT.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.Query_BALANCE.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.JUNIOR_Query_BALANCE.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.Query_Transfer.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.JUNIOR_Query_Transfer.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.Cashier_TopUp.equals(mCashierOrderInfoBean.getPayType())
                        || CommonConstants.CashierPaymentType.CASH_OUT.equals(mCashierOrderInfoBean.getPayType())) {
                    mOrderTab.setVisibility(View.GONE);
                    mOrderTv.setVisibility(View.GONE);
                } else {
                    mOrderTab.setVisibility(View.VISIBLE);
                    mOrderTv.setVisibility(View.VISIBLE);
                }
//                else {
//                    mPaymentMethodTv.setText(R.string.checkout_btn_please);
//                    mPayTv.setEnabled(false);
//                    if (isAdded()) {
//                        mPaymentMethodTv.setTextColor(getResources().getColor(R.color.color_999999));
//                    }
//                    mPayTv.setBackgroundResource(R.drawable.common_unusable_btn);
//                }

                switch (mCashierOrderInfoBean.getPayType()) {
                    // 固定账户，特殊处理
                    case CommonConstants.CashierPaymentType.ZIPIT:
                    case CommonConstants.CashierPaymentType.JUNIOR_Query_Transfer:
                    case CommonConstants.CashierPaymentType.Query_Transfer:
                    case CommonConstants.CashierPaymentType.JUNIOR_Query_BALANCE:
                    case CommonConstants.CashierPaymentType.Query_BALANCE:
                    case CommonConstants.CashierPaymentType.Cashier_Withdrawal:
                        PaymentBean.PaymentMethodListBean paymentMethod = mSelectPaymentMethod;
                        TextView payHint_tv = mRootView.findViewById(R.id.payHint_tv);
                        // 1 的话走上面正常逻辑就好了
                        if (paymentMethod.getIsBalanceEnough().equals("0")) {
                            mPaymentMethodTv.setText(PaymentTypeHelper.getPaymentMethodName(mContext, paymentMethod));
                            if (isAdded()) {
                                mPaymentMethodTv.setTextColor(getResources().getColor(R.color.color_212121));
                            }
                            payHint_tv.setVisibility(View.VISIBLE);
                        } else {
                            payHint_tv.setVisibility(View.GONE);
                        }
                        break;
                }

            });

            mPayMethodLiveData.getErrorLv().observe(getActivity(), s ->
                    ToastUtil.show(getActivity(), s)
            );

            mPayMethodLiveData.mDismiss.observe(this, aBoolean -> {
                if (aBoolean) {
                    goPayClose();
                    mPayMethodLiveData.mDismiss.setValue(false);
                }
            });
        }
    }

    private void setTaxAndFee() {
        //设置费率、税费
        String currency = CurrencyUtils.setCurrency(mContext, mCurrency) + " ";
        String feeAmount = mCashierOrderInfoBean.getTransFeeAmt();
        // todo 未处理格式化金额
        Double orderAmt = Double.parseDouble(mCashierOrderInfoBean.getOrderAmt());
        mOrderTv.setText(currency + " " + StringUtils.formatAmount(mCashierOrderInfoBean.getOrderAmt()));
        Double actAmt = orderAmt;
        if (!TextUtils.isEmpty(feeAmount) && Double.parseDouble(feeAmount) != 0) {
            //个人钱包用户交易时，交易确认弹窗中的详情信息，涉及税费手续费的字段全部隐藏，全部交易类型统一调整
            mFeeTv.setVisibility(View.GONE);
            mFeeTab.setVisibility(View.GONE);
            /*if (isAdded()) {
                BigDecimal feeMoney = MoneyUtil.getTwoDecimal(feeAmount);
                actAmt += Double.parseDouble(feeAmount);
                mFeeTv.setText(currency + feeMoney);
            }*/
        } else {
            mFeeTv.setVisibility(View.GONE);
            mFeeTab.setVisibility(View.GONE);
        }

        String taxAmount = mCashierOrderInfoBean.getTransTaxAmt();
        if (!TextUtils.isEmpty(taxAmount) && Double.parseDouble(taxAmount) != 0) {
            //个人钱包用户交易时，交易确认弹窗中的详情信息，涉及税费手续费的字段全部隐藏，全部交易类型统一调整
            mTaxTv.setVisibility(View.GONE);
            mTaxTab.setVisibility(View.GONE);
            /*if (isAdded()) {
                BigDecimal taxMoney = MoneyUtil.getTwoDecimal(taxAmount);
                actAmt += Double.parseDouble(taxAmount);
                mTaxTv.setText(currency + taxMoney);
            }*/
        } else {
            mTaxTv.setVisibility(View.GONE);
            mTaxTab.setVisibility(View.GONE);
        }

        // 手动计算实付金额
        switch (mCashierOrderInfoBean.getPayType()) {
            case CommonConstants.CashierPaymentType.CASH_OUT:
            case CommonConstants.CashierPaymentType.ZIPIT:
            case CommonConstants.CashierPaymentType.Cashier_Withdrawal:
            case CommonConstants.CashierPaymentType.ZESA_FEE:
            case CommonConstants.CashierPaymentType.AIRTIME_BUNDLE:
                if (mSelectPaymentType.getDeductType().equals("0")) {
                    mAmtTv.setText(currency + " " + StringUtils.formatAmount(actAmt + ""));
                } else {
                    mAmtTv.setText(currency + " " + StringUtils.formatAmount(orderAmt + ""));
                }
                break;
        }
    }

    /**
     * 有的支付流程 额外费用从支付方式获取
     *
     * @param paymentBean
     */
    private void addTaxAndFee(PaymentBean paymentBean) {
        String payType = mCashierOrderInfoBean.getPayType();
        PaymentBean.PaymentTypeListBean payTypeBean;
        switch (payType) {
            case CommonConstants.CashierPaymentType.CASH_OUT:
            case CommonConstants.CashierPaymentType.ZIPIT:
            case CommonConstants.CashierPaymentType.Cashier_Withdrawal:
            case CommonConstants.CashierPaymentType.JUNIOR_Query_Transfer:
            case CommonConstants.CashierPaymentType.Query_Transfer:
            case CommonConstants.CashierPaymentType.JUNIOR_Query_BALANCE:
            case CommonConstants.CashierPaymentType.Query_BALANCE:
            case CommonConstants.CashierPaymentType.Cashier_TopUp:
            case CommonConstants.CashierPaymentType.ZESA_FEE:
                payTypeBean = mSelectPaymentType;
                mCashierOrderInfoBean.setTransFeeAmt(payTypeBean.getFeeAmt());
                mCashierOrderInfoBean.setTransTaxAmt(payTypeBean.getTaxAmt());
                break;
            default:
                break;
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mPaymentMethodTv.setOnClickListener(mClickListener);
        mPaymentMethodIv.setOnClickListener(mClickListener);
        mLeftBg.setOnClickListener(mClickListener);
        mPayTv.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.paymentMethodTv || view.getId() == R.id.paymentMethodIv) {
                mCashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(mCashierOrderInfoBean));
                mCashierBankListFragment.show(getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
            } else if (view.getId() == R.id.leftBg) {
                goClose();
            } else if (view.getId() == R.id.payTv) {
                String payType = mCashierOrderInfoBean.getPayType();
                if (CommonConstants.CashierPaymentType.CASH_OUT.equals(payType)){
                    cashierPay.nextClick("", CashierDetailsBottomFragment.this);
                }else{
                    cashierPay.nextClick(mSelectPaymentMethod.getFreePwd(), CashierDetailsBottomFragment.this);
                }
            }
        }
    };

    public void goClose() {
        mClosedDialog = new DoubleDialog(mContext);
        mClosedDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
            @Override
            public void leftCallback() {
                if (mClosedDialog != null) {
                    mClosedDialog.dismiss();
                }
                dismiss();
                if (cashierPay != null) cashierPay.onClose();
                CashierCallBackManager.getInstance().getCashierCallBack().cancelOrderPay();
            }

            @Override
            public void rightCallback() {
                if (mClosedDialog != null) {
                    mClosedDialog.dismiss();
                }
            }
        });
        mClosedDialog.show();
        mClosedDialog.setMyTitle(getString(R.string.common_alert_prompt))
                .setLeftColor(getResources().getColor(R.color.color_F85A40))
                .setLeftStr(getResources().getString(R.string.common_alert_cancel_pay))
                .setRightStr(getString(R.string.common_alert_keep_pay))
                .setRightColor(getResources().getColor(R.color.common_ye_F3881E));

        String payType = mCashierOrderInfoBean.getPayType();
        if (TextUtils.isEmpty(payType)) {
            throw new RuntimeException("没有配置payType");
        }

        switch (payType) {
//                    case CommonConstants.CashierPaymentType.Cashier_Withdrawal:
//                        //提现
//                        mClosedDialog.setContentStr(getString(R.string.checkout_alert_cancel_withdrawal_tip));
//                        break;
            default:
                mClosedDialog.setContentStr(getString(R.string.checkout_alert_cancel_pay_tip));
        }
    }

    public void goPayClose() {
        if (cashierPay != null) cashierPay.onClose();
        /*mClosedDialog = new DoubleDialog(mContext);
        mClosedDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
            @Override
            public void leftCallback() {
                if (mClosedDialog != null) {
                    mClosedDialog.dismiss();
                }
                //dismiss();
                if (cashierPay != null) cashierPay.onClose();
                //CashierCallBackManager.getInstance().getCashierCallBack().cancelOrderPay();
            }

            @Override
            public void rightCallback() {
                if (mClosedDialog != null) {
                    mClosedDialog.dismiss();
                }
            }
        });
        mClosedDialog.show();
        mClosedDialog.setMyTitle(getString(R.string.common_alert_prompt))
                .setLeftColor(getResources().getColor(R.color.color_F85A40))
                .setLeftStr(getResources().getString(R.string.common_alert_continue))
                .setRightStr(getString(R.string.common_alert_cancel))
                .setRightColor(getResources().getColor(R.color.common_ye_F3881E));

        String payType = mCashierOrderInfoBean.getPayType();
        if (TextUtils.isEmpty(payType)) {
            throw new RuntimeException("没有配置payType");
        }
        mClosedDialog.setContentStr(getString(R.string.checkout_alert_cancel_pay_tip));*/
    }


    /**
     * 监听的初始化.
     *
     * @param cashierCallBack 回调
     */
    public void setCashierCallBack(CashierCallBack cashierCallBack) {
        CashierCallBackManager.getInstance().init(cashierCallBack);
    }
}