package om.rrtx.mobile.functioncommon.utils;

import android.os.CountDownTimer;
import android.view.View;
import android.widget.TextView;


/**
 * 键盘倒计时工具类
 *
 * <AUTHOR>
 */
public class CodeDownTimerUtils extends CountDownTimer {
    private TextView mTextSend;
    private TextView mTextTime;

    /**
     * @param sendView          The TextView
     * @param millisInFuture    The number of millis in the future from the call
     *                          to {@link #start()} until the countdown is done and {@link #onFinish()}
     *                          is called.
     * @param countDownInterval The interval along the way to receiver
     *                          {@link #onTick(long)} callbacks.
     */
    public CodeDownTimerUtils(TextView timeView,TextView sendView, long millisInFuture, long countDownInterval) {
        super(millisInFuture, countDownInterval);
        this.mTextSend = sendView;
        this.mTextTime = timeView;
    }

    /**
     * 倒计时期间会调用
     *
     * @param millisUntilFinished 倒计时的时间
     */
    @Override
    public void onTick(long millisUntilFinished) {
        //设置不可点击
        //mTextSend.setClickable(false);
        mTextSend.setVisibility(View.GONE);
        mTextTime.setVisibility(View.VISIBLE);
        //设置倒计时时间
        long time = millisUntilFinished / 1000;
        String timeStr = time + "s";
        //设置按钮为灰色，这时是不能点击的
        //mTextTime.setTextColor(mTextTime.getContext().getResources().getColor(R.color.common_ye_F3881E));
        //mTextTime.setBackgroundColor(mTextTime.getContext().getResources().getColor(R.color.color_F5F7F8));
        mTextTime.setText(timeStr);
    }

    /**
     * 倒计时完成后调用
     */
    @Override
    public void onFinish() {
        mTextSend.setVisibility(View.VISIBLE);
        mTextTime.setVisibility(View.GONE);
        //mTextSend.setText(mTextSend.getContext().getResources().getString(R.string.SMS_btn_resend));
        //重新获得点击
        //mTextSend.setClickable(true);
        //还原背景色
        //mTextSend.setBackgroundResource(R.drawable.security_drawable_btn_select);
        //mTextSend.setTextColor(mTextSend.getContext().getResources().getColor(R.color.color_FFFFFF));
    }
}