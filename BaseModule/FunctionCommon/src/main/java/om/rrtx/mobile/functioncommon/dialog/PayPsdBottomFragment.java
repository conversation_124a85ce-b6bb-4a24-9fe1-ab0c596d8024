package om.rrtx.mobile.functioncommon.dialog;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;

import com.google.gson.Gson;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.bean.XWalletPayBean;
import om.rrtx.mobile.functioncommon.callback.PinDialogCallback;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.functioncommon.widget.PayPasswordView;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 收银台输入支付密码页面
 */
public class PayPsdBottomFragment extends BaseBottomFragment {
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.hint_tv)
    TextView mHintTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.ppv_content)
    PayPasswordView mPpvContent;
    private PayMethodLiveData mPayMethodLiveData;

    private PinDialogCallback mPinDialogCallback = new PinDialogCallback() {
        @Override
        public void pinEnd(String pin) {

        }

        @Override
        public void onClose() {

        }
    };

    public void setPinDialogCallback(PinDialogCallback mPinDialogCallback) {
        this.mPinDialogCallback = mPinDialogCallback;
    }

    /**
     * 目前没有需要处理 xWallerPayBeanJson 的情况，替换为hint
     */
    public static PayPsdBottomFragment newInstance(String hint) {
        Bundle args = new Bundle();
        args.putString(CommonConstants.Transmit.STRING, hint);
        PayPsdBottomFragment fragment = new PayPsdBottomFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static PayPsdBottomFragment newInstance() {
        Bundle args = new Bundle();
        PayPsdBottomFragment fragment = new PayPsdBottomFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.common_fragment_bottom_pay_psd;
    }

    @Override
    public BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView(View rootView) {
        setCancelable(false);

        mTitleTv.setText(R.string.enter_the_PIN);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_212121));
        ViewGroup.LayoutParams layoutParams = mBackIv.getLayoutParams();
        layoutParams.width = ExtensionsKt.pt2px(30);
        layoutParams.height = ExtensionsKt.pt2px(30);
        mBackIv.setLayoutParams(layoutParams);
        mBackIv.setImageResource(R.drawable.common_ic_close);

        String hint = getArguments().getString(CommonConstants.Transmit.STRING);
        if (StringUtils.isValidString(hint)) {
            try {
                XWalletPayBean xWalletPayBean = new Gson().fromJson(hint, XWalletPayBean.class);
            } catch (Exception e) {
                // 转换异常就是hint
                mHintTv.setText(hint);
            }
        }
    }

    @Override
    public void initDate() {
        super.initDate();

        if (getActivity() != null) {
            mPayMethodLiveData = new ViewModelProvider(getActivity()).get(PayMethodLiveData.class);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mCustomClickListener);
        mPpvContent.setPassword(new PayPasswordView.Password() {
            @Override
            public void passwordCallBack(String password) {
                LogUtil.e("TAG", "passwordCallBack: " + password);
                if (mPpvContent != null) {
                    mPpvContent.cancelPassword();
                }
                mPayMethodLiveData.getPsdFinish().setValue(password);
                mPinDialogCallback.pinEnd(password);
            }

            @Override
            public void forgotCallBack() {
                //这里是忘记密码
                CashierCallBackManager.getInstance().getCashierCallBack().forgotCallBack();
            }
        });
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                mPayMethodLiveData.mDismiss.setValue(true);
                mPinDialogCallback.onClose();
            }
        }
    };


    @Override
    public void dismiss() {
        super.dismiss();
        if (mPpvContent != null) {
            mPpvContent.cancelPassword();
        }
        mPayMethodLiveData.getPsdFinish().setValue(null);
    }

    /**
     * 清空密码
     */
    public void cancelPassword() {
        if (mPpvContent != null) {
            mPpvContent.cancelPassword();
        }
    }
}
