package om.rrtx.mobile.functioncommon.dialog.type;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.ZesaMerBean;
import om.rrtx.mobile.functioncommon.bean.ZesaResultBean;
import om.rrtx.mobile.functioncommon.dialog.CashierBankListFragment;
import om.rrtx.mobile.functioncommon.dialog.CashierPayImpl;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收银台转账的实现
 */
public class CashierBillPayTransferImpl implements CashierPayImpl {
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean bean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public CashierBillPayTransferImpl(AppCompatActivity activity, CashierOrderInfoBean cashierOrderInfoBean, PayMethodLiveData payMethodLiveData) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        bean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
    }

    @Override
    public void showInfo(View rootView) {
        TextView amtTv = rootView.findViewById(R.id.amtTv);
        TextView tab1 = rootView.findViewById(R.id.tab1);
        TextView tab1Tv = rootView.findViewById(R.id.tab1Tv);
        TextView paymentMethod = rootView.findViewById(R.id.paymentMethod);
        TextView paymentMethodTv = rootView.findViewById(R.id.paymentMethodTv);
        ImageView paymentMethodIv = rootView.findViewById(R.id.paymentMethodIv);
        View paymentMethodView = rootView.findViewById(R.id.paymentMethodView);

        String currency = CurrencyUtils.setCurrency(mActivity, bean.getCurrency()) + " ";
        //设置订单金额
        amtTv.setText(currency + StringUtils.formatAmount(bean.getOrderAmt()));
        //设置商户名称
        tab1.setVisibility(View.VISIBLE);
        tab1Tv.setVisibility(View.VISIBLE);
        tab1.setText(R.string.order_info);
        tab1Tv.setText(bean.getOrderInfo());
        // 手续费 、税费 在外面设置

        paymentMethod.setVisibility(View.VISIBLE);
        paymentMethodTv.setVisibility(View.VISIBLE);
//        paymentMethodTv.setEnabled(false);
        paymentMethodIv.setVisibility(View.VISIBLE);
        paymentMethodView.setVisibility(View.VISIBLE);

        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            pay(psd, "0");
        });
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        CashierBankListFragment cashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(cashierOrderInfoBean));
        cashierBankListFragment.show(baseBottomFragment.getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
    }

    @Override
    public void getPayMethod() {
    }

    @Override
    public void nextClick(String isFreePwd, BaseBottomFragment baseBottomFragment) {
        if (isFreePwd.equals("1")) {
            pay("", isFreePwd);
        } else {
            mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(bean));
            mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
        }
    }

    private void pay(String psd, String isFreePwd) {
        if (mIsRequestFinish || psd == null) {
            return;
        }
        mIsRequestFinish = true;

        mCommonModel.payZesa(
                bean.getMerName(),
                bean.getMerNo(),
                bean.getOrderAmt(),
                bean.getCurrency(),
                psd,
                isFreePwd,
                new BaseObserver<ZesaResultBean>(mActivity) {
                    @Override
                    public void requestSuccess(ZesaResultBean sResData) {
                        if (mPayPsdBottomFragment != null) {
                            mPayPsdBottomFragment.dismiss();
                        }
                        CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                        mIsRequestFinish = false;
                    }

                    @Override
                    public void requestFail(String sResMsg) {
                        if (mPayPsdBottomFragment != null) {
                            mPayPsdBottomFragment.cancelPassword();
                        }
                        ToastUtil.show(mActivity, sResMsg);
                        mIsRequestFinish = false;
                    }
                });

        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.cancelPassword();
        }
    }

    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
