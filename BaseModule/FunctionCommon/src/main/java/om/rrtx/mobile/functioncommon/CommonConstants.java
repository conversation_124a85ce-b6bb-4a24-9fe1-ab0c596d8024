package om.rrtx.mobile.functioncommon;

import om.rrtx.mobile.rrtxcommon1.UserConstants;

/**
 * <AUTHOR>
 */
public class CommonConstants {
    /**
     * 刷新银行卡列表广播
     */
    public static final String REFRESHBANKSACTION = "om.rrtx.mobile.functioncommon.refresh.banks.action";

    /**
     * 绑定服务的全路径
     */
    public static final String SERVICEPATH = "com.rrtx.xwalletpaylib.CashierPayService";

    public interface URL {
        /**
         * 获取公钥接口
         */
        String GETPUB = "/encrypt/public/key";

        /**
         * 支付方式
         */
        String PAYMENTTYPE = "/checkOutCounter/payment/type/get";

        /**
         * 订单信息
         */
        String GETORDERINFO = "/pay/order/get";

        /**
         * 汇款码缴费创建订单
         */
        String MERCHANT_CREATERORDER = "/perBillPayment/createOrder";

        /**
         * 汇款吗付款下单
         */
        String MERCODE_PAY = "/pay/createMerCodeOrder";

        /**
         * 电费下单
         */
        String ZESA_CREATERORDER = "/perZesa/createOrder";

        /**
         * 话费下单
         */
        String BUY_CREATERORDER = "/perBuyAirtimeOrBundle/createOrder";

        /**
         * 支付订单
         */
        String PAYORDER = "/pay/payOrder";
        /**
         * 绑定一行卡接口
         */
        String BINDBANK = "/cstCardRecord/add";

        /**
         * 汇款码缴费支付
         */
        String PAY_BILL_CODE = "/perBillPayment/payOrder";
        /**
         * 个人用户转账订单校验接口
         */
        String TRANSFERTOPERACC = "/perTransfer/transferToPerAcc";
        /**
         * 请求支付AA订单
         */
        String AAPAYORDER = "/splitBill/payOrder";
        /**
         * 创建提现订单
         */
        String WITHDRAWALCREATEORDER = "/withdrawal/createOrder";
        /**
         * 钱包用户Zipit提现
         */
        String ZIPITCREATEORDER = "/user/zipit/createOrder";
        String CASHCREATEORDER = "/cashOut/createOrder";
        /**
         * 充值
         */
        String TOPUPACC = "/perTopUp/topUpAcc";
        /**
         * 充值校验
         */
        String TOPUPCHECK = "/perTopUp/check";
        /**
         * 激活的货币
         */
        String AVAILABLECURRENCY = "/subjectInf/availableCurrencies";
        /**查询默认币种**/
        String COMMON_SYSDICTCODE = "/common/sysDictCode";
        String EGT_BANK_LIST = "/cstBank/bankList";
        String REMITTANCE_CODE_CHECK = "/cashOut/remittanceCodeCheck";
        String BANK_NUM_QUERY = "/perTopUp/bankNumQuery";

        /**
         * 查询所有币种账户
         */
        String BALANCE = "/subjectInf/balance";

        /**
         * 查询交易支付接口
         */
        String QUERY_TRANSFER_PAY = "/orderRecord/queryOrderRecordPay";
        /**
         * 查询亲子交易支付接口
         */
        String JUNIOR_TRANSFER_PAY = "/juniorSubjectInf/queryOrderRecordPay";

        /**
         * 查询余额
         */
        String QUERY_BALANCE = "/subjectInf/walletEnquiryPay";
        String JUNIOR_ENQUIRY = "/juniorSubjectInf/walletEnquiryPay";

        /**
         * 电费支付
         */
        String PAY_ZESA = "/perZesa/tokenPurchase";
        /**
         * 电费支付
         */
        String PAY_ZESA_ORDER = "/perZesa/payOrder";
        /**
         * 话费支付
         */
        String PAY_BUY_ORDER = "/perBuyAirtimeOrBundle/payOrder";
        /**
         *
         */
        String RE_ZESA_TOKEN = "/perZesa/resendToken";

        /**
         * 话费流量购买
         */
        String BUY_AIRTIME_BUNDLE = "/perBuyAirtimeOrBundle/apply";

        /**
         * 营销信息查询
         */
        String Query_Marketing = "/api/marketing/marketingInfoQuery";
        /**
         *
         */
        String Use_Marketing = "/api/marketing/marketingInfoChange";

        /**
         * 创建订单接口
         */
        String CREATEORDER = "/pay/createOrder";

        /**
         * 校验验证码
         */
        String VALIDATESMS = "/code/sms/validate";

        /**
         * 修改主设备
         */
        String CHANGEMASTERDEVICE = "/user/device/changeMasterDevice";

        /**
         * 亲子注册验证码校验
         */
        String JUNIOR_VALIDATESMS = "/juniorAccount/verifySmsCode";

        String GET_ACCOUNT_BALANCE = "/subjectInf/getAccountBalanceByCurrency";
    }

    public interface Parameter {
        String SEND_CODE_TOKEN = "sendCodeToken";
        String SEND_CODE = "sendCode";
        String MOBILE = "mobile";
        String SMSCODE = "smsCode";
        String DICT_CODE = "dictCode";

        /**
         * 银行卡类型
         */
        String CARDTYPE = "cardType";
        /**
         * 订单类型
         */
        String ORDERSOURCE = "orderSource";
        /**
         * 订单token
         */
        String PAYTOKEN = "payToken";
        /**
         * 订单支付
         */
        String PAYMENTPRODUCT = "paymentProduct";
        /**
         * 订单支付
         */
        String TRANSTYPE = "transType";
        /**
         * 订单编号
         */
        String ORDERNO = "orderNo";
        /**
         * 订单金额
         */
        String AMT = "amt";
        /**
         * 商户号
         */
        String MERNO = "merNo";
        /**
         * 支付密码
         */
        String PIN = "pin";
        String CHECK_TOKEN = "checkToken";
        String Sign_Token = "signToken";
        String Sign_NO = "signNo";
        String Condition_VersionNo = "conditionVersionNo";
        String Condition_Title = "conditionTitle";
        String Condition_Type = "conditionType";
        String IS_FREE_PWD = "isFreePwd";
        /**
         * 支付公钥
         */
        String KEYID = "keyId";
        /**
         * 支付订单号
         */
        String PAYORDERNO = "payOrderNo";
        /**
         * 支付密码类型
         */
        String PAYMENTPASSWORDTYPE = "paymentPasswordType";
        String PAYMENTPASSWORD = "paymentPassword";
        String PASSWORD = "password";
        /**
         * 二维码
         */
        String QRCODE = "qrCode";
        /**
         * 外部订单号
         */
        String OUTORDERNO = "outOrderNo";
        /**
         * 卡号
         */
        String CARDNO = "cardNo";
        /**
         * 银行名称
         */
        String BANKNO = "bankNo";
        /**
         * 客户类型
         */
        String CSTTYPE = "cstType";
        /**
         * 客户类型
         */
        String CSTNO = "cstNo";
        /**
         * 金额
         */
        String TRANSAMT = "transAmt";
        String TRX_AMT = "trxAmt";
        String TOKEN = "token";
        String TRX_ORDER_NO = "trxOrderNo";
        String METERNO = "meterNo";
        String MERCODE = "merchantCode";
        String METER_NAME = "meterName";
        String TRX_TRANS_NO = "trxTransType";
        String BILL_CATEGORY = "billerCategory";
        String ACCOUNT_NO = "recAccountNo";
        String TERM = "term";
        String CLASSNO = "classNo";
        String CITY = "city";
        String BILL_PAYMENTTYPE = "billerPaymentType";

        /**
         * 付款方式
         */
        String PAYMENTTYPE = "paymentType";
        String AIRTIMEPLANCODE = "airtimePlanCode";
        /**
         * 转账人电话
         */
        String RCVMOBILE = "rcvMobile";
        /**
         * 转账金额
         */
        String TRANSFERAMT = "transferAmt";
        /**
         * 转账的支付凭证`
         */
        String TRANSFERTOKEN = "transferToken";
        /**
         * 转账留言
         */
        String REMARK = "remark";
        /**
         * 支付订单号
         */
        String DETAILORDERNO = "detailOrderNo";
        /**
         * 银行名称
         */
        String BANKNAME = "bankName";
        /**
         * 手续
         */
        String FEEAMT = "feeAmt";
        /**
         * 税费
         */
        String TAXAMT = "taxAmt";
        /**
         * 充值金额
         */
        String TOPUPAMT = "topUpAmt";
        /**
         * 充值的token
         */
        String TOPUPTOKEN = "topUpToken";
        /**
         * 充值的卡id
         */
        String CARDID = "cardId";
        /**
         * 提现token
         */
        String WITHDRAWALTOKEN = "withdrawalToken";
        String REMITTANCECODE = "remittanceCode";
        /**
         * 用户id
         */
        String USERID = "userId";
        String JUNIOR_USERID = "juniorUserId";
        String USER_TYPE = "userType";
        /**
         * 临时设备ID
         */
        String TEMPDEVICEID = "tempDeviceId";
        /**
         * bizhong
         */
        String CURRENCY = "currency";
        /**
         * 当前用户类型 00-客户 10-机构
         */
        String CUSTOMER_TYPE = "customerType";
        String QUERY_TYPE = "queryType";
        String BANK_TYPE = "bankType";
        String JSON_BEAN = "json_Bena";
        String PAGE_NUM = "pageNum";
        String PAGE_SIZE = "pageSize";
        String PAGE = "page";
        String LIMIT = "limit";
        String START_DATE = "startDate";
        String END_DATE = "endDate";
        String BILL_SUB_TYPE = "billSubType";
        String BUY_AMOUNT = "buyAmount";

        String PLAN_CODE = "planCode";
        String BUNDLE_ID = "bundleId";
        String BUNDLE_TYPE = "bundleType";
        String APP_NAME_CODE = "appNameCode";

        /**
         * 商户号
         */
        String RECMERNO = "recMerNo";

        /**
         * 商户名
         */
        String RECMERNAME = "recMerName";

        /**
         * 收银台编号
         */
        String CHECKSTANDNO = "checkstandNo";
    }

    public interface Transmit {
        /**
         * 传递的json参数
         */
        String XWALLETPAYBEANJSON = "xWalletPayBeanJson";
        /**
         * 转账的相应数据
         */
        String ORDER_SUCCESS_BRAN = "orderSuccessBean";
        String DETAILSBEAN = "detailsBean";
        /**
         * 卡号
         */
        String CARDNO = "cardNo";
        /**
         * 支付类型
         */
        String PAYMENTTYPE = "paymentType";
        /**
         * 传递的订单信息接口
         */
        String CASHIERORDERINFO = "cashierOrderInfo";
        /**
         * 充值的状态
         */
        String TOPSTATUS = "topStatus";
        /**
         * 传递类型
         */
        String OUTTYPE = "outType";
        /**
         * 货币符号
         */
        String CURRENCY = "currency";
        String POSITION = "position";
        String JSON = "json";
        String REQUESTCODE = "requestCode";
        String STRING = "json";
        String MOBILE = "mobile";
    }

    public interface OrderSource {
        /**
         * 外部订单
         */
        String EXTERNALORDER = "1";
        /**
         * 内部订单
         */
        String INNERORDER = "0";
    }

    /**
     * 支付产品类型
     */
    public interface PaymentProduct {
        /**
         * 支付产品 00-APP
         */
        String APP = "00";
        /**
         * 01-H5
         */
        String H5 = "01";
        /**
         * 02-条码(Bar Code Payment)
         * 个人付款码支付，商户扫
         */
        String BARCODE = "02";
        /**
         * 03-二维码(Qr Code Payment)
         * 商户收款码支付，个人扫
         */
        String QRCODE = "03";
        /**
         * 04 -订单二维码Qr Code Payment
         */
        String ORDER_QRCODE = "04";
        /**
         * 05 -电费订单
         */
        String ORDER_ZESA = "05";
        /**
         * 06 -话费订单
         */
        String ORDER_BUND = "06";
        /**
         * 07 -汇款码付款
         */
        String MERCHANT_CODE_PAY = "07";
        /**
         * 30支付、且支付产品为10-代收时
         */
        String PAYMENT_THIRTY = "10";
    }

    /**
     * 收银台跳转标识
     */
    public interface CashierJumpFlag {
        /**
         * 外部订单跳转
         */
        String EXTERNALJUMP = "externalJump";
        /**
         * 内部订单
         */
        String INNERJUMP = "innerJump";
    }

    /**
     * 支付方式
     */
    public interface CstType {
        //客户类型：0-商户，1 用户  充值及提现传1   支付传0 其他类型待补充
        String WithdrawalType = "0";
        String PayType = "1";
        //支付
        String PAYTYPE = "30";
    }

    /**
     * 收银台的支付类型
     */
    public interface CashierPaymentType {
        /**
         * 扫码支付
         */
        String Cashier_Mer = "01";
        /**
         * 转账支付
         */
        String Cashier_Transfer = "02";
        /**
         * AA支付
         */
        String Cashier_AASplit = "03";
        /**
         * 提现支付
         */
        String Cashier_Withdrawal = "04";
        /**
         * 订单支付
         */
        String Cashier_Payment = "05";
        /**
         * 外部订单支付
         */
        String Cashier_OutPayment = "06";
        /**
         * 充值支付
         */
        String Cashier_TopUp = "07";
        /**
         * 交易查询
         */
        String Query_Transfer = "08";
        /**
         * 余额查询
         */
        String Query_BALANCE = "09";
        /**
         * 电费
         */
        String ZESA_FEE = "10";
        /**
         * 话费 流量
         */
        String AIRTIME_BUNDLE = "11";
        /**
         * ZIPIT
         */
        String ZIPIT = "12";
        /**
         * 亲子账户余额查询
         */
        String JUNIOR_Query_BALANCE = "13";
        /**
         * 亲子交易查询
         */
        String JUNIOR_Query_Transfer = "14";
        /**
         * 转账
         */
        String SEND_MONEY = "15";
        /**
         * 汇款码缴费
         */
        String MERCHANT_CODE = "16";
        /**
         * 汇款码付款
         */
        String MMERCODE_PAY = "17";
        /**
         * CASH OUT
         */
        String CASH_OUT = "18";
    }

    /**
     * 事务的类型
     */
    public interface TransType {
        String TopUp = "00";
        String Agent_TopUp = "01";
        String Agent_MOBILLE = "02";
        String Transfer = "10";
        String Transfer_Reversal = "13";
        String Agent_Transfer = "11";
        //代客转账退款
        String Agent_Transfer_REFUND = "12";
        String Withdraw = "20";
        String Payment = "30";
        String Thirty_Party_Paying = "34";
        //缴费
        String Bill_PAY = "31";
        String Mobile_Fee = "32";
        String Agent_Zesa = "33";
        String AAPayment = "40";
        String Refund = "50";
        String Cashback = "55";
        String Query_BALANCE = "65";
        String Query_Transfer = "66";
        String Withdraw_Refund = "70";
        String Bulk_Payment = "75";
        String ZIPIT = "26";
        String ZIPIT_TWO = "27";
        String REVERSE = "71";
        String ADJUST = "6001";
        String CASH_OUT = "22";
        String ZIPIT_Reversal = "23";
    }

    /**
     * 支付方式
     */
    public interface PaymentPasswordType {
        //密码
        String PSD = "1";
        //指纹
        String FINGER = "2";
    }

    public interface PaymentType {
        String ewallet = "02";
        String bank = "03";
    }

    public interface Currency {
        public static final String ZWL = "ZWG";
        public static final String USD = "USD";
    }

    public interface BillPayType {
        /**
         * 00：话费充值
         * 01：哈拉雷市政
         * 02：电费
         * 03：学费
         * 04：党费
         * 05：社会福利
         * 06：其他
         */
        String MOBILE_FEES = "00";
        String HARARE = "01";
        String ELE_FEES = "02";
        String SCHOOL_FEES = "03";
        String PARTY_FEES = "04";
        String SOCIAL_WELFARE = "05";
        String OTHER = "06";
        String PAY_BILLCODE = "07";
    }

    public interface BundleType {
        String Khuluma = "02";
        String Gigs = "03";
        String Data_Bundles = "01";
        String Designated = "05";
//        {"bundleTypeList":[{"bundleType":"02","bundleName":"Khuluma 24/7 Bundles"}
//            ,{"bundleType":"03","bundleName":"Data Mo’Gigs Bundles"},
//            {"bundleType":"01","bundleName":"Data Bundles"},
//            {"bundleType":"04","bundleName":"Designated APP Bundle"}]}
    }

    public interface ResultCode {
        int Request = 999;  // 必须大于0
        int Success = 1;
        int Fail = 2;
    }

    public interface ExtJumpType {
        String Jump_App_Pay = "00";
        String AUTO_Debit_Sign = "01";
    }

    public interface AirTimeStatus {
        String SUCCESS = "30";
        String Fail = "40";

        String Error = "20";
    }

    public interface UserStatus {
        // 0-正常 1-冻结 2-删除 3-待激活 4-待认证 5-临时冻结
        String NORMAL = "0";
        String FROST = "1";
        String DELETE = "2";
        String PENDING_ACTIVATE = "3";
        String PENDING_AUTH = "4";
        String FROST_LIN = "5";
    }
    public interface RegisterParameter {
        /**
         * 名字
         */
        String AREACODE = "areaCode";
        String RELATION_SHIP = "relationShip";

        /**
         * 电话号码
         */
        String MOBILE = "mobile";

        /**
         * 名字
         */
        String FIRSTNAME = "firstName";
        /**
         *
         */
        String MIDDLENAME = "middleName";
        /**
         * 姓氏
         */
        String LASTNAME = "lastName";

        /**
         * 性别（1:女 2:男）
         */
        String GENDER = "gender";

        /**
         * 生日(yyyyMMdd)
         */
        String BIRTHDAY = "dateOfBirth";

        /**
         * 证件类型（10-身份证 20-护照）
         */
        String IDTYPE = "idType";

        /**
         * 证件号
         */
        String IDNUMBER = "idNumber";

        /**
         * 省市编码
         */
        String CITYCODE = "cityCode";

        /**
         * 地址
         */
        String ADDRESS = "address";
        String CHECK_TOKEN = "checkToken";

        /**
         * 邮箱
         */
        String EMAIL = "email";

        /**
         * password
         */
        String PASSWORD = "password";

        /**
         * 协议类型 01-钱包个人 02-钱包商户
         */
        String CONDITIONTYPE = UserConstants.Parameter.CONDITIONTYPE;
        /**
         * 协议版本
         */
        String CONDITIONVERSIONNO = UserConstants.Parameter.CONDITIONVERSIONNO;
        /**
         * 协议名称
         */
        String CONDITIONTITLE = UserConstants.Parameter.CONDITIONTITLE;
    }

}
