package om.rrtx.mobile.functioncommon.model;

import android.content.Context;
import android.text.TextUtils;

import java.util.HashMap;
import java.util.Map;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.CommonService;
import om.rrtx.mobile.functioncommon.bean.AirtimeSuccessBean;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.functioncommon.bean.BalanceCertificateBean;
import om.rrtx.mobile.functioncommon.bean.BankNumBean;
import om.rrtx.mobile.functioncommon.bean.CashOutCheckBean;
import om.rrtx.mobile.functioncommon.bean.CertificateBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyBean;
import om.rrtx.mobile.functioncommon.bean.DictoryBean;
import om.rrtx.mobile.functioncommon.bean.OptionsBankListBean;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.bean.TopUpCheckBean;
import om.rrtx.mobile.functioncommon.bean.TopUpSuccessBean;
import om.rrtx.mobile.functioncommon.bean.WithdrawalOrderBean;
import om.rrtx.mobile.functioncommon.bean.ZesaMerBean;
import om.rrtx.mobile.functioncommon.bean.ZesaResultBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.CodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.rrtxcommon1.bean.MarketingBean;
import om.rrtx.mobile.rrtxcommon1.bean.RequestUseMarketingBean;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class CommonModel extends BaseLoader {

    private CommonService mCommonService;

    public CommonModel() {
        mCommonService = RetrofitServiceManager.getInstance().create(CommonService.class);
    }

    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mCommonService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 请求支付类型
     *
     * @param orderNo
     * @param orderAmt       订单接
     * @param paymentProduct 支付类型
     * @param merNo          商户号
     * @param baseObserver   回调
     */
    public void requestPaymentType(String userId, String orderNo, String orderAmt, String paymentProduct, String merNo, String transType, String orderSource, String currency, BaseObserver<PaymentBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.AMT, orderAmt);
//        map.put(CommonConstants.Parameter.PAYMENTPRODUCT, paymentProduct);
        map.put(CommonConstants.Parameter.ORDERSOURCE, orderSource);
        if (!TextUtils.isEmpty(orderNo)) {
            map.put(CommonConstants.Parameter.ORDERNO, orderNo);
        }
        map.put(CommonConstants.Parameter.TRANSTYPE, transType);
        if (!TextUtils.isEmpty(merNo)) {
            map.put(CommonConstants.Parameter.MERNO, merNo);
        }
        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestPaymentType(map)).subscribe(baseObserver);
    }

    public void requestPaymentType(String amt, String transType, String orderSource, String currency, BaseObserver<PaymentBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.AMT, amt);
        map.put(CommonConstants.Parameter.TRANSTYPE, transType);
        map.put(CommonConstants.Parameter.ORDERSOURCE, orderSource);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestPaymentType(map)).subscribe(baseObserver);
    }


    /**
     * 支付订单
     *
     * @param paymentProduct      支付产品 00-APP 01-H5 02-条码(Bar Code Payment) 03-二维码(Qr Code Payment)
     * @param payOrderNo          支付订单号
     * @param paymentPassword     支付密码
     * @param paymentPasswordType 1:密码 2：指纹
     * @param qrCode              paymentProduct为02时必须
     * @param orderSource         0-内部 1-外部
     * @param outOrderNo          orderSource为1时必须
     * @param payToken            orderSource为1时必须
     */
    public void requestPayment(String userId, String paymentProduct, String payOrderNo, String paymentPassword, String paymentPasswordType, String qrCode, String orderSource, String outOrderNo, String payToken, String paymentType, String cardId, String currency, BaseObserver<PaymentSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.PIN, paymentPassword);
        map.put(CommonConstants.Parameter.PAYMENTPRODUCT, paymentProduct);
        map.put(CommonConstants.Parameter.PAYORDERNO, payOrderNo);
        map.put(CommonConstants.Parameter.PAYMENTPASSWORDTYPE, paymentPasswordType);
        map.put(CommonConstants.Parameter.PAYMENTTYPE, paymentType);
        if (!TextUtils.isEmpty(qrCode)) {
            map.put(CommonConstants.Parameter.QRCODE, qrCode);
        }
        map.put(CommonConstants.Parameter.ORDERSOURCE, orderSource);
        if (!TextUtils.isEmpty(outOrderNo)) {
            map.put(CommonConstants.Parameter.OUTORDERNO, outOrderNo);
        }
        if (!TextUtils.isEmpty(payToken)) {
            map.put(CommonConstants.Parameter.PAYTOKEN, payToken);
        }
        map.put(CommonConstants.Parameter.CARDID, cardId);
        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestPayment(map)).subscribe(baseObserver);
    }

    public void requestPayment(String trxOrderNo, String paymentPassword, String qrCode, String payToken, String paymentType, String cardId,
                               String isFreePwd,
                               BaseObserver<PaymentSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.TRX_ORDER_NO, trxOrderNo);
        map.put(CommonConstants.Parameter.PAYMENTPASSWORD, paymentPassword);
        map.put(CommonConstants.Parameter.QRCODE, qrCode);
        map.put(CommonConstants.Parameter.PAYTOKEN, payToken);
        map.put(CommonConstants.Parameter.PAYMENTTYPE, paymentType);
        if (cardId != null)
            map.put(CommonConstants.Parameter.CARDID, cardId);
        map.put(CommonConstants.Parameter.IS_FREE_PWD, isFreePwd);
        observe(mCommonService.requestPayment(map)).subscribe(baseObserver);
    }

    /**
     * 绑定卡接口
     *
     * @param cardNo 卡号
     */
    public void requestBindBank(String userId, String cardNo, String cardType, String bankNo, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CARDNO, cardNo);
        map.put(CommonConstants.Parameter.CARDTYPE, cardType);
        map.put(CommonConstants.Parameter.BANKNO, bankNo);
        map.put(CommonConstants.Parameter.USERID, userId);
        observe(mCommonService.requestBindBank(map)).subscribe(baseObserver);
    }

    public void requestBillCodePayOrder(String trxOrderNo, String psd, BaseObserver<PaymentSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.TRX_ORDER_NO, trxOrderNo);
        map.put(CommonConstants.Parameter.PIN, psd);
        map.put(CommonConstants.Parameter.PAYMENTTYPE, "02");
        observe(mCommonService.requestBillCodePayOrder(map)).subscribe(baseObserver);
    }

    public void requestUserTransfer(String userId, String mobile, String orderAmt, String remark, String psd, String transferToken, String currency, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.RCVMOBILE, mobile);
        map.put(CommonConstants.Parameter.TRANSFERAMT, orderAmt);
        map.put(CommonConstants.Parameter.PIN, psd);
        map.put(CommonConstants.Parameter.TRANSFERTOKEN, transferToken);
        if (!TextUtils.isEmpty(remark)) {
            map.put(CommonConstants.Parameter.REMARK, remark);
        }
        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestUserTransfer(map)).subscribe(baseObserver);
    }

    public void requestPayOrder(String userId, String paymentToken, String psd, String currency, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.PIN, psd);
        map.put(CommonConstants.Parameter.DETAILORDERNO, paymentToken);
        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestPayOrder(map)).subscribe(baseObserver);
    }

    /**
     * 创建提现订单
     *
     * @param bankNo              卡类型
     * @param cardNo              卡号
     * @param amt                 金额
     * @param paymentPassword     密码
     * @param paymentPasswordType 密码类型
     * @param paymentPasswordType token
     * @param baseObserver        回调
     */
    public void requestWithdrawalOrder(String remark,String payType, String bankNo, String cardNo, String amt, String paymentPassword, String paymentPasswordType, String withdrawalToken, String currency, BaseObserver<WithdrawalOrderBean> baseObserver) {

        Map<String, String> withdrawalMap = new HashMap<>();
        withdrawalMap.put(CommonConstants.Parameter.CARDNO, cardNo);
        withdrawalMap.put(CommonConstants.Parameter.AMT, amt);
        withdrawalMap.put(CommonConstants.Parameter.PAYMENTPASSWORD, paymentPassword);
        withdrawalMap.put(CommonConstants.Parameter.PAYMENTPASSWORDTYPE, paymentPasswordType);
        withdrawalMap.put(CommonConstants.Parameter.WITHDRAWALTOKEN, withdrawalToken);
        withdrawalMap.put(CommonConstants.Parameter.BANKNO, bankNo);
        withdrawalMap.put(CommonConstants.Parameter.CURRENCY, currency);
        if (CommonConstants.CashierPaymentType.ZIPIT.equals(payType)) {
            if (!TextUtils.isEmpty(remark)) {
                withdrawalMap.put(CommonConstants.Parameter.REMARK, remark);
            }
            observe(mCommonService.requestZIPITOrder(withdrawalMap)).subscribe(baseObserver);
        } else {
            observe(mCommonService.requestWithdrawalOrder(withdrawalMap)).subscribe(baseObserver);
        }

    }

    public void requestCashOutOrder(String remittanceCode,String amt, String paymentPassword, String paymentPasswordType, String withdrawalToken, String currency, BaseObserver<WithdrawalOrderBean> baseObserver) {
        Map<String, String> withdrawalMap = new HashMap<>();
        withdrawalMap.put(CommonConstants.Parameter.REMITTANCECODE, remittanceCode);
        withdrawalMap.put(CommonConstants.Parameter.AMT, amt);
        withdrawalMap.put(CommonConstants.Parameter.CURRENCY, currency);
        withdrawalMap.put(CommonConstants.Parameter.WITHDRAWALTOKEN, withdrawalToken);
        withdrawalMap.put(CommonConstants.Parameter.PAYMENTPASSWORD, paymentPassword);
        withdrawalMap.put(CommonConstants.Parameter.PAYMENTPASSWORDTYPE, paymentPasswordType);
        observe(mCommonService.requestCashOutOrder(withdrawalMap)).subscribe(baseObserver);

    }

    /**
     * 请求充值接口
     *
     * @param orderAmt 充值金额
     */
    public void requestTopUp(String userId, String orderAmt, String currency, BaseObserver<TopUpCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.TOPUPAMT, orderAmt);
//        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestTopUpCheck(map)).subscribe(baseObserver);
    }

    public void requestTopUpAcc(String userId, String orderAmt, String cardNo, String psd, String paymentPasswordType, String topUpToken, String currency, BaseObserver<TopUpSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.TOPUPAMT, orderAmt);
//        map.put(CommonConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.PIN, psd);
        map.put(CommonConstants.Parameter.CARDID, cardNo);
        map.put(CommonConstants.Parameter.PAYMENTPASSWORDTYPE, paymentPasswordType);
        map.put(CommonConstants.Parameter.TOPUPTOKEN, topUpToken);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestTopUpAcc(map)).subscribe(baseObserver);
//                .subscribeOn(Schedulers.io())
//                .concatMap(new Function<Response<BaseBean<TopUpCheckBean>>, ObservableSource<Response<BaseBean<PubBean>>>>() {
//                    @Override
//                    public ObservableSource<Response<BaseBean<PubBean>>> apply(Response<BaseBean<TopUpCheckBean>> baseBeanResponse) throws Exception {
//                        try {
//                            Map<String, String> pubMap = new HashMap<>();
//                            String topUpToken = baseBeanResponse.body().getData().getTopUpToken();
//                            map.put(CommonConstants.Parameter.TOPUPTOKEN, topUpToken);
//                            return mCommonService.requestPub(pubMap);
//                        } catch (RuntimeException e) {
//                            return Observable.error(new ApiException(baseBeanResponse.body().getMessage()));
//                        }
//                    }
//                })
//                .concatMap(new Function<Response<BaseBean<PubBean>>, ObservableSource<Response<BaseBean<Object>>>>() {
//                    @Override
//                    public ObservableSource<Response<BaseBean<Object>>> apply(Response<BaseBean<PubBean>> baseBeanResponse) throws Exception {
//                        try {
//                            map.put(CommonConstants.Parameter.PAYMENTPASSWORD, psd);
//                            map.put(CommonConstants.Parameter.CARDID, cardNo);
//                            map.put(CommonConstants.Parameter.PAYMENTPASSWORDTYPE, paymentPasswordType);
//                            return mCommonService.requestTopUpAcc(map);
//                        } catch (RuntimeException e) {
//                            return Observable.error(new ApiException(baseBeanResponse.body().getMessage()));
//                        }
//                    }
//                })
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(baseObserver);
    }

    public void queryDefaultCurrency(BaseNoDialogObserver<DictoryBean.AppDictBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put("dictCode","default_currency");
        observe(mCommonService.queryDefaultCurrency(map)).subscribe(baseObserver);
    }

    /**
     * 激活的货币
     */
    public void getAvailableCurrency(String userId, BaseNoDialogObserver<CurrencyBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(userId)) {
            map.put(CommonConstants.Parameter.USERID, userId);
        }
        observe(mCommonService.getAvailableCurrency(map)).subscribe(baseObserver);
    }

    public void getOptionsBankList(String queryType, BaseObserver<OptionsBankListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.BANK_TYPE, queryType);
        observe(mCommonService.getOptionsBankList(map)).subscribe(baseObserver);
    }

    public void remittanceCodeCheck(String merchantCode, BaseNoDialogObserver<CashOutCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.REMITTANCECODE, merchantCode);
        observe(mCommonService.remittanceCodeCheck(map)).subscribe(baseObserver);
    }

    public void getBankNum(BaseObserver<BankNumBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        observe(mCommonService.getBankNum(map)).subscribe(baseObserver);
    }

    /**
     * 查询所有币种账户
     *
     * @param baseObserver 回调
     */
    public void requestAccountCurrency(String type, BaseObserverNoError<CurrencyAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.QUERY_TYPE, type);
        observe(mCommonService.requestAccountCurrency(map)).subscribe(baseObserver);
    }

    public void requestAccountCurrencyNoDialog(String type, BaseNoDialogObserver<CurrencyAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.QUERY_TYPE, type);
        observe(mCommonService.requestAccountCurrency(map)).subscribe(baseObserver);
    }

    /**
     * 查询所有币种账户
     *
     * @param baseObserver 回调
     */
    public void requestTransferPay(String payType, String useId, String userType, String currency, String amt, String transType, String pin, BaseObserver<CertificateBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.USER_TYPE, userType);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.TRANSTYPE, transType);
        map.put(CommonConstants.Parameter.PIN, pin);
        if (CommonConstants.CashierPaymentType.JUNIOR_Query_Transfer.equals(payType)) {
            map.put(CommonConstants.Parameter.TRX_AMT, "0");
            map.put(CommonConstants.Parameter.JUNIOR_USERID, useId);
            observe(mCommonService.requestJuniorTransferPay(map)).subscribe(baseObserver);
        } else {
            map.put(CommonConstants.Parameter.TRX_AMT, amt);
            observe(mCommonService.requestTransferPay(map)).subscribe(baseObserver);
        }
    }

    public void requestBalancePay(String payType, String pin, String currency, String transType,
                                  String userId, BaseObserver<BalanceCertificateBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.TRANSTYPE, transType);
        //map.put(CommonConstants.Parameter.FEEAMT, fee);
        //map.put(CommonConstants.Parameter.TAXAMT, tax);
        if (CommonConstants.CashierPaymentType.JUNIOR_Query_BALANCE.equals(payType)) {
            if (StringUtils.isValidString(userId))
                // 查询亲子余额需传入id
                map.put(CommonConstants.Parameter.JUNIOR_USERID, userId);
            observe(mCommonService.requestJuniorBalancePay(map)).subscribe(baseObserver);
        } else {
            if (StringUtils.isValidString(userId))
                map.put(CommonConstants.Parameter.USERID, userId);
            observe(mCommonService.requestBalancePay(map)).subscribe(baseObserver);
        }
    }

    public void payZesa(String meterName, String meterNo, String amt, String currency, String pin,
                        String isFreePwd, BaseObserver<ZesaResultBean> baseObserver) {
//        meterNo
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.METERNO, meterNo);
        map.put(CommonConstants.Parameter.METER_NAME, meterNo);
        map.put(CommonConstants.Parameter.TRX_AMT, amt);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.IS_FREE_PWD, isFreePwd);
        observe(mCommonService.payZesa(map)).subscribe(baseObserver);
    }

    public void payZesaOrder(String trxOrderNo, String paymentType, String cardId, String pin,
                             String isFreePwd, BaseObserver<PaymentSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.TRX_ORDER_NO, trxOrderNo);
        map.put(CommonConstants.Parameter.PAYMENTTYPE, paymentType);
        if (!TextUtils.isEmpty(cardId)) {
            map.put(CommonConstants.Parameter.CARDID, cardId);
        }
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.IS_FREE_PWD, isFreePwd);
        observe(mCommonService.payZesaOrder(map)).subscribe(baseObserver);
    }

    public void payBuyOrder(String airtimePlanCode,String trxOrderNo, String paymentType, String cardId, String pin,
                             String isFreePwd, BaseObserver<PaymentSuccessBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.TRX_ORDER_NO, trxOrderNo);
        map.put(CommonConstants.Parameter.PAYMENTTYPE, paymentType);
        if (!TextUtils.isEmpty(cardId)) {
            map.put(CommonConstants.Parameter.CARDID, cardId);
        }
        if (!TextUtils.isEmpty(airtimePlanCode)){
            map.put(CommonConstants.Parameter.AIRTIMEPLANCODE, airtimePlanCode);
        }
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.IS_FREE_PWD, isFreePwd);
        observe(mCommonService.payBuyOrder(map)).subscribe(baseObserver);
    }

    public void reZesaToken(String meterNo, String amt,
                            String token,
                            String pin,
                            BaseObserver<ZesaMerBean> baseObserver) {
//        meterNo
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.METERNO, meterNo);
        map.put(CommonConstants.Parameter.TRX_AMT, amt);
        map.put(CommonConstants.Parameter.TOKEN, token);
        map.put(CommonConstants.Parameter.PIN, pin);
        observe(mCommonService.reZesaToken(map)).subscribe(baseObserver);
    }


    public void requestsBuyAirtimeOrBundle(String billSubType, String rcvMobile, String buyAmount, String currency, String planCode, String bundleId, String pin, String isFreePwd, BaseObserver<AirtimeSuccessBean> baseObserver) {
        Map<String, Object> map = new HashMap<>(2);
        map.put(CommonConstants.Parameter.BILL_SUB_TYPE, billSubType);
        map.put(CommonConstants.Parameter.RCVMOBILE, rcvMobile);
        map.put(CommonConstants.Parameter.BUY_AMOUNT, buyAmount);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.PLAN_CODE, planCode);
        map.put(CommonConstants.Parameter.BUNDLE_ID, bundleId);
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.IS_FREE_PWD, isFreePwd);

        observe(mCommonService.requestsBuyAirtimeOrBundle(map)).subscribe(baseObserver);
    }

    /**
     * 请求订单信息
     *
     * @param orderSource  订单类型
     * @param payToken     订单的token
     * @param baseObserver 回调
     */
    public void requestOrderInfo(String userId, String orderSource, String payToken, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(orderSource)) {
            map.put(CommonConstants.Parameter.ORDERSOURCE, orderSource);
        }
        map.put(CommonConstants.Parameter.PAYTOKEN, payToken);
        map.put(CommonConstants.Parameter.USERID, userId);
        observe(mCommonService.requestOrderInfo(map)).subscribe(baseObserver);
    }

    public void requestMerchantOrder(String billerCategory, String recMerNo, String currency, String amt, String recAccountNo, String firstName,
                                     String middleName, String lastName, String term, String classNo,
                                     String city, String address, String billerPaymentType, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.BILL_CATEGORY, billerCategory);
        if ("03".equals(billerCategory) || "06".equals(billerCategory)){
            map.put(CommonConstants.Parameter.RECMERNO, recMerNo);
        }
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.AMT, amt);
        if ("01".equals(billerCategory) || "04".equals(billerCategory) || "06".equals(billerCategory)) {
            map.put(CommonConstants.Parameter.ACCOUNT_NO, recAccountNo);
        }
        if ("03".equals(billerCategory) || "05".equals(billerCategory)) {
            map.put(CommonConstants.RegisterParameter.FIRSTNAME, firstName);
            if (!TextUtils.isEmpty(middleName)) {
                map.put(CommonConstants.RegisterParameter.MIDDLENAME, middleName);
            }
            map.put(CommonConstants.RegisterParameter.LASTNAME, lastName);
        }
        if ("03".equals(billerCategory)) {
            map.put(CommonConstants.Parameter.TERM, term);
            map.put(CommonConstants.Parameter.CLASSNO, classNo);
        }
        if ("05".equals(billerCategory)) {
            map.put(CommonConstants.Parameter.CITY, city);
            map.put(CommonConstants.RegisterParameter.ADDRESS, address);
            map.put(CommonConstants.Parameter.BILL_PAYMENTTYPE, billerPaymentType);
        }
        observe(mCommonService.requestMerchantOrder(map)).subscribe(baseObserver);
    }

    public void requestMerCodePayOrder(String merchantCode, String amt, String currency, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.MERCODE, merchantCode);
        map.put(CommonConstants.Parameter.AMT, amt);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestMerCodePayOrder(map)).subscribe(baseObserver);
    }

    public void requestZesaOrder(String meterNo, String meterName, String trxAmt, String currency, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.METERNO, meterNo);
        map.put(CommonConstants.Parameter.METER_NAME, meterName);
        map.put(CommonConstants.Parameter.TRX_AMT, trxAmt);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestZesaOrder(map)).subscribe(baseObserver);
    }

    public void requestBuyOrder(String billSubType, String rcvMobile, String buyAmount, String currency, String planCode, String bundleId, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.BILL_SUB_TYPE, billSubType);
        map.put(CommonConstants.Parameter.RCVMOBILE, rcvMobile);
        map.put(CommonConstants.Parameter.BUY_AMOUNT, buyAmount);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        if (!TextUtils.isEmpty(planCode)) {
            map.put(CommonConstants.Parameter.PLAN_CODE, planCode);
        }
        if (!TextUtils.isEmpty(bundleId)) {
            map.put(CommonConstants.Parameter.BUNDLE_ID, bundleId);
        }
        observe(mCommonService.requestBuyOrder(map)).subscribe(baseObserver);
    }

    public void queryMarketingInfo(String payToken, String trxOrderNo, String trxTransType, BaseObserver<MarketingBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.PAYTOKEN, payToken);
        map.put(CommonConstants.Parameter.TRX_ORDER_NO, trxOrderNo);
        map.put(CommonConstants.Parameter.TRX_TRANS_NO, trxTransType);
        observe(mCommonService.queryMarketingInfo(map)).subscribe(baseObserver);
    }

    public void requestUseMarketing(RequestUseMarketingBean bean, BaseObserver<MarketingBean> baseObserver) {
        observe(mCommonService.requestUseMarketing(bean)).subscribe(baseObserver);
    }


    public void requestCreateQrCodeOrder(String userId, String paymentProduct, String recMerNo, String recMerName, String checkstandNo, String amt, String qrCode, String currency, BaseObserver<OrderInfoBean> baseObserver) {
        Map<String, String> qrCodeMap = new HashMap<>();
        qrCodeMap.put(CommonConstants.Parameter.PAYMENTPRODUCT, paymentProduct);
        qrCodeMap.put(CommonConstants.Parameter.RECMERNO, recMerNo);
        qrCodeMap.put(CommonConstants.Parameter.RECMERNAME, recMerName);
        qrCodeMap.put(CommonConstants.Parameter.CHECKSTANDNO, checkstandNo);
        qrCodeMap.put(CommonConstants.Parameter.AMT, amt);
        qrCodeMap.put(CommonConstants.Parameter.QRCODE, qrCode);
        qrCodeMap.put(CommonConstants.Parameter.USERID, userId);
        qrCodeMap.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mCommonService.requestCreateQrCodeOrder(qrCodeMap)).subscribe(baseObserver);
    }


    /**
     * 获取用户信息
     */
    public void requestUserInfo(String mobile, String idCard, String userName, BaseObserver<UserInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.IDCARD, idCard);
        map.put(UserConstants.Parameter.USERNAME, userName);
        map.put(UserConstants.Parameter.MOBILE, mobile);
//        map.put(UserConstants.Parameter.USERID, userId);
        observe(mCommonService.requestUserInfo(map)).subscribe(baseObserver);
    }

    /**
     * 获取亲子用户信息
     */
    public void requestUserInfoById(BaseObserver<UserInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        //map.put(UserConstants.Parameter.IDCARD, idCard);
        //map.put(UserConstants.Parameter.USERNAME, userName);
        //map.put(UserConstants.Parameter.USERID, userId);
        observe(mCommonService.requestUserInfoById(map)).subscribe(baseObserver);
    }

    public void requestCheckUser(String mobile, String userName, String idCard, BaseObserver<UserInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.MOBILE, mobile);
        map.put(UserConstants.Parameter.USERNAME, userName);
        map.put(UserConstants.Parameter.IDCARD, idCard);
//        map.put(UserConstants.Parameter.USERID, userId);
        observe(mCommonService.requestUserInfo(map)).subscribe(baseObserver);
    }

    public void getUserInfoByIdCardAndMobile(String mobile,String idCard, BaseObserver<UserInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.MOBILE, mobile);
        map.put(UserConstants.Parameter.IDCARD, idCard);
        observe(mCommonService.getUserInfoByIdCardAndMobile(map)).subscribe(baseObserver);
    }

    public void requestCheckJuniorAccount(String juniorUserId, String idNumber, String userName, BaseObserver<UserInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.JUNIOR_USER_ID, juniorUserId);
        map.put(UserConstants.Parameter.ID_NUMBER, idNumber);
        map.put(UserConstants.Parameter.USERNAME, userName);
//        map.put(UserConstants.Parameter.USERID, userId);
        observe(mCommonService.requestCheckJuniorAccount(map)).subscribe(baseObserver);
    }


    /**
     * 获取验证码接口
     */
    public void requestCode(String mobile, String messageTemplateType, String mobileAreaCode, BaseObserver<CodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put("mobile", mobile);
        map.put("messageTemplateType", messageTemplateType);
        if (mobileAreaCode.equals("")) {
            mobileAreaCode = "263";
        }
        map.put("mobileAreaCode", mobileAreaCode);
        observe(mCommonService.requestCode(map)).subscribe(baseObserver);
    }


    /**
     * 校验验证码接口
     * 亲子注册传token
     */
    public void requestValidateSms(String mobile, String smsCode, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.MOBILE, mobile);
        map.put(CommonConstants.Parameter.SMSCODE, smsCode);
        observe(mCommonService.requestValidateSms(map)).subscribe(baseObserver);
    }

    /**
     * 切换主设备
     *
     * @param baseObserver 回调
     */
    public void requestChangeMasterDevice(String userId, String tempDeviceId, BaseObserverNoError<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.TEMPDEVICEID, tempDeviceId);
        map.put(CommonConstants.Parameter.USERID, userId);
        observe(mCommonService.requestChangeMasterDevice(map)).subscribe(baseObserver);
    }

    public void requestJuniorValidateSms(String token, String smsCode, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.SEND_CODE_TOKEN, token);
        map.put(CommonConstants.Parameter.SEND_CODE, smsCode);
        observe(mCommonService.requestJuniorValidateSms(map)).subscribe(baseObserver);
    }


    public void checkBalanceOrder(String currency, String queryType, BaseObserver<BalanceBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.QUERY_TYPE, queryType);
        observe(mCommonService.checkBalanceOrder(map)).subscribe(baseObserver);
    }

}
