package om.rrtx.mobile.functioncommon.activity

import android.app.Activity
import android.content.Intent
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.common_base_title.backIv
import kotlinx.android.synthetic.main.common_base_title.leftBg
import kotlinx.android.synthetic.main.common_base_title.titleTv
import kotlinx.android.synthetic.main.common_debit_result.icon
import kotlinx.android.synthetic.main.common_debit_result.nextTv
import kotlinx.android.synthetic.main.common_debit_result.text1
import kotlinx.android.synthetic.main.common_debit_result.text3
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.bean.ComResultBean
import om.rrtx.mobile.functioncommon.databinding.CommonDebitResultBinding
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils

class DebitResultActivity : BaseActivity<CommonDebitResultBinding>() {
    private lateinit var mBean: ComResultBean
    private var requestCode: Int? = null
    override fun createContentView() = R.layout.common_debit_result

    override fun doGetExtra() {
        requestCode = intent.getIntExtra(CommonConstants.Transmit.REQUESTCODE, 0)
        val json = intent.getStringExtra(CommonConstants.Transmit.JSON)
        mBean = Gson().fromJson(json, ComResultBean::class.java)
    }

    override fun initWorkspaceAction() {

    }

    override fun initView() {
        ImmersionBar.with(this).statusBarView(R.id.statusView).statusBarDarkFont(true).init()

        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))

        when (mBean.status) {
            CommonConstants.AirTimeStatus.SUCCESS -> {
                titleTv.text = mBean.title
                mBean.s1 = getString(R.string.successful)
                text1.text = mBean.s1
                text3.text = mBean.s3
                mBean.iconID = R.drawable.common_ic_success_ye
            }

            CommonConstants.AirTimeStatus.Fail, CommonConstants.AirTimeStatus.Error -> {
                titleTv.text = mBean.title
                mBean.s1 = getString(R.string.failed)
                text1.text = mBean.s1
                text3.text = mBean.s3
                mBean.iconID = R.drawable.common_ic_fail
            }

            else -> {
                titleTv.text = mBean.title
                mBean.s1 = getString(R.string.successful)
                text1.text = mBean.s1
                text3.text = mBean.s3
                mBean.iconID = R.drawable.common_ic_success_ye
            }
        }

        if (mBean.iconID != 0) icon.setBackgroundResource(mBean.iconID)

        if (StringUtils.isValidString(mBean.btText)) {
            nextTv.text = mBean.btText
        }
    }


    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    nextTv, leftBg -> jump()

                }
            }

        }.apply {
            nextTv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }
    }

    private fun jump() {
        when (mBean.flag) {
            BaseConstants.JumpFlag.PAY_MERCHANT_CODE -> {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }

            BaseConstants.JumpFlag.NO_PIN_PAY -> {
                ARouter.getInstance().build(ARouterPath.SecurityPath.NoPinPayActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }

            BaseConstants.JumpFlag.HOMEJUMP, BaseConstants.JumpFlag.BUY_AIRTIME_BUNDLE -> {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }

            BaseConstants.JumpFlag.Cashier_Pay -> {
                if (requestCode == CommonConstants.ResultCode.Request) {
                    setResult(CommonConstants.ResultCode.Success)
                    finish()
                } else {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation()
                }
            }

            BaseConstants.JumpFlag.Auto_Debit -> {
                setResult(CommonConstants.ResultCode.Success)
                finish()
            }

            BaseConstants.JumpFlag.Upgrade_junior_Account -> {
                ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }

            BaseConstants.JumpFlag.Delete_junior_Account -> {
                ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountManagementActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }
        }
    }

    companion object {
        @JvmStatic
        @JvmOverloads
        fun jump(context: Activity, bean: ComResultBean, requestCode: Int? = null) {
            val intent = Intent(context, DebitResultActivity::class.java)
            intent.putExtra(CommonConstants.Transmit.JSON, Gson().toJson(bean))
            intent.putExtra(CommonConstants.Transmit.REQUESTCODE, requestCode)
            if (requestCode == null) {
                context.startActivity(intent)
            } else {
                context.startActivityForResult(intent, requestCode)
            }
        }
    }

    override fun onBackPressed() {
        jump()
    }
}