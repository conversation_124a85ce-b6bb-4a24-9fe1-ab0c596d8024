package om.rrtx.mobile.functioncommon.bean;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PaymentBean {

    private int selectPosition;
    private List<PaymentMethodListBean> paymentMethodList;
    private List<PaymentTypeListBean> paymentTypeList;

    public int getSelectPosition() {
        return selectPosition;
    }

    public void setSelectPosition(int selectPosition) {
        this.selectPosition = selectPosition;
    }

    public List<PaymentMethodListBean> getPaymentMethodList() {
        return paymentMethodList;
    }

    public void setPaymentMethodList(List<PaymentMethodListBean> paymentMethodList) {
        this.paymentMethodList = paymentMethodList;
    }

    public List<PaymentTypeListBean> getPaymentTypeList() {
        return paymentTypeList;
    }

    public void setPaymentTypeList(List<PaymentTypeListBean> paymentTypeList) {
        this.paymentTypeList = paymentTypeList;
    }

    public static class PaymentMethodListBean {
        /**
         * isOpen : 1
         * isAvailable : 1
         * isBalanceEnough : 1
         * isSelected : 1
         * paymentType : 02
         * balance : 130559.71
         * currency : USD
         * bankNo :
         * bankName :
         * cardNo :
         */

        private String isOpen;
        private String isAvailable;
        private String isBalanceEnough;
        private String isSelected;
        private String paymentType;
        private String balance;
        private String currency;
        private String bankNo;
        private String bankName;
        private String cardNo;
        private String cardId;
        private String freePwd ="";

        public String getFreePwd() {
            return freePwd;
        }

        public void setFreePwd(String freePwd) {
            this.freePwd = freePwd;
        }

        public String getIsOpen() {
            return isOpen;
        }

        public void setIsOpen(String isOpen) {
            this.isOpen = isOpen;
        }

        public String getIsAvailable() {
            return isAvailable;
        }

        public void setIsAvailable(String isAvailable) {
            this.isAvailable = isAvailable;
        }

        public String getIsBalanceEnough() {
            return isBalanceEnough;
        }

        public void setIsBalanceEnough(String isBalanceEnough) {
            this.isBalanceEnough = isBalanceEnough;
        }

        public String getIsSelected() {
            return isSelected;
        }

        public void setIsSelected(String isSelected) {
            this.isSelected = isSelected;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public String getBalance() {
            return balance;
        }

        public void setBalance(String balance) {
            this.balance = balance;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getBankNo() {
            return bankNo;
        }

        public void setBankNo(String bankNo) {
            this.bankNo = bankNo;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }

        public String getCardId() {
            return cardId;
        }

        public void setCardId(String cardId) {
            this.cardId = cardId;
        }

        @Override
        public String toString() {
            return "PaymentMethodListBean{" +
                    "isOpen='" + isOpen + '\'' +
                    ", isAvailable='" + isAvailable + '\'' +
                    ", isBalanceEnough='" + isBalanceEnough + '\'' +
                    ", isSelected='" + isSelected + '\'' +
                    ", paymentType='" + paymentType + '\'' +
                    ", balance='" + balance + '\'' +
                    ", currency='" + currency + '\'' +
                    ", bankNo='" + bankNo + '\'' +
                    ", bankName='" + bankName + '\'' +
                    ", cardNo='" + cardNo + '\'' +
                    ", cardId='" + cardId + '\'' +
                    '}';
        }
    }

    public static class PaymentTypeListBean {
        /**
         * paymentType : 02
         * isOpen : 1
         * feeAmt : 10.00
         * deductType : 1
         */

        private String paymentType;
        private String isOpen;
        private String feeAmt;
        private String taxAmt;
        /**
         * 0 外扣 1内扣
         */
        private String deductType;
        private String currency;
        private String taxDeductType;
        private String cashbackAmt;
        private String paymentAmt;

        public void setTaxAmt(String taxAmt) {
            this.taxAmt = taxAmt;
        }

        public String getTaxDeductType() {
            return taxDeductType;
        }

        public void setTaxDeductType(String taxDeductType) {
            this.taxDeductType = taxDeductType;
        }

        public String getCashbackAmt() {
            return cashbackAmt;
        }

        public void setCashbackAmt(String cashbackAmt) {
            this.cashbackAmt = cashbackAmt;
        }

        public String getPaymentAmt() {
            return paymentAmt;
        }

        public void setPaymentAmt(String paymentAmt) {
            this.paymentAmt = paymentAmt;
        }

        public String getTaxAmt() {
            return taxAmt;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public String getIsOpen() {
            return isOpen;
        }

        public void setIsOpen(String isOpen) {
            this.isOpen = isOpen;
        }

        public String getFeeAmt() {
            return feeAmt;
        }

        public void setFeeAmt(String feeAmt) {
            this.feeAmt = feeAmt;
        }

        public String getDeductType() {
            return deductType;
        }

        public void setDeductType(String deductType) {
            this.deductType = deductType;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
