package om.rrtx.mobile.functioncommon.dialog

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.gson.Gson
import om.rrtx.mobile.functioncommon.CashierCallBackManager1
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.PayMethodLiveData
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.adapter.OrderInfoAdapter
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.OrderInfoItemBean
import om.rrtx.mobile.functioncommon.bean.PaymentBean
import om.rrtx.mobile.functioncommon.bean.PaymentBean.PaymentTypeListBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack1
import om.rrtx.mobile.functioncommon.databinding.CommonNewDialogFragmentDetailsBottomBinding
import om.rrtx.mobile.functioncommon.dialog.type.CashierBuyAImpl1
import om.rrtx.mobile.functioncommon.dialog.type.CashierMerchantCodeCitympl1
import om.rrtx.mobile.functioncommon.dialog.type.CashierPayImpl1
import om.rrtx.mobile.functioncommon.dialog.type.CashierPaymentImpl1
import om.rrtx.mobile.functioncommon.dialog.type.CashierZESAImpl1
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil.Companion.getTwoDecimal
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration


class NewCashierDetailsBottomFragment : BaseBottomFragment<Any, BasePresenter<Any>>() {

    private lateinit var mCashierOrderInfoBean: CashierOrderInfoBean
    lateinit var mPayMethodLiveData: PayMethodLiveData
    private lateinit var mPaymentBean: PaymentBean
    private lateinit var mSelectPaymentMethod: PaymentBean.PaymentMethodListBean
    private lateinit var mSelectPayType: PaymentTypeListBean
    private lateinit var mOrderInfoAdapter: OrderInfoAdapter
    private lateinit var mClosedDialog: DoubleDialog
    private lateinit var cashierPay: CashierPayImpl1
    private lateinit var mCashierBankListFragment: CashierBankListFragment
    private lateinit var dataBinding: CommonNewDialogFragmentDetailsBottomBinding

    private var mCurrency: String = ""

    private val data = arrayListOf<OrderInfoItemBean>()


    companion object {
        @JvmStatic
        fun newInstance(orderInfoJson: String?): NewCashierDetailsBottomFragment {
            val args = Bundle()
            //这里的orderInfo就需要包含订单信息了
            args.putString(CommonConstants.Transmit.CASHIERORDERINFO, orderInfoJson)
            val fragment = NewCashierDetailsBottomFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            val bottomSheetDialog = dialog
            bottomSheetDialog.setOnShowListener { dialogInterface: DialogInterface? ->
                val bottomSheet =
                    bottomSheetDialog.findViewById<FrameLayout>(R.id.design_bottom_sheet)
                val bottomSheetBehavior: BottomSheetBehavior<*> =
                    BottomSheetBehavior.from(bottomSheet!!)
                //默认展开
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                bottomSheetBehavior.isDraggable = false
            }
        }
        return dialog
    }


    override fun show(manager: FragmentManager, tag: String?) {
        try {
            //在每个add事务前增加一个remove事务，防止连续的add
            manager.beginTransaction().remove(this).commit()
            super.show(manager, tag)
        } catch (e: Exception) {
            //同一实例使用不同的tag会异常,这里捕获一下
            e.printStackTrace()
        }
    }

    override fun createViewLayoutId() = R.layout.common_new_dialog_fragment_details_bottom

    override fun createPresenter(): BasePresenter<Any> = BasePresenter()
    override fun initView(rootView: View) {
        initData()
        isCancelable = false
        dataBinding = CommonNewDialogFragmentDetailsBottomBinding.bind(rootView)
        dataBinding.apply {
            orderInfoRv.apply {
                layoutManager = LinearLayoutManager(mContext)
                mOrderInfoAdapter = OrderInfoAdapter()
                adapter = mOrderInfoAdapter
                addItemDecoration(SpacesItemDecoration(24.pt2px()))
            }
            initVMListener()
        }

    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    dataBinding.leftBg -> clickClose()

                    dataBinding.payTv -> cashierPay.nextClick(
                        mSelectPaymentMethod.freePwd,
                        this@NewCashierDetailsBottomFragment
                    )
                }
            }
        }.apply {
            dataBinding.leftBg.setOnClickListener(this)
            dataBinding.payTv.setOnClickListener(this)
        }
    }

    override fun initDate() {

    }

    fun initData() {
        //监听支付类型的改变
        val activity = activity as AppCompatActivity
        mPayMethodLiveData = ViewModelProvider(activity).get(PayMethodLiveData::class.java)

        //订单信息
        val arguments = arguments
        if (arguments != null) {
            val mOrderInfoJson = arguments.getString(CommonConstants.Transmit.CASHIERORDERINFO)
            mCashierOrderInfoBean =
                Gson().fromJson(mOrderInfoJson, CashierOrderInfoBean::class.java)
            mCurrency = mCashierOrderInfoBean.currency
        }

        when (mCashierOrderInfoBean.payType) {
            CommonConstants.CashierPaymentType.Cashier_Payment,CommonConstants.CashierPaymentType.MMERCODE_PAY -> {
                // 收银台支付
                cashierPay =
                    CashierPaymentImpl1(activity, mCashierOrderInfoBean, mPayMethodLiveData, data)
            }

            CommonConstants.CashierPaymentType.MERCHANT_CODE -> {
                // 收银台支付
                cashierPay =
                    CashierMerchantCodeCitympl1(
                        activity,
                        mCashierOrderInfoBean,
                        mPayMethodLiveData,
                        data
                    )
            }

            CommonConstants.CashierPaymentType.ZESA_FEE -> {
                // 收银台支付
                cashierPay =
                    CashierZESAImpl1(activity, mCashierOrderInfoBean, mPayMethodLiveData, data)
            }

            CommonConstants.CashierPaymentType.AIRTIME_BUNDLE -> {
                // 收银台支付
                cashierPay =
                    CashierBuyAImpl1(activity, mCashierOrderInfoBean, mPayMethodLiveData, data)
            }
        }
    }

    private fun initVMListener() {
        val activity = activity as AppCompatActivity

        //监听支付类型的改变
        mPayMethodLiveData.paymentTypeLV.removeObservers(activity)
        mPayMethodLiveData.paymentTypeLV.observe(activity) { paymentTypeBean: PaymentBean ->
            if (paymentTypeBean.paymentMethodList.size == 0) return@observe
            data.clear()
            cashierPay.initData()
            //对支付方式进行复制
            mPaymentBean = paymentTypeBean
            //处理支付类型
            if (mPaymentBean.paymentMethodList.size>0) {
                if (mPaymentBean.paymentMethodList.size==1){
                    mCashierOrderInfoBean.isFixMethod = true
                }
                mSelectPaymentMethod =
                    PaymentTypeHelper.getSelectPayMethod(mPaymentBean.paymentMethodList)
            }
            mSelectPayType =
                PaymentTypeHelper.getPaymentType(
                    mSelectPaymentMethod,
                    mPaymentBean.paymentTypeList
                )

            // 实付金额
            if (TextUtils.isEmpty(mSelectPayType.currency)) {
                mSelectPayType.currency = mCashierOrderInfoBean.currency
            }
            var amtAmount = mCashierOrderInfoBean.orderAmt
            if (MoneyUtil.isValidMoney(mCashierOrderInfoBean.afterDisCountAmt)){
                amtAmount = mCashierOrderInfoBean.afterDisCountAmt
            }
            dataBinding.amtTv.text =
                mSelectPayType.currency + " " + StringUtils.formatAmount(amtAmount)
            // 保留之前部分逻辑，手动添加税费和手续
            addTaxAndFee()
            // 不论余额是否足够都需要显示
            setTaxAndFee()

            //设置支付名称
            data.add(OrderInfoItemBean(getString(R.string.payment_method),
                PaymentTypeHelper.getPaymentMethodName(mContext, mSelectPaymentMethod),
                isEnough = mSelectPaymentMethod.isBalanceEnough,
                isFixMethod = mCashierOrderInfoBean.isFixMethod,
                callback = { selectPayMethod() }
            ))

            //这里要设置支付方式
            mCashierOrderInfoBean.paymentType = mSelectPaymentMethod.paymentType
            if (StringUtils.isValidString(mSelectPaymentMethod.cardId)) {
                mCashierOrderInfoBean.cardNo = mSelectPaymentMethod.cardId
            }
            // 免密支付
            val isFreePwd: String = mSelectPaymentMethod.freePwd
            if (StringUtils.isValidString(isFreePwd) && isFreePwd == "1") {
                dataBinding.payTv.setText(R.string.no_IN_Payment)
            } else {
                dataBinding.payTv.setText(R.string.pay)
            }

            // 余额不足（1）支付按钮不可用
            dataBinding.payTv.isEnabled = mSelectPaymentMethod.isBalanceEnough == "1"


            mOrderInfoAdapter.setData(data)

            if (mCashierOrderInfoBean.isFixMethod) {
                dataBinding.line.visibility = View.GONE
            } else {
                dataBinding.line.visibility = View.VISIBLE
            }
        }

        mPayMethodLiveData.paymentTypeError.observe(activity) { s: String? ->
            ToastUtil.show(activity, s)
            dismiss()
        }

        mPayMethodLiveData.errorLv.observe(activity) { s: String? ->
            ToastUtil.show(activity, s)
        }

        mPayMethodLiveData.mDismiss.observe(this) { aBoolean: Boolean ->
            if (aBoolean) {
                if (cashierPay != null) cashierPay.onClose()
                mPayMethodLiveData.mDismiss.value = false
            }
        }
    }

    private fun setTaxAndFee() {
        val currency = CurrencyUtils.setCurrency(mContext, mCurrency) + " "
        var afterDisCountAmt = mCashierOrderInfoBean.afterDisCountAmt
        if (TextUtils.isEmpty(afterDisCountAmt)){
            afterDisCountAmt = mCashierOrderInfoBean.orderAmt
        }
        //if (MoneyUtil.isValidMoney(afterDisCountAmt))
        // 优惠后金额
            data.add(
                OrderInfoItemBean(
                    getString(R.string.order_Amount),
                    currency + afterDisCountAmt
                )
            )

        //设置费率、税费
        //个人钱包用户交易时，交易确认弹窗中的详情信息，涉及税费手续费的字段全部隐藏，全部交易类型统一调整
        /*val feeAmount = mCashierOrderInfoBean.transFeeAmt
        if (!TextUtils.isEmpty(feeAmount) && feeAmount.toDouble() > 0) {
            val feeMoney = getTwoDecimal(feeAmount)
            data.add(OrderInfoItemBean(getString(R.string.fee), currency + feeMoney))
        }
        val taxAmount = mCashierOrderInfoBean.transTaxAmt
        if (!TextUtils.isEmpty(taxAmount) && taxAmount.toDouble() > 0) {
            val taxMoney = getTwoDecimal(taxAmount)
            data.add(OrderInfoItemBean(getString(R.string.tax), currency + taxMoney))
        }*/

        // 返现金额
        val cashbackAmt = mSelectPayType.cashbackAmt
        if (MoneyUtil.isValidMoney(cashbackAmt))
            data.add(
                OrderInfoItemBean(
                    getString(R.string.cashback_Account),
                    currency + cashbackAmt
                )
            )

    }

    /**
     * 有的支付流程 额外费用从支付方式获取
     *
     * @param paymentBean
     */
    private fun addTaxAndFee() {
        val payType = mCashierOrderInfoBean.payType
        val payTypeBean: PaymentTypeListBean
        when (payType) {
            CommonConstants.CashierPaymentType.Cashier_Withdrawal,
            CommonConstants.CashierPaymentType.Query_Transfer,
            CommonConstants.CashierPaymentType.Cashier_TopUp,
            CommonConstants.CashierPaymentType.MERCHANT_CODE,
            CommonConstants.CashierPaymentType.MMERCODE_PAY,
            CommonConstants.CashierPaymentType.ZESA_FEE,
            CommonConstants.CashierPaymentType.AIRTIME_BUNDLE,
            CommonConstants.CashierPaymentType.Cashier_Payment,
            -> {
                payTypeBean = mSelectPayType
                mCashierOrderInfoBean.transFeeAmt = payTypeBean.feeAmt
                mCashierOrderInfoBean.transTaxAmt = payTypeBean.taxAmt
            }

            else -> {}
        }
    }

    private fun selectPayMethod() {
        mCashierBankListFragment =
            CashierBankListFragment.newInstance(Gson().toJson(mCashierOrderInfoBean))
        mCashierBankListFragment.show(
            childFragmentManager,
            CashierBankListFragment::class.java.simpleName
        )
    }

    private fun clickClose() {
        mClosedDialog = DoubleDialog(mContext)
        mClosedDialog.setDoubleCallback(object : DoubleDialog.DoubleCallback {
            override fun leftCallback() {
                mClosedDialog.dismiss()
                onBack()
                CashierCallBackManager1.getInstance().cashierCallBack.leftClick()

            }

            override fun rightCallback() {
                mClosedDialog.dismiss()
                //CashierCallBackManager1.getInstance().cashierCallBack.rightClick()
                //onBack()
            }
        })
        mClosedDialog.show()
        mClosedDialog.setMyTitle(getString(R.string.common_alert_prompt))


        val payType = mCashierOrderInfoBean.payType
        when (payType) {
            CommonConstants.CashierPaymentType.Cashier_Payment -> {
                mClosedDialog
                    .setLeftStr(resources.getString(R.string.common_alert_cancel_pay))
                    .setRightStr(getString(R.string.common_alert_keep_pay))
                    .setContentStr(getString(R.string.checkout_alert_cancel_pay_tip))
            }

            else -> mClosedDialog
                .setLeftStr(resources.getString(R.string.common_alert_cancel_pay))
                .setRightStr(getString(R.string.common_alert_keep_pay))
                .setContentStr(getString(R.string.checkout_alert_cancel_pay_tip))
        }
    }


    /**
     * 关闭收银台
     */
    private fun onBack() {
        if (this@NewCashierDetailsBottomFragment::cashierPay.isInitialized) cashierPay.onClose()
        dismiss()
    }

    /**
     * 监听的初始化.
     *
     * @param cashierCallBack 回调
     */
    fun setCashierCallBack(cashierCallBack: CashierCallBack1) {
        CashierCallBackManager1.getInstance().init(cashierCallBack)
    }
}

