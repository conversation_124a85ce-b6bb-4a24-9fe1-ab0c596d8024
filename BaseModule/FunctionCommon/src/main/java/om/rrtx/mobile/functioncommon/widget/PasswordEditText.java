package om.rrtx.mobile.functioncommon.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.inputmethod.EditorInfo;

import androidx.appcompat.widget.AppCompatEditText;

import java.util.ArrayList;

import om.rrtx.mobile.functioncommon.R;


/**
 * https://github.com/StevenYan88/PayPasswordEditText
 */
public class PasswordEditText extends AppCompatEditText {
    // 画笔
    private Paint mPaint;
    // 一个密码所占的宽度
    private int mPasswordItemWidth;
    // 密码的个数默认为4位数
    private int mPasswordNumber = 4;
    // 背景边框颜色
    private int mBgColor = Color.parseColor("#c9cdd4");
    // 背景边框大小
    private int mBgSize = 1;
    // 背景边框圆角大小
    private int mBgCorner = 0;
    // 分割线的颜色
    private int mDivisionLineColor = mBgColor;
    // 分割线的大小
    private int mDivisionLineSize = 1;
    // 密码圆点的颜色
    private int mPasswordColor = mDivisionLineColor;
    // 密码圆点的半径大小
    private int mPasswordRadius = 4;
    //密码输入完毕需要一个接口回调出去
    private PasswordFullListener mPasswordFullListener;

    public PasswordEditText(Context context) {
        this(context, null);
    }

    public PasswordEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        initPaint();
        initAttributeSet(context, attrs);
        // 设置输入模式是密码
        setInputType(EditorInfo.TYPE_TEXT_VARIATION_PASSWORD);
        // 不显示光标
        setCursorVisible(false);
    }

    /**
     * 初始化属性
     */
    private void initAttributeSet(Context context, AttributeSet attrs) {
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.Common_PasswordEditText);
        // 获取大小
        mDivisionLineSize = (int) array.getDimension(R.styleable.Common_PasswordEditText_common_bgSize, dip2px(mDivisionLineSize));
        mPasswordRadius = (int) array.getDimension(R.styleable.Common_PasswordEditText_common_passwordRadius, dip2px(mPasswordRadius));
        mBgSize = (int) array.getDimension(R.styleable.Common_PasswordEditText_common_bgSize, dip2px(mBgSize));
        mBgCorner = (int) array.getDimension(R.styleable.Common_PasswordEditText_common_bgCorner, 0);
        // 获取颜色
        mBgColor = array.getColor(R.styleable.Common_PasswordEditText_common_bgColor, mBgColor);
        mDivisionLineColor = array.getColor(R.styleable.Common_PasswordEditText_common_divisionLineColor, mDivisionLineColor);
        mPasswordColor = array.getColor(R.styleable.Common_PasswordEditText_common_passwordColor, mDivisionLineColor);
        array.recycle();
    }

    /**
     * 初始化画笔
     */
    private void initPaint() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
    }

    /**
     * dip 转 px
     */
    private int dip2px(int dip) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dip, getResources().getDisplayMetrics());
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int passwordWidth = getWidth() - (mPasswordNumber - 1) * mDivisionLineSize;
        mPasswordItemWidth = passwordWidth / mPasswordNumber;
        // 连体框
        // 绘制背景
//        drawBg(canvas);
        // 绘制分割线
//        drawDivisionLine(canvas);
        // 独立框，很多属性没对外暴露，后面补
        initFrame();
        drawFrame(canvas);
        // 绘制密码
        drawHidePassword(canvas);

        if (mPasswordFullListener != null) {
            //获取输入的密码
            String password = String.valueOf(getText());
            if (password.length() == mPasswordNumber) {
                mPasswordFullListener.passwordFull(password);
            } else if (password.length() == 0) {
                mPasswordFullListener.passwordClear();
            }
        }
    }

    /**
     * 绘制背景
     */
    private void drawBg(Canvas canvas) {
        mPaint.setColor(mBgColor);
        // 设置画笔为空心
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mBgSize);
        RectF rectF = new RectF(mBgSize, mBgSize, getWidth() - mBgSize, getHeight() - mBgSize);
        // 如果没有设置圆角，就画矩形
        if (mBgCorner == 0) {
            canvas.drawRect(rectF, mPaint);
        } else {
            // 如果有设置圆角就画圆矩形
            canvas.drawRoundRect(rectF, mBgCorner, mBgCorner, mPaint);
        }
    }

    /**
     * 绘制分割线
     */
    private void drawDivisionLine(Canvas canvas) {
        mPaint.setStrokeWidth(mDivisionLineSize);
        mPaint.setColor(mDivisionLineColor);
        for (int i = 0; i < mPasswordNumber - 1; i++) {
            int startX = (i + 1) * mDivisionLineSize + (i + 1) * mPasswordItemWidth + mBgSize;
            canvas.drawLine(startX, mBgSize, startX, getHeight() - mBgSize, mPaint);
        }
    }

    private ArrayList<RectF> frames = new ArrayList<>();
    private int frameLength;

    public void initFrame() {
        int width = getWidth();
        int height = getHeight();
        // 间隔数
        int spaceNum = mPasswordNumber - 1;
        if (frameLength == 0) {
            // 15 10 10 10 15
            int reWidth = (int) (width / (mPasswordNumber + (spaceNum * 0.5) + 3));
            int reHeight = (int) (height/2.5);
            frameLength = Math.min(reWidth, reHeight);
        }
        // 默然间距一半边长
        int space = (int) (frameLength * 0.5);
        int left = (width - (frameLength * mPasswordNumber) - (space * spaceNum)) / 2;
        int top = (height - frameLength) / 2;
        int right = left + frameLength;
        int bottom = top + frameLength;
        for (int i = 0; i < mPasswordNumber; i++) {
            //参数1：矩形【左】上角坐标x;参数2：矩形【左】上角坐标y; 参数3:矩形【右】下角坐标x ;参数4：矩形【右】下角坐标y
            RectF rectF = new RectF(left, top, right, bottom);
            frames.add(rectF);
            left += frameLength + space;
            right += frameLength + space;
        }
    }


    /**
     * 绘制方框
     */
    private void drawFrame(Canvas canvas) {
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mDivisionLineSize);
        mPaint.setColor(mDivisionLineColor);
        for (RectF frame : frames) {
//            canvas.drawRect(frame, mPaint);
            canvas.drawRoundRect(frame, dip2px(4), dip2px(4), mPaint);
        }
//        for (int i = 0; i < mPasswordNumber - 1; i++) {
//            int startX = (i + 1) * mDivisionLineSize + (i + 1) * mPasswordItemWidth + mBgSize;
//            canvas.drawLine(startX, mBgSize, startX, getHeight() - mBgSize, mPaint);
//        }
    }

    /**
     * 绘制隐藏的密码
     */
    private void drawHidePassword(Canvas canvas) {
        int passwordLength = String.valueOf(getText()).length();
        mPaint.setColor(mPasswordColor);
        // 设置画笔为实心
        mPaint.setStyle(Paint.Style.FILL);
        int l = frameLength / 2;
        for (int i = 0; i < passwordLength; i++) {
            RectF frame = frames.get(i);
//            int cx = i * mDivisionLineSize + i * mPasswordItemWidth + mPasswordItemWidth / 2 + mBgSize;
//            canvas.drawCircle(cx, getHeight() / 2, mPasswordRadius, mPaint);
            canvas.drawCircle(frame.left + l, frame.top + l, mPasswordRadius, mPaint);
        }
    }


    /**
     * 添加密码
     *
     * @param number 添加的号码
     */
    public void addPassword(String number) {
        if (TextUtils.isEmpty(number)) {
            return;
        }
        //把密码取取出来
        String password = String.valueOf(getText());
        if (password.length() <= mPasswordNumber) {
            //密码叠加
            password += number;
            setText(password);
        }
    }

    public void cancelPassword() {
        //把密码取取出来
        String password = "";
        setText(password);
    }

    /**
     * 删除密码
     */
    public void deletePassword() {
        String password = String.valueOf(getText());
        if (TextUtils.isEmpty(password)) {
            return;
        }
        password = password.substring(0, password.length() - 1);
        setText(password);
    }

    /**
     * 设置一个密码输入完毕的监听器
     *
     * @param passwordFullListener Listener
     */
    public void setPasswordFullListener(PasswordFullListener passwordFullListener) {
        this.mPasswordFullListener = passwordFullListener;
    }

    public interface PasswordFullListener {
        void passwordFull(String password);

        void passwordClear();
    }
}