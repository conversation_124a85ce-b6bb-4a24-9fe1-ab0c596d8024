package om.rrtx.mobile.functioncommon.bean;

/**
 * <AUTHOR>
 */
public class WithdrawalOrderBean {
    /**
     * orderStatus : 10
     * feeAmt : 1.00
     * amt : 12.00
     * cardNo : 1231231231213121313
     * bankName : Bank of America
     * createTime : 2020-03-14 13:27:06
     * orderId : 8131111256804298827
     * tradeType : 20
     */

    private String failMsg;
    private String trxStatus;
    private String orderStatus;
    private String feeAmt;
    private String amt;
    private String cardNo;
    private String bankName;
    private String createTime;
    private String orderId;
    private String tradeType;
    private String currency;
    private String actualPayAmt;

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(String trxStatus) {
        this.trxStatus = trxStatus;
    }

    public void setActualPayAmt(String actualPayAmt) {
        this.actualPayAmt = actualPayAmt;
    }

    public String getActualPayAmt() {
        return actualPayAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(String feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
