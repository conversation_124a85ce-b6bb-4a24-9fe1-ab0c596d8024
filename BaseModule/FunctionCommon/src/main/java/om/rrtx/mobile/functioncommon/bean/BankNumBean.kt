package om.rrtx.mobile.functioncommon.bean

class BankNumBean (val bankCardNum:String){
}
data class BalanceCertificateBean(
    val balance: String,
    var currency: String,
    var lastBalanceEnquiryTime: String,
    val certificate: String
)
data class CertificateBean(
    val certificate: String
)


data class TopUpSuccessBean(
    val actualAmt: String,
    val chlRespCode: String,
    val chlRespMsg: String,
    val currency: String,
    val isTradeSuccess: Boolean,
    val realName: String,
    val topUpAmt: String,
    val topUpFee: String,
    val topUpFlowNo: String,
    val topUpOrderNo: String,
)
