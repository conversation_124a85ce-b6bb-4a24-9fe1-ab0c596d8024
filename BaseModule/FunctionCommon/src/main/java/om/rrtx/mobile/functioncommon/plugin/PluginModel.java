package om.rrtx.mobile.functioncommon.plugin;

import java.util.HashMap;
import java.util.Map;

import om.rrtx.mobile.functioncommon.CommonService;
import om.rrtx.mobile.functioncommon.bean.NewListsBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;

/**
 * <AUTHOR>
 */
public class PluginModel extends BaseLoader {
    private CommonService mHomeService;

    public PluginModel() {
        mHomeService = RetrofitServiceManager.getInstance().create(CommonService.class);
    }

    public void requestNesList(String appName, String pageNum, String pageSize, BaseObserver<NewListsBean> observer) {
        Map<String, String> map = new HashMap<>(2);
        map.put(UserConstants.Parameter.APPNAME, appName);
        map.put(UserConstants.Parameter.PAGENUM, pageNum);
        map.put(UserConstants.Parameter.PAGESIZE, pageSize);

        observe(mHomeService.requestNewsList(map)).subscribe(observer);
    }
}
