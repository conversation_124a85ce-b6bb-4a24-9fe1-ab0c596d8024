
package om.rrtx.mobile.functioncommon.plugin;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/**
 * <AUTHOR>
 * 消息的方法通信插件
 */
public class NativeAppMethodChannelPlugin implements MethodChannel.MethodCallHandler {
    //
    private static final String CHANNEL = "nativeApp";

    private Context mContext;
    private FlutterEngine mFlutterEngine;
    private static NativeAppMethodChannelPlugin nativeAppPlugin;


    public NativeAppMethodChannelPlugin(FlutterEngine flutterEngine, Context context) {
        mFlutterEngine = flutterEngine;
        mContext = context;
    }

    /**
     * 注册相应的组件 并把相应的插件暴露出去
     */
    public static NativeAppMethodChannelPlugin registerEventChannel(FlutterEngine flutterEngine, Context context) {
        MethodChannel methodChannel = new MethodChannel(flutterEngine.getDartExecutor(), CHANNEL);
        nativeAppPlugin = new NativeAppMethodChannelPlugin(flutterEngine, context);
        methodChannel.setMethodCallHandler(nativeAppPlugin);
        return nativeAppPlugin;
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        switch (call.method) {
            case "backToNative":
                ((Activity) mContext).finish();
                break;
        }
    }

    /**
     * 调用flutter端方法，无返回值
     */
    public void invokeMethod(String method, Object o) {
        nativeAppPlugin.invokeMethod(method, o);
    }

    /**
     * 调用flutter端方法，有返回值
     */
    public void invokeMethod(String method, Object o, MethodChannel.Result result) {
        nativeAppPlugin.invokeMethod(method, o, result);
    }
}
