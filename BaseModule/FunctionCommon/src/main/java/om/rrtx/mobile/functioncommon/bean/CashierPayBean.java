package om.rrtx.mobile.functioncommon.bean;

/**
 * <AUTHOR>
 * 收银台支付的实体类
 * 这里应该是构建者模式的方按
 */
public class CashierPayBean {

    /**
     * 订单金额
     */
    private String amt;
    /**
     * 支付种类
     */
    private String paymentProduct;
    /**
     * 订单号
     */
    private String merNo;

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getPaymentProduct() {
        return paymentProduct;
    }

    public void setPaymentProduct(String paymentProduct) {
        this.paymentProduct = paymentProduct;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }
}
