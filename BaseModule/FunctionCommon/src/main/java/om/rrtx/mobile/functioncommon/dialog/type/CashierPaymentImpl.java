package om.rrtx.mobile.functioncommon.dialog.type;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.dialog.CashierBankListFragment;
import om.rrtx.mobile.functioncommon.dialog.CashierPayImpl;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收银台转账的实现
 */
public class CashierPaymentImpl implements CashierPayImpl {
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean mCashierOrderInfoBean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public CashierPaymentImpl(AppCompatActivity activity, CashierOrderInfoBean cashierOrderInfoBean, PayMethodLiveData payMethodLiveData) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        mCashierOrderInfoBean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
    }

    @Override
    public void showInfo(View rootView) {
        TextView amtTv = rootView.findViewById(R.id.amtTv);
        TextView tab1 = rootView.findViewById(R.id.tab1);
        TextView tab1Tv = rootView.findViewById(R.id.tab1Tv);
        TextView tab2 = rootView.findViewById(R.id.tab2);
        TextView tab2Tv = rootView.findViewById(R.id.tab2Tv);
        TextView tab3 = rootView.findViewById(R.id.tab3);
        TextView tab3Tv = rootView.findViewById(R.id.tab3Tv);
        TextView paymentMethod = rootView.findViewById(R.id.paymentMethod);
        TextView paymentMethodTv = rootView.findViewById(R.id.paymentMethodTv);
        ImageView paymentMethodIv = rootView.findViewById(R.id.paymentMethodIv);
        View paymentMethodView = rootView.findViewById(R.id.paymentMethodView);

        //设置订单金额
        amtTv.setText(CurrencyUtils.setCurrency(mActivity, mCashierOrderInfoBean.getCurrency())+" "+StringUtils.formatAmount(mCashierOrderInfoBean.getOrderAmt()));

        //设置商户名称
        String merName = mCashierOrderInfoBean.getMerName();
        if (!TextUtils.isEmpty(merName)) {
            tab1.setVisibility(View.VISIBLE);
            tab1.setText(R.string.checkout_label_mer_name);
            tab1Tv.setVisibility(View.VISIBLE);
            tab1Tv.setText(mCashierOrderInfoBean.getMerName());
        } else {
            tab1.setVisibility(View.GONE);
            tab1Tv.setVisibility(View.GONE);
        }

        //设置订单号
        if (TextUtils.equals(mCashierOrderInfoBean.getPayType(), CommonConstants.CashierPaymentType.Cashier_Mer)) {
            tab2.setVisibility(View.GONE);
            tab2Tv.setVisibility(View.GONE);
        } else {
            tab2.setVisibility(View.VISIBLE);
            tab2Tv.setVisibility(View.VISIBLE);
            tab2.setText(R.string.checkout_label_order_no);
            tab2Tv.setText(mCashierOrderInfoBean.getTransferToken());
        }
        //设置订单信息
        String orderInfo = mCashierOrderInfoBean.getOrderInfo();
        if (!TextUtils.isEmpty(orderInfo)) {
            tab3.setVisibility(View.VISIBLE);
            tab3Tv.setVisibility(View.VISIBLE);
            tab3.setText(R.string.order_info);
            tab3Tv.setText(orderInfo);
        } else {
            tab3.setVisibility(View.GONE);
            tab3Tv.setVisibility(View.GONE);
        }

        //设置支付方式
        paymentMethod.setVisibility(View.VISIBLE);
        paymentMethodTv.setVisibility(View.VISIBLE);
        paymentMethodIv.setVisibility(View.VISIBLE);
        paymentMethodView.setVisibility(View.VISIBLE);

        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            if (mIsRequestFinish) {
                return;
            }
            mIsRequestFinish = true;
            String pubLick = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, "");
            String userId = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.USERID, "");
            if (!TextUtils.isEmpty(pubLick)) {
                mCommonModel.requestPayment(userId, mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getTransferToken(),
                        psd, CommonConstants.PaymentPasswordType.PSD, mCashierOrderInfoBean.getQrCode(), CommonConstants.OrderSource.INNERORDER,
                        mCashierOrderInfoBean.getOutOrderNo(), mCashierOrderInfoBean.getPaymentToken(), mCashierOrderInfoBean.getPaymentType(), mCashierOrderInfoBean.getCardNo(),
                        mCashierOrderInfoBean.getCurrency(),new BaseObserver<PaymentSuccessBean>(mActivity) {
                            @Override
                            public void requestSuccess(PaymentSuccessBean sResData) {
                                mCashierOrderInfoBean.setTransFeeAmt(sResData.getPayFeeAmount());
                                CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                                //保存一下支付方式
                                PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
                                SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.LOCALPAYMENTTYPE, new Gson().toJson(selectPaymentMethod));
                                mIsRequestFinish = false;
                            }

                            @Override
                            public void requestFail(String sResMsg) {
                                if (mPayPsdBottomFragment != null) {
                                    mPayPsdBottomFragment.cancelPassword();
                                }
                                ToastUtil.show(mActivity, sResMsg);
                                mIsRequestFinish = false;
                            }
                        });
            } else {
                mCommonModel.commonPub(new BaseObserver<PubBean>(mActivity) {
                    @Override
                    public void requestSuccess(PubBean sResData) {
                        SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                        mCommonModel.requestPayment(userId, mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getTransferToken(),
                                psd, CommonConstants.PaymentPasswordType.PSD, mCashierOrderInfoBean.getQrCode(), CommonConstants.OrderSource.INNERORDER,
                                mCashierOrderInfoBean.getOutOrderNo(), mCashierOrderInfoBean.getPaymentToken(), mCashierOrderInfoBean.getPaymentType(), mCashierOrderInfoBean.getCardNo(),
                                mCashierOrderInfoBean.getCurrency(),new BaseObserver<PaymentSuccessBean>(mActivity) {
                                    @Override
                                    public void requestSuccess(PaymentSuccessBean sResData) {
                                        mCashierOrderInfoBean.setTransFeeAmt(sResData.getPayFeeAmount());
                                        CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                                        //保存一下支付方式
                                        PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
                                        SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.LOCALPAYMENTTYPE, new Gson().toJson(selectPaymentMethod));
                                        mIsRequestFinish = false;
                                    }

                                    @Override
                                    public void requestFail(String sResMsg) {
                                        if (mPayPsdBottomFragment != null) {
                                            mPayPsdBottomFragment.cancelPassword();
                                        }
                                        ToastUtil.show(mActivity, sResMsg);
                                        mIsRequestFinish = false;
                                    }
                                });
                    }

                    @Override
                    public void requestFail(String sResMsg) {

                    }
                });
            }

            if (mPayPsdBottomFragment != null) {
                mPayPsdBottomFragment.cancelPassword();
            }
        });
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        CashierBankListFragment cashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(cashierOrderInfoBean));
        cashierBankListFragment.show(baseBottomFragment.getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
    }

    @Override
    public void getPayMethod() {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        String pubLick = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.requestPaymentType(userId, mCashierOrderInfoBean.getOrderNo(), mCashierOrderInfoBean.getOrderAmt(),
                    mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getMerNo(),
                    mCashierOrderInfoBean.getTransType(), mCashierOrderInfoBean.getOrderSource(),mCashierOrderInfoBean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                        @Override
                        public void requestSuccess(PaymentBean paymentBean) {
                            mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                        }
                    });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mActivity) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.requestPaymentType(userId, mCashierOrderInfoBean.getOrderNo(), mCashierOrderInfoBean.getOrderAmt(),
                            mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getMerNo(),
                            mCashierOrderInfoBean.getTransType(), mCashierOrderInfoBean.getOrderSource(),mCashierOrderInfoBean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                                @Override
                                public void requestSuccess(PaymentBean paymentBean) {
                                    mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }

    @Override
    public void nextClick(String isFreePwd,BaseBottomFragment baseBottomFragment) {
        mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(mCashierOrderInfoBean));
        mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
    }
    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
