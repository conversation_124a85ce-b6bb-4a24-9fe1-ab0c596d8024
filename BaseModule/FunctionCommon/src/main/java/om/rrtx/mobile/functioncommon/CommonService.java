package om.rrtx.mobile.functioncommon;

import om.rrtx.mobile.functioncommon.bean.AirtimeSuccessBean;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.functioncommon.bean.BalanceCertificateBean;
import om.rrtx.mobile.functioncommon.bean.CashOutCheckBean;
import om.rrtx.mobile.functioncommon.bean.CertificateBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.functioncommon.bean.BankNumBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyBean;
import om.rrtx.mobile.functioncommon.bean.DictoryBean;
import om.rrtx.mobile.functioncommon.bean.NewListsBean;
import om.rrtx.mobile.functioncommon.bean.OptionsBankListBean;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;

import java.util.Map;

import io.reactivex.Observable;
import om.rrtx.mobile.functioncommon.bean.TopUpCheckBean;
import om.rrtx.mobile.functioncommon.bean.TopUpSuccessBean;
import om.rrtx.mobile.functioncommon.bean.WithdrawalOrderBean;
import om.rrtx.mobile.functioncommon.bean.ZesaMerBean;
import om.rrtx.mobile.functioncommon.bean.ZesaResultBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.rrtxcommon1.bean.MarketingBean;
import om.rrtx.mobile.rrtxcommon1.bean.RequestUseMarketingBean;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * 首页的Api接口层
 */
public interface CommonService {


    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);

    /**
     * 查询用户金额
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.PAYMENTTYPE)
    Observable<Response<BaseBean<PaymentBean>>> requestPaymentType(@FieldMap Map<String, String> formData);

    /**
     * 查询订单信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.GETORDERINFO)
    Observable<Response<BaseBean<OrderInfoBean>>> requestOrderInfo(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.MERCHANT_CREATERORDER)
    Observable<Response<BaseBean<OrderInfoBean>>> requestMerchantOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.MERCODE_PAY)
    Observable<Response<BaseBean<OrderInfoBean>>> requestMerCodePayOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.ZESA_CREATERORDER)
    Observable<Response<BaseBean<OrderInfoBean>>> requestZesaOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.BUY_CREATERORDER)
    Observable<Response<BaseBean<OrderInfoBean>>> requestBuyOrder(@FieldMap Map<String, String> formData);

    /**
     * 支付订单
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.PAYORDER)
    Observable<Response<BaseBean<PaymentSuccessBean>>> requestPayment(@FieldMap Map<String, String> formData);

    /**
     * binding银行卡接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.BINDBANK)
    Observable<Response<BaseBean<Object>>> requestBindBank(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.PAY_BILL_CODE)
    Observable<Response<BaseBean<PaymentSuccessBean>>> requestBillCodePayOrder(@FieldMap Map<String, String> formData);

    /**
     * 个人用户转账
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.TRANSFERTOPERACC)
    Observable<Response<BaseBean<Object>>> requestUserTransfer(@FieldMap Map<String, String> formData);

    /**
     * 请求支付aa订单
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.AAPAYORDER)
    Observable<Response<BaseBean<Object>>> requestPayOrder(@FieldMap Map<String, String> formData);

    /**
     * 创建提现订单
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.WITHDRAWALCREATEORDER)
    Observable<Response<BaseBean<WithdrawalOrderBean>>> requestWithdrawalOrder(@FieldMap Map<String, String> formData);

    /**
     * 钱包用户Zipit提现
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.ZIPITCREATEORDER)
    Observable<Response<BaseBean<WithdrawalOrderBean>>> requestZIPITOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.CASHCREATEORDER)
    Observable<Response<BaseBean<WithdrawalOrderBean>>> requestCashOutOrder(@FieldMap Map<String, String> formData);

    /**
     * 创建提现订单
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.TOPUPACC)
    Observable<Response<BaseBean<TopUpSuccessBean>>> requestTopUpAcc(@FieldMap Map<String, String> formData);

    /**
     * 充值校验
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.TOPUPCHECK)
    Observable<Response<BaseBean<TopUpCheckBean>>> requestTopUpCheck(@FieldMap Map<String, String> formData);

    /**
     * 激活的货币
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.AVAILABLECURRENCY)
    Observable<Response<BaseBean<CurrencyBean>>> getAvailableCurrency(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.COMMON_SYSDICTCODE)
    Observable<Response<BaseBean<DictoryBean.AppDictBean>>> queryDefaultCurrency(@FieldMap Map<String, String> formData);

    /**
     * 银行list
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.EGT_BANK_LIST)
    Observable<Response<BaseBean<OptionsBankListBean>>> getOptionsBankList(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.REMITTANCE_CODE_CHECK)
    Observable<Response<BaseBean<CashOutCheckBean>>> remittanceCodeCheck(@FieldMap Map<String, String> formData);

    /**
     * 绑定的银行卡数
     */
    @POST(CommonConstants.URL.BANK_NUM_QUERY)
    @FormUrlEncoded
    Observable<Response<BaseBean<BankNumBean>>> getBankNum(@FieldMap Map<String, String> formData);


    /**
     * 查询所有币种账户
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.BALANCE)
    Observable<Response<BaseBean<CurrencyAccountListBean>>> requestAccountCurrency(@FieldMap Map<String, String> formData);

    /**
     * 查询交易支付接口
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.QUERY_TRANSFER_PAY)
    Observable<Response<BaseBean<CertificateBean>>> requestTransferPay(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.JUNIOR_TRANSFER_PAY)
    Observable<Response<BaseBean<CertificateBean>>> requestJuniorTransferPay(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.QUERY_BALANCE)
    Observable<Response<BaseBean<BalanceCertificateBean>>> requestBalancePay(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.JUNIOR_ENQUIRY)
    Observable<Response<BaseBean<BalanceCertificateBean>>> requestJuniorBalancePay(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.PAY_ZESA)
    Observable<Response<BaseBean<ZesaResultBean>>> payZesa(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.PAY_ZESA_ORDER)
    Observable<Response<BaseBean<PaymentSuccessBean>>> payZesaOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.PAY_BUY_ORDER)
    Observable<Response<BaseBean<PaymentSuccessBean>>> payBuyOrder(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.RE_ZESA_TOKEN)
    Observable<Response<BaseBean<ZesaMerBean>>> reZesaToken(@FieldMap Map<String, String> formData);


    @FormUrlEncoded
    @POST(CommonConstants.URL.BUY_AIRTIME_BUNDLE)
    Observable<Response<BaseBean<AirtimeSuccessBean>>> requestsBuyAirtimeOrBundle(@FieldMap Map<String, Object> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.Query_Marketing)
    Observable<Response<BaseBean<MarketingBean>>> queryMarketingInfo(@FieldMap Map<String, String> formData);

    @POST(CommonConstants.URL.Use_Marketing)
    Observable<Response<BaseBean<MarketingBean>>> requestUseMarketing(@Body RequestUseMarketingBean bean);


    @FormUrlEncoded
    @POST(CommonConstants.URL.CREATEORDER)
    Observable<Response<BaseBean<OrderInfoBean>>> requestCreateQrCodeOrder(@FieldMap Map<String, String> formData);


    /**
     * 获取用户信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GETUSERINFO)
    Observable<Response<BaseBean<UserInfoBean>>> requestUserInfo(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(UserConstants.URL.GETUSERINFOBY)
    Observable<Response<BaseBean<UserInfoBean>>> getUserInfoByIdCardAndMobile(@FieldMap Map<String, String> formData);

    /**
     * 获取亲子用户信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GETUSERINFO_BYID)
    Observable<Response<BaseBean<UserInfoBean>>> requestUserInfoById(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(UserConstants.URL.CHECK_JUNIOR_ACCOUNT)
    Observable<Response<BaseBean<UserInfoBean>>> requestCheckJuniorAccount(@FieldMap Map<String, String> formData);


    /**
     * 获取短信验证码
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GET_VER_CODE)
    Observable<Response<BaseBean<CodeBean>>> requestCode(@FieldMap Map<String, String> formData);


    @FormUrlEncoded
    @POST(CommonConstants.URL.VALIDATESMS)
    Observable<Response<BaseBean<Object>>> requestValidateSms(@FieldMap Map<String, String> formData);

    /**
     * 切换主设备
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(CommonConstants.URL.CHANGEMASTERDEVICE)
    Observable<Response<BaseBean<Object>>> requestChangeMasterDevice(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(CommonConstants.URL.JUNIOR_VALIDATESMS)
    Observable<Response<BaseBean<Object>>> requestJuniorValidateSms(@FieldMap Map<String, String> formData);


    /**
     * 消息列表
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.NEWLISTS)
    Observable<Response<BaseBean<NewListsBean>>> requestNewsList(@FieldMap Map<String, String> formData);


    /**
     * 查询币种账户余额
     */
    @POST(CommonConstants.URL.GET_ACCOUNT_BALANCE)
    @FormUrlEncoded
    Observable<Response<BaseBean<BalanceBean>>> checkBalanceOrder(@FieldMap Map<String, String> formData);

}
