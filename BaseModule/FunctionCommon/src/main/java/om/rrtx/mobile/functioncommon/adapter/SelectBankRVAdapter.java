package om.rrtx.mobile.functioncommon.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import om.rrtx.mobile.functioncommon.bean.SelectBankBean;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;

/**
 * <AUTHOR>
 */
public class SelectBankRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<SelectBankBean> mList;
    private int mCurrentIndex = -1;
    private RVAdapterItemClickListener<SelectBankBean> mClickListener;

    public SelectBankRVAdapter(Context context) {
        mContext = context;
        initData();
    }

    private void initData() {
        mList = new ArrayList<>();
        mList.add(new SelectBankBean(mContext.getString(R.string.withdrawal_label_citibank_name), "1"));
        mList.add(new SelectBankBean(mContext.getString(R.string.withdrawal_label_bank_of_america_name), "2"));
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.common_item_select_bank, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        SelectBankBean itemName = mList.get(position);

        TextView bankName = holder.getView(R.id.bankName);
        ImageView selectIv = holder.getView(R.id.selectIv);

        bankName.setText(itemName.getBankName());

        if (position == mCurrentIndex) {
            selectIv.setVisibility(View.VISIBLE);
        } else {
            selectIv.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.itemClickListener(itemName, position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }


    public void setClickListener(RVAdapterItemClickListener<SelectBankBean> clickListener) {
        mClickListener = clickListener;
    }

    public void setSelectItem(SelectBankBean selectBankBean) {
        if (mList.size() == 0) {
            return;
        }

        int mIndex = -1;
        for (int i = 0; i < mList.size(); i++) {
            SelectBankBean cardRecordsBean = mList.get(i);
            if (TextUtils.equals(cardRecordsBean.getBankName(), selectBankBean.getBankName())) {
                mIndex = i;
                break;
            }
        }

        if (mIndex != -1) {
            mCurrentIndex = mIndex;
            notifyDataSetChanged();
        }
    }
}
