package om.rrtx.mobile.functioncommon.utils;

import androidx.appcompat.app.AppCompatActivity;

import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.dialog.CashierDetailsBottomFragment;

/**
 * <AUTHOR>
 * 收银台管理类
 * 主要是管理收银台的内容,这里主要是为了封装
 * 1. 创建一个DialogFragment
 * 2. 传入相应的内容
 */
public class CashierManager {

    /**
     * 需要的传入信息
     */
    private String mInfoJson;
    /**
     * 回调信息接口
     */
    private CashierCallBack mCashierCallBack;

    private AppCompatActivity mActivity;
    private CashierDetailsBottomFragment mCashierDetailsFragment;

    public CashierManager(AppCompatActivity activity, String infoJson, CashierCallBack cashierCallBack) {
        mActivity = activity;
        mInfoJson = infoJson;
        this.mCashierCallBack = cashierCallBack;
    }

    /**
     * 展示收银台信息
     */
    public void showCashierDialog() {
        //这里创建收银台的话,因为涉及到多比订单一起支付,所以这里就直接创建相应的对象吧 !
        mCashierDetailsFragment = CashierDetailsBottomFragment.newInstance(mInfoJson);
        mCashierDetailsFragment.setCashierCallBack(mCashierCallBack);
        mCashierDetailsFragment.show(mActivity.getSupportFragmentManager(), CashierDetailsBottomFragment.class.getSimpleName());
    }

    public void dismiss() {
        if (mCashierDetailsFragment != null) {
            mCashierDetailsFragment.dismiss();
        }
    }
}
