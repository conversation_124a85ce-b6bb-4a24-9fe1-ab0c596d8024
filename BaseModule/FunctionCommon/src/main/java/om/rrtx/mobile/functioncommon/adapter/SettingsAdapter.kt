package om.rrtx.mobile.functioncommon.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.functioncommon.databinding.ItemSettingsBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

class SettingsAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var callback: BaseAdapterCallback

    lateinit var context: Context

    private var data = arrayListOf<InfoItemBean>()
    fun setNewData(list: ArrayList<InfoItemBean>) {
        data = list
        notifyDataSetChanged()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemSettingsBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemSettingsBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.titleTv.text = bean.title

        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                if (bean.callBack != null) bean.callBack!!()
                if (bean.callBack1 != null) bean.callBack1!!(position)
                if (bean.callBack2 != null) bean.callBack2!!(position, bean)
            }
        })

    }

}