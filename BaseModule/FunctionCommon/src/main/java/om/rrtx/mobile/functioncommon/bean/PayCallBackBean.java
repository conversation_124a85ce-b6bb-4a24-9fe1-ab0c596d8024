package om.rrtx.mobile.functioncommon.bean;

/**
 * <AUTHOR>
 * 支付回调实体类
 */
public class PayCallBackBean {
    private String fullName;
    private String bankName;
    private String cardNo;

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public PayCallBackBean(Builder builder) {
        fullName = builder.fullName;
        bankName = builder.bankName;
        cardNo = builder.cardNo;
    }

    public static class Builder {
        private String fullName;
        private String bankName;
        private String cardNo;

        public Builder setFullName(String fullName) {
            this.fullName = fullName;
            return this;
        }

        public Builder setBankName(String bankName) {
            this.bankName = bankName;
            return this;
        }

        public Builder setCardNo(String cardNo) {
            this.cardNo = cardNo;
            return this;
        }

        public PayCallBackBean builder() {
            return new PayCallBackBean(this);
        }
    }
}
