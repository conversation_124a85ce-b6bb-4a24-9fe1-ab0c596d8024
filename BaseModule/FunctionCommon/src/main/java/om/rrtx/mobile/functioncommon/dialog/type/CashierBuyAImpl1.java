package om.rrtx.mobile.functioncommon.dialog.type;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import java.util.ArrayList;

import om.rrtx.mobile.functioncommon.CashierCallBackManager1;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.OrderInfoItemBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean;
import om.rrtx.mobile.functioncommon.dialog.CashierBankListFragment;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 */
public class CashierBuyAImpl1 implements CashierPayImpl1 {
    private final ArrayList<OrderInfoItemBean> data;
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean bean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public CashierBuyAImpl1(AppCompatActivity activity,
                            CashierOrderInfoBean cashierOrderInfoBean,
                            PayMethodLiveData payMethodLiveData,
                            ArrayList<OrderInfoItemBean> data) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        bean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
        this.data = data;
        initVMListener();
        getPayMethod();
    }

    public void initData() {
        data.add(new OrderInfoItemBean(getString(R.string.merchant_name), bean.getMerName()));
        data.add(new OrderInfoItemBean(getString(R.string.order_info), bean.getOrderInfo(),null,bean.getSetmeal()));
        //data.add(new OrderInfoItemBean(getString(R.string.order_No_), bean.getOrderNo()));
//        data.add(new OrderInfoItemBean(getString(R.string.order_Amount), bean.getOrderAmt()));
//        data.add(new OrderInfoItemBean(getString(R.string.cashback_Account), bean.getCashbackAmt()));
    }

    private void initVMListener() {
        //输入密码完成
        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            pay(psd, "");
        });
    }

    public String getString(int id) {
        return mActivity.getString(id);
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        CashierBankListFragment cashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(cashierOrderInfoBean));
        cashierBankListFragment.show(baseBottomFragment.getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
    }

    @Override
    public void getPayMethod() {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        mCommonModel.requestPaymentType("", bean.getOrderNo(),
                bean.getOrderAmt(),
                "",
                bean.getMerNo(),
                bean.getTransType(),
                bean.getOrderSource(),
                bean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                    @Override
                    public void requestSuccess(PaymentBean paymentBean) {
                        mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                    }

                    @Override
                    public void requestFail(String sResMsg) {
                        mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                    }
                });

    }

    @Override
    public void nextClick(String isFreePwd, BaseBottomFragment baseBottomFragment) {
        if (isFreePwd.equals("1")) {
            pay("", isFreePwd);
        } else {
            //获取选择的cardId
            PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
            bean.setCardNo(selectPaymentMethod.getCardId());
            bean.setPaymentType(selectPaymentMethod.getPaymentType());

            mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(bean));
            mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
        }
    }

    public void pay(String psd, String isFreePwd) {
        if (mIsRequestFinish || psd == null) {
            return;
        }
        mIsRequestFinish = true;

        mCommonModel.payBuyOrder(bean.getPlanCode(),bean.getOrderNo(),
                bean.getPaymentType(),
                bean.getCardNo(),
                psd,
                isFreePwd,
                new BaseObserver<PaymentSuccessBean>(mActivity) {
                    @Override
                    public void requestSuccess(PaymentSuccessBean sResData) {
                        bean.setTransFeeAmt(sResData.getPayFeeAmount());
                        CashierCallBackManager1.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                        //保存一下支付方式
                        PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
                        SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.LOCALPAYMENTTYPE, new Gson().toJson(selectPaymentMethod));
                        mIsRequestFinish = false;
                    }

                    @Override
                    public void requestFail(String sResMsg) {
                        if (mPayPsdBottomFragment != null) {
                            mPayPsdBottomFragment.cancelPassword();
                        }
                        ToastUtil.show(mActivity, sResMsg);
                        mIsRequestFinish = false;
                    }
                });
    }

    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
