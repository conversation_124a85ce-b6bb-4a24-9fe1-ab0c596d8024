package om.rrtx.mobile.functioncommon.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gyf.immersionbar.ImmersionBar;

import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.bean.CurrencyBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR> zfw
 * @time   : 2023/9/13 14:20
 * @desc   : 选择货币账户
 */
public class SelectCurrencyAccountActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.contentRv)
    RecyclerView mContentRv;

    private List<String> mList;
    private CommonModel mCommonModel;
    private String mSelectedCurrency;
    CurrencyAccountAdapter mAdapter;

    @Override
    protected int createContentView() {
        return R.layout.common_activity_select_currency_account;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    public void doGetExtra() {
        mSelectedCurrency = getIntent().getStringExtra(CommonConstants.Parameter.CURRENCY);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.common_ic_back_black);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.common_label_select_act);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

    }

    @Override
    public void initDate() {
        mCommonModel = new CommonModel();
        getAvailableCurrency();
    }

    @Override
    public void initListener() {
        mContentRv.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new CurrencyAccountAdapter(mContext);
        mContentRv.setAdapter(mAdapter);
        mLeftBg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    public void getAvailableCurrency() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.getAvailableCurrency(userId, new BaseNoDialogObserver<CurrencyBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    if (mContext != null) {
                        ToastUtil.showCenter(mContext,sResMsg);
                    }
                }

                @Override
                public void requestSuccess(CurrencyBean bean) {
                    if (mContext != null) {
                        if (bean != null && bean.getCurrencyList() != null && bean.getCurrencyList().length > 0) {
                            mAdapter.setDataList(Arrays.asList(bean.getCurrencyList()));
                        }
                    }
                }
            });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.getAvailableCurrency(userId, new BaseNoDialogObserver<CurrencyBean>(mContext) {
                        @Override
                        public void requestFail(String sResMsg) {
                            if (mContext != null) {
                                ToastUtil.showCenter(mContext,sResMsg);
                            }
                        }

                        @Override
                        public void requestSuccess(CurrencyBean bean) {
                            if (mContext != null) {
                                if (bean != null && bean.getCurrencyList() != null && bean.getCurrencyList().length > 0) {
                                    mAdapter.setDataList(Arrays.asList(bean.getCurrencyList()));
                                }
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }

    public class CurrencyAccountAdapter extends RecyclerView.Adapter<BaseHolder> {
        private Context mContext;

        public CurrencyAccountAdapter(Context context) {
            mContext = context;
        }


        @NonNull
        @Override
        public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View rootView = LayoutInflater.from(mContext).inflate(R.layout.common_item_currency_account, parent, false);
            return new BaseHolder(rootView);
        }

        @Override
        public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
            String curStr = mList.get(position);

            TextView curTv = holder.getView(R.id.accountTv);
            ImageView selectIv = holder.getView(R.id.selectIv);
            curTv.setText(curStr+" "+getString(R.string.common_label_account));

            if (curStr.equals(mSelectedCurrency)) {
                selectIv.setVisibility(View.VISIBLE);
            } else {
                selectIv.setVisibility(View.GONE);
            }

            holder.itemView.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    mSelectedCurrency = curStr;
                    notifyDataSetChanged();
                    LogUtil.e("done", "itemClickListener  curStr: " + curStr);
                    Intent intent = new Intent();
                    intent.putExtra(CommonConstants.Transmit.CURRENCY, curStr);
                    setResult(RESULT_OK, intent);
                    finish();
                }
            });
        }


        @Override
        public int getItemCount() {
            return mList != null ? mList.size() : 0;
        }

        public void setDataList(List<String> strList) {
            mList = strList;
            notifyDataSetChanged();
        }
    }
}