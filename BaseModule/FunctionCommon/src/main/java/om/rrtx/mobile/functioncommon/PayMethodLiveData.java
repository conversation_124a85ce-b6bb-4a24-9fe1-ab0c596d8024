package om.rrtx.mobile.functioncommon;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import om.rrtx.mobile.functioncommon.bean.PaymentBean;

/**
 * <AUTHOR>
 */
public class PayMethodLiveData extends ViewModel {

    /**
     * 整体数据的LiveData
     */
    private MutableLiveData<PaymentBean> paymentTypeLV = new MutableLiveData<>();
    /**
     * 错误信息接口
     */
    private MutableLiveData<String> mErrorLv = new MutableLiveData<>();
    /**
     * 获取支付方式失败
     */
    private MutableLiveData<String> mPaymentTypeError = new MutableLiveData<>();
    /**
     * 密码输入完成回调
     */
    private MutableLiveData<String> mPsdFinish = new MutableLiveData<>();
    public MutableLiveData<Boolean> mDismiss = new MutableLiveData<>();

    public MutableLiveData<PaymentBean> getPaymentTypeLV() {
        if (paymentTypeLV == null) {
            paymentTypeLV = new MutableLiveData<>();
        }
        return paymentTypeLV;
    }

    public MutableLiveData<String> getErrorLv() {
        if (mErrorLv == null) {
            mErrorLv = new MutableLiveData<>();
        }
        return mErrorLv;
    }

    public MutableLiveData<String> getPaymentTypeError() {
        if (mPaymentTypeError == null) {
            mPaymentTypeError = new MutableLiveData<>();
        }
        return mPaymentTypeError;
    }

    public MutableLiveData<String> getPsdFinish() {
        if (mPsdFinish == null) {
            mPsdFinish = new MutableLiveData<>();
        }
        return mPsdFinish;
    }
}
