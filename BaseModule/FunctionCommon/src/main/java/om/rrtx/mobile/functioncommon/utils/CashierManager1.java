package om.rrtx.mobile.functioncommon.utils;

import androidx.appcompat.app.AppCompatActivity;

import om.rrtx.mobile.functioncommon.callback.CashierCallBack1;
import om.rrtx.mobile.functioncommon.dialog.NewCashierDetailsBottomFragment;

/**
 * <AUTHOR>
 * 收银台管理类
 * 主要是管理收银台的内容,这里主要是为了封装
 * 1. 创建一个DialogFragment
 * 2. 传入相应的内容
 */
public class CashierManager1 {

    /**
     * 需要的传入信息
     */
    private String mInfoJson;
    /**
     * 回调信息接口
     */
    private CashierCallBack1 mCashierCallBack;

    private AppCompatActivity mActivity;
    private NewCashierDetailsBottomFragment mCashierDetailsFragment;

    public CashierManager1(AppCompatActivity activity, String infoJson, CashierCallBack1 cashierCallBack) {
        mActivity = activity;
        mInfoJson = infoJson;
        this.mCashierCallBack = cashierCallBack;
    }

    /**
     * 展示收银台信息
     */
    public void showCashierDialog() {
        //这里创建收银台的话,因为涉及到多比订单一起支付,所以这里就直接创建相应的对象吧 !
        mCashierDetailsFragment = NewCashierDetailsBottomFragment.newInstance(mInfoJson);
        mCashierDetailsFragment.setCashierCallBack(mCashierCallBack);
        mCashierDetailsFragment.show(mActivity.getSupportFragmentManager(), NewCashierDetailsBottomFragment.class.getSimpleName());
    }

    public void dismiss() {
        if (mCashierDetailsFragment != null) {
            mCashierDetailsFragment.dismiss();
        }
    }
}
