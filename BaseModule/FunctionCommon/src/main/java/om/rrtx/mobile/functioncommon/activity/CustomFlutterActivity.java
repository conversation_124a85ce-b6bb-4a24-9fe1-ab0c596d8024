package om.rrtx.mobile.functioncommon.activity;


import android.app.Activity;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;

import cn.bingoogolapple.swipebacklayout.BGASwipeBackHelper;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugins.GeneratedPluginRegistrant;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.plugin.NativeAppMethodChannelPlugin;
import om.rrtx.mobile.functioncommon.plugin.NewsMethodChannelPlugin;

/**
 * <AUTHOR>
 */
public class CustomFlutterActivity extends FlutterActivity implements BGASwipeBackHelper.Delegate {
//FlutterActivity implements BGASwipeBackHelper.Delegate
    private BGASwipeBackHelper mSwipeBackHelper;
    private NewsMethodChannelPlugin mNewsMethodChannelPlugin;

    public static NewMyEngineIntentBuilder withNewEngine(Class<? extends FlutterActivity> activityClass) {
        return new NewMyEngineIntentBuilder(activityClass);
    }

    /**
     * 重写创建引擎方法
     */
    public static class NewMyEngineIntentBuilder extends NewEngineIntentBuilder {
        private NewMyEngineIntentBuilder(Class<? extends FlutterActivity> activityClass) {
            super(activityClass);
        }
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine);

        //注册新闻插件
        mNewsMethodChannelPlugin = NewsMethodChannelPlugin.registerEventChannel(flutterEngine, this);
        //注册工单插件
        NativeAppMethodChannelPlugin.registerEventChannel(flutterEngine, this);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initSwipeBackFinish();
    }

    public void initSwipeBackFinish() {
        mSwipeBackHelper = new BGASwipeBackHelper(this, this);

        // 「必须在 Application 的 onCreate 方法中执行 BGASwipeBackHelper.init 来初始化滑动返回」
        // 下面几项可以不配置，这里只是为了讲述接口用法。

        // 设置滑动返回是否可用。默认值为 true
        mSwipeBackHelper.setSwipeBackEnable(true);
        // 设置是否仅仅跟踪左侧边缘的滑动返回。默认值为 true
        mSwipeBackHelper.setIsOnlyTrackingLeftEdge(true);
        // 设置是否是微信滑动返回样式。默认值为 true
        mSwipeBackHelper.setIsWeChatStyle(true);
        // 设置阴影资源 id。默认值为 R.drawable.bga_sbl_shadow
        mSwipeBackHelper.setShadowResId(R.drawable.bga_sbl_shadow);
        // 设置是否显示滑动返回的阴影效果。默认值为 true
        mSwipeBackHelper.setIsNeedShowShadow(true);
        // 设置阴影区域的透明度是否根据滑动的距离渐变。默认值为 true
        mSwipeBackHelper.setIsShadowAlphaGradient(true);
        // 设置触发释放后自动滑动返回的阈值，默认值为 0.3f
        mSwipeBackHelper.setSwipeBackThreshold(0.3f);
        // 设置底部导航条是否悬浮在内容上，默认值为 false
        mSwipeBackHelper.setIsNavigationBarOverlap(false);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        mNewsMethodChannelPlugin.invokeMethod("refresh", "");
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    public void setSwipeBackEnable(boolean isEnable) {
        mSwipeBackHelper.setSwipeBackEnable(isEnable);
    }


    @Override
    public void onSwipeBackLayoutSlide(float slideOffset) {

    }

    @Override
    public void onSwipeBackLayoutCancel() {

    }

    @Override
    public void onSwipeBackLayoutExecuted() {
        mSwipeBackHelper.swipeBackward();
    }

    @Override
    public void onBackPressed() {
        // 正在滑动返回的时候取消返回按钮事件
        if (mSwipeBackHelper.isSliding()) {
            return;
        }
        mSwipeBackHelper.backward();
    }
}
