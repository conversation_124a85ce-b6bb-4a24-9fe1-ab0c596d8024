package om.rrtx.mobile.functioncommon.activity;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Paint;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.TextView;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import butterknife.ButterKnife;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils;

/**
 * <AUTHOR> zfw
 * @time : 2023/3/20 10:40
 * @desc :
 */
public class ConditionActivity extends AppCompatActivity {
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.contentTv)
    TextView mContentTv;
    static String TITLE = "title";
    static String CONTENT = "content";
    String mTitle, mContent;

    public static void jumpCondition(Context context, String title, String content) {
        Intent intent = new Intent(context, ConditionActivity.class);
        intent.putExtra(TITLE, title);
        intent.putExtra(CONTENT, content);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_condition);
        ButterKnife.bind(this);
        mTitle = getIntent().getStringExtra(TITLE);
        mContent = getIntent().getStringExtra(CONTENT);
        initView();
    }

    private void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.common_ic_back_black);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        Log.e("ConditionActivity>>>", "zfw initView>>>mTitle :" + mTitle);
        Log.e("ConditionActivity>>>", "zfw initView>>>mContent :" + mContent);
        mTitleTv.setText(mTitle);
        mLeftBg.setOnClickListener(v -> finish());
        mContentTv.setText(Html.fromHtml(GetHtmlByUrl(mContent)));
        mContentTv.getViewTreeObserver().addOnGlobalLayoutListener(new OnTvGlobalLayoutListener());
    }

    @Override
    public Resources getResources() {
        return AdaptScreenUtils.adaptWidth(super.getResources(), 750);
    }

    public String GetHtmlByUrl(String contents) {
        String htmlBody = "";
        if (!TextUtils.isEmpty(contents)) {
            htmlBody = contents.substring(contents.indexOf("<body>") + "<body>".length(), contents.indexOf("</body>"));
        }
        Log.e("TermsActivity>>>", "zfw GetHtmlByUrl>>> :" + htmlBody);
        return htmlBody;
    }

    private class OnTvGlobalLayoutListener implements ViewTreeObserver.OnGlobalLayoutListener {
        @Override
        public void onGlobalLayout() {
            mContentTv.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            final String newText = autoSplitText(mContentTv);
            if (!TextUtils.isEmpty(newText)) {
                mContentTv.setText(newText);
            }
        }
    }

    private String autoSplitText(final TextView tv) {
        final String rawText = tv.getText().toString(); //原始文本
        final Paint tvPaint = tv.getPaint(); //paint，包含字体等信息
        final float tvWidth = tv.getWidth() - tv.getPaddingLeft() - tv.getPaddingRight(); //控件可用宽度

        //将原始文本按行拆分
        String[] rawTextLines = rawText.replaceAll("\r", "").split("\n");
        StringBuilder sbNewText = new StringBuilder();
        for (String rawTextLine : rawTextLines) {
            if (tvPaint.measureText(rawTextLine) <= tvWidth) {
                //如果整行宽度在控件可用宽度之内，就不处理了
                sbNewText.append(rawTextLine);
            } else {
                //如果整行宽度超过控件可用宽度，则按字符测量，在超过可用宽度的前一个字符处手动换行
                float lineWidth = 0;
                for (int cnt = 0; cnt != rawTextLine.length(); ++cnt) {
                    char ch = rawTextLine.charAt(cnt);
                    lineWidth += tvPaint.measureText(String.valueOf(ch));
                    if (lineWidth <= tvWidth) {
                        sbNewText.append(ch);
                    } else {
                        sbNewText.append("\n");
                        lineWidth = 0;
                        --cnt;
                    }
                }
            }
            sbNewText.append("\n");
        }

        //把结尾多余的\n去掉
        if (!rawText.endsWith("\n")) {
            sbNewText.deleteCharAt(sbNewText.length() - 1);
        }

        return sbNewText.toString();
    }


}