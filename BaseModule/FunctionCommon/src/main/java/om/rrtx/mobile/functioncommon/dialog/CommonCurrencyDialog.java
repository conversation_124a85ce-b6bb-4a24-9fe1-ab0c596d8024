package om.rrtx.mobile.functioncommon.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.adapter.CurrencyListRvAdapter;
import om.rrtx.mobile.functioncommon.bean.CurrencyBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;


/**
 * <AUTHOR> zfw
 * @time : 2023/9/8 10:47
 * @desc :
 */
public class CommonCurrencyDialog extends Dialog implements RVAdapterItemClickListener<String> {

    RelativeLayout viewRoot;
    TextView mCancelTv;
    TextView mConfirmTv;
    RecyclerView mListRv;
    private String mType;
    private Context mContext;
    private List<String> mList = new ArrayList<>();
    private String selectValue = "";
    private CommonModel mCommonModel;
    private CurrencyListRvAdapter mAdapter;
    OnCurrencyClickListener listener;

    public CommonCurrencyDialog(@NonNull Context context) {
        super(context, R.style.transparentBottomDialog);
        mContext = context;
    }

    public CommonCurrencyDialog(@NonNull Context context, String type) {
        super(context, R.style.transparentBottomDialog);
        mContext = context;
        this.mType = type;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.common_dialog_currency_list);

        setCancelable(true);
        setCanceledOnTouchOutside(true);

        setLocation();

        initView();

        initListener();

        initData();
    }

    private void initData() {
        mCommonModel = new CommonModel();
        if (!TextUtils.isEmpty(mType) && "1".equals(mType)) {
            mList.clear();
            mList.add(mContext.getString(R.string.zwl));
            mList.add(mContext.getString(R.string.usd));
            mAdapter.setDataList(mList);
            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = viewRoot.getLayoutParams();
            layoutParams.height = ExtensionsKt.pt2px(378);
            viewRoot.setLayoutParams(layoutParams);
        } else {
            getAvailableCurrency();
            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = viewRoot.getLayoutParams();
            layoutParams.height = ExtensionsKt.pt2px(458);
            viewRoot.setLayoutParams(layoutParams);
        }
    }

    public void getAvailableCurrency() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.getAvailableCurrency(userId, new BaseNoDialogObserver<CurrencyBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    if (mContext != null) {
                        ToastUtil.showCenter(mContext, sResMsg);
                    }
                }

                @Override
                public void requestSuccess(CurrencyBean bean) {
                    if (mContext != null) {
                        if (bean != null && bean.getCurrencyList() != null && bean.getCurrencyList().length > 0) {
                            mList.clear();
                            mList = Arrays.asList(bean.getCurrencyList());
                            mAdapter.setDataList(mList);
                        }
                    }
                }
            });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.getAvailableCurrency(userId, new BaseNoDialogObserver<CurrencyBean>(mContext) {
                        @Override
                        public void requestFail(String sResMsg) {
                            if (mContext != null) {
                                ToastUtil.showCenter(mContext, sResMsg);
                            }
                        }

                        @Override
                        public void requestSuccess(CurrencyBean bean) {
                            if (mContext != null) {
                                if (bean != null && bean.getCurrencyList() != null && bean.getCurrencyList().length > 0) {
                                    mList.clear();
                                    mList = Arrays.asList(bean.getCurrencyList());
                                    mAdapter.setDataList(mList);
                                }
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.BOTTOM);
            window.setAttributes(WL);
        }
    }

    /**
     * 初始化控件
     */
    private void initView() {
        viewRoot = findViewById(R.id.view);
        mCancelTv = findViewById(R.id.tv_cancel);
        mConfirmTv = findViewById(R.id.tv_confirm);
        mListRv = findViewById(R.id.selectRv);

    }

    private void initListener() {
        mListRv.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new CurrencyListRvAdapter(mContext);
        mAdapter.setListener(this);
        mListRv.setAdapter(mAdapter);
        mCancelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mConfirmTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                dismiss();
                if (listener != null && mList!=null && mList.size()>0) {
                    if (TextUtils.isEmpty(selectValue)) {
                        selectValue = mList.get(0);
                    }
                    listener.onCurrencyClick(selectValue);
                }
            }
        });
    }


    @Override
    public void itemClickListener(String s, int position) {
        selectValue = s;
        /*dismiss();
        if (listener != null) {
            listener.onCurrencyClick(s);
        }*/
    }

    public void setOnCurrencyClickListener(OnCurrencyClickListener listener) {
        this.listener = listener;
    }

    public interface OnCurrencyClickListener {
        void onCurrencyClick(String currency);
    }
}
