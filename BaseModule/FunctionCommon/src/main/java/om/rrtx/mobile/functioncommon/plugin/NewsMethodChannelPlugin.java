package om.rrtx.mobile.functioncommon.plugin;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

import java.util.Map;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import om.rrtx.mobile.functioncommon.bean.CallBackBean;
import om.rrtx.mobile.functioncommon.bean.NewListsBean;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 消息的方法通信插件
 */
public class NewsMethodChannelPlugin implements MethodChannel.MethodCallHandler {
//    implements MethodChannel.MethodCallHandler
    private static final String CHANNEL = "com.cpkj.xwalletPro";

    private Context mContext;
    private FlutterEngine mFlutterEngine;
    private static NewsMethodChannelPlugin sNewsPlugin;
    private final PluginModel mPluginModel;
    private static MethodChannel sMethodChannel;

    public NewsMethodChannelPlugin(FlutterEngine flutterEngine, Context context) {
        mFlutterEngine = flutterEngine;
        mContext = context;
        mPluginModel = new PluginModel();
    }

    /**
     * 注册相应的组件 并把相应的插件暴露出去
     */
    public static NewsMethodChannelPlugin registerEventChannel(FlutterEngine flutterEngine, Context context) {
        sMethodChannel = new MethodChannel(flutterEngine.getDartExecutor(), CHANNEL);
        sNewsPlugin = new NewsMethodChannelPlugin(flutterEngine, context);
        sMethodChannel.setMethodCallHandler(sNewsPlugin);
        return sNewsPlugin;
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        switch (call.method) {
            //返回的方法名称
            case "getNewsList":
                //获取列表的方法
                //传递过来的信息
                Map<String, Integer> callResult = (Map<String, Integer>) call.arguments;
                mPluginModel.requestNesList("0",
                        String.valueOf(callResult.get("pageNum")), String.valueOf(callResult.get("pageSize")),
                        new BaseObserver<NewListsBean>(mContext) {
                            @Override
                            public void requestSuccess(NewListsBean sResData) {
                                CallBackBean callBackBean = new CallBackBean();
                                callBackBean.setData(sResData);
                                result.success(new Gson().toJson(callBackBean));
                            }

                            @Override
                            public void requestFail(String sResMsg) {
                                ToastUtil.show(mContext, sResMsg);
                            }
                        });
                break;
            case "getCurrentLanguage":
                String currentLocale = LocaleManager.getInstance().getLanguage(BaseApp.getAPPContext());
                String local = "";
                if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
                    //language 当前语言
                    local = LocaleManager.LANGUAGE_CHINA;
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
                    //language 当前语言
                    local = LocaleManager.LANGUAGE_NDEBELE;
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
                    //language 当前语言
                    local = LocaleManager.LANGUAGE_SHONA;
                } else {
                    local = LocaleManager.LANGUAGE_ENGLISH;
                }
                result.success(local);
                break;
            case "backToNative":
                ((Activity) mContext).finish();
                break;
            case "canPopFlutterPage":
                //是否能返回的页面
                boolean isBack = (boolean) call.arguments;
//                LogUtil.e("done", "onMethodCall: " + isBack);
//                ((CustomFlutterActivity) mContext).setSwipeBackEnable(!isBack);
                break;
            default:
        }
    }

    /**
     * 调用flutter端方法，无返回值
     */
    public void invokeMethod(String method, Object o) {
        sMethodChannel.invokeMethod(method, o);
    }

    /**
     * 调用flutter端方法，有返回值
     */
    public void invokeMethod(String method, Object o, MethodChannel.Result result) {
        sMethodChannel.invokeMethod(method, o, result);
    }
}
