package om.rrtx.mobile.functioncommon;

import om.rrtx.mobile.functioncommon.callback.CashierCallBack1;

/**
 * <AUTHOR>
 */
public class CashierCallBackManager1 {

    private static CashierCallBackManager1 sCallBackManager;

    public static CashierCallBackManager1 getInstance() {
        if (sCallBackManager == null) {
            synchronized (CashierCallBackManager1.class) {
                if (sCallBackManager == null) {
                    sCallBackManager = new CashierCallBackManager1();
                }
            }
        }
        return sCallBackManager;
    }

    private CashierCallBack1 mCashierCallBack;

    public void init(CashierCallBack1 cashierCallBack) {
        mCashierCallBack = cashierCallBack;
    }

    public CashierCallBack1 getCashierCallBack() {
        if (mCashierCallBack == null) {
            throw new RuntimeException("请先初始化CashierCallBack");
        }
        return mCashierCallBack;
    }
}
