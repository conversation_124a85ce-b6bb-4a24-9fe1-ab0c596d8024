package om.rrtx.mobile.functioncommon.bean

data class BillCodeBean(
    val billerCategory: String,
    val merTradeName: String,
    val recMerNo: String,
    var billerCode: String,
)

data class ZesaMerBean(
    val customerMobile: String,
    val customerName: String,
    val meterNo: String,
    val transCurrency: String,
    val usdMinLimt: String,
    val zwgMinLimt: String
)

class ZesaResultBean(
    val trxStatus: String,
    val buyToken: String,
    val actAmount: String,
    val token: String,
)

data class BillPayRecordBean(
    val countId: String,
    val current: Int,
    val maxLimit: String,
    val optimizeCountSql: Boolean,
    val orders: List<Any>,
    val pages: Int,
    val records: ArrayList<RecordBean>,
    val searchCount: Boolean,
    val size: Int,
    val total: Int
)

data class RecordBean(
    val billOrderNo: String,
    val currency: String,
    val meterNo: String,
    val trxAmt: String,
    val trxTime: String,
    val status: String
)
