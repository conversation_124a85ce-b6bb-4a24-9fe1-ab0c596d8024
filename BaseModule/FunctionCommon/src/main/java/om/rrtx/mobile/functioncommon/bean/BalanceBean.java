package om.rrtx.mobile.functioncommon.bean;

public class BalanceBean {

    /**
     * balance : 0.00
     * currType : USD
     * basicAccountBalance : 0.00
     * cashbackAccountBalance :
     */

    private String balance;
    private String currType;
    private String basicAccountBalance;
    private String cashbackAccountBalance;
    private boolean isShow;
    private boolean isNeedActivate;
    /**
     * 收费金额
     */
    private String totalAmt;
    private String feeAmt;
    private String taxAmt;
    private String lastBalanceEnquiryTime;
    private String isEnoughPay;

    public String isEnoughPay() {
        return isEnoughPay;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public String getFeeAmt() {
        return feeAmt;
    }

    public String getTaxAmt() {
        return taxAmt;
    }

    public String getLastBalanceEnquiryTime() {
        return lastBalanceEnquiryTime;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getCurrType() {
        return currType;
    }

    public void setCurrType(String currType) {
        this.currType = currType;
    }

    public String getBasicAccountBalance() {
        return basicAccountBalance;
    }

    public void setBasicAccountBalance(String basicAccountBalance) {
        this.basicAccountBalance = basicAccountBalance;
    }

    public String getCashbackAccountBalance() {
        return cashbackAccountBalance;
    }

    public void setCashbackAccountBalance(String cashbackAccountBalance) {
        this.cashbackAccountBalance = cashbackAccountBalance;
    }

    public boolean isShow() {
        return isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }

    public boolean isNeedActivate() {
        return isNeedActivate;
    }

    public void setNeedActivate(boolean needActivate) {
        isNeedActivate = needActivate;
    }
}
