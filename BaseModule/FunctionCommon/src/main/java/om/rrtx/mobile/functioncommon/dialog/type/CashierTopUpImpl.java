package om.rrtx.mobile.functioncommon.dialog.type;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.bean.TopUpSuccessBean;
import om.rrtx.mobile.functioncommon.dialog.CashierBankListFragment;
import om.rrtx.mobile.functioncommon.dialog.CashierPayImpl;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 */
public class CashierTopUpImpl implements CashierPayImpl {
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean bean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public  CashierTopUpImpl(AppCompatActivity activity, CashierOrderInfoBean cashierOrderInfoBean, PayMethodLiveData payMethodLiveData) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        bean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
    }

    @Override
    public void showInfo(View rootView) {
        LogUtil.e("TAG", "showInfo: 0");
        TextView amtTv = rootView.findViewById(R.id.amtTv);
        TextView tab1 = rootView.findViewById(R.id.tab1);
        TextView tab1Tv = rootView.findViewById(R.id.tab1Tv);
        TextView paymentMethod = rootView.findViewById(R.id.paymentMethod);
        TextView paymentMethodTv = rootView.findViewById(R.id.paymentMethodTv);
        ImageView paymentMethodIv = rootView.findViewById(R.id.paymentMethodIv);
        View paymentMethodView = rootView.findViewById(R.id.paymentMethodView);

        //设置订单金额
        amtTv.setText(bean.getCurrency() + " " + StringUtils.formatAmount(bean.getOrderAmt()));
        //设置币种

        //充值
        tab1.setText(R.string.order_info);
        tab1.setVisibility(View.VISIBLE);
        tab1Tv.setText(R.string.bank_to_OneMoney);
        tab1Tv.setVisibility(View.VISIBLE);

        //设置支付方式
        paymentMethod.setVisibility(View.VISIBLE);
        paymentMethodTv.setVisibility(View.VISIBLE);
        paymentMethodIv.setVisibility(View.VISIBLE);
        paymentMethodView.setVisibility(View.VISIBLE);

        //输入密码完成
        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        LogUtil.e("TAG", "showInfo: " + mPayMethodLiveData.getPsdFinish().hasObservers());
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            if (mIsRequestFinish || psd == null) {
                return;
            }
            LogUtil.e("TAG", "showInfo: 2");
            mIsRequestFinish = true;
            mCommonModel.requestTopUpAcc("", bean.getOrderAmt(), bean.getCardNo(),
                    psd, CommonConstants.PaymentPasswordType.PSD, bean.getTransferToken(), bean.getCurrency(), new BaseObserver<TopUpSuccessBean>(mActivity) {
                        @Override
                        public void requestSuccess(TopUpSuccessBean sResData) {
                            CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                            //保存一下支付方式
//                            PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
//                            SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.LOCALPAYMENTTYPE, new Gson().toJson(selectPaymentMethod));
                            mIsRequestFinish = false;
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (mPayPsdBottomFragment != null) {
                                mPayPsdBottomFragment.cancelPassword();
                            }
                            ToastUtil.show(mActivity, sResMsg);
                            mIsRequestFinish = false;
                        }
                    });
            if (mPayPsdBottomFragment != null) {
                mPayPsdBottomFragment.cancelPassword();
            }
        });
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        CashierBankListFragment cashierBankListFragment = CashierBankListFragment.newInstance(new Gson().toJson(cashierOrderInfoBean));
        cashierBankListFragment.show(baseBottomFragment.getChildFragmentManager(), CashierBankListFragment.class.getSimpleName());
    }

    @Override
    public void getPayMethod() {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        String pubLick = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.requestPaymentType(userId, bean.getOrderNo(), bean.getOrderAmt(),
                    bean.getPaymentProduct(),
                    bean.getMerNo(), bean.getTransType(), bean.getOrderSource(), bean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                        @Override
                        public void requestSuccess(PaymentBean paymentBean) {
                            mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                        }
                    });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mActivity) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.requestPaymentType(userId, bean.getOrderNo(), bean.getOrderAmt(),
                            bean.getPaymentProduct(),
                            bean.getMerNo(), bean.getTransType(), bean.getOrderSource(), bean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                                @Override
                                public void requestSuccess(PaymentBean paymentBean) {
                                    mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }

    @Override
    public void nextClick(String isFreePwd,BaseBottomFragment baseBottomFragment) {
        //获取选择的cardId
        PaymentBean.PaymentMethodListBean selectPaymentMethod = PaymentTypeHelper.getSelectPaymentMethod(mPayMethodLiveData.getPaymentTypeLV().getValue().getPaymentMethodList());
        bean.setCardNo(selectPaymentMethod.getCardId());
        bean.setPaymentType(selectPaymentMethod.getPaymentType());

        mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(bean));
        mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
    }
    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
