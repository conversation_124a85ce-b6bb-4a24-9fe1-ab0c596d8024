package om.rrtx.mobile.functioncommon.dialog.type;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import om.rrtx.mobile.functioncommon.CashierCallBackManager;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.bean.WithdrawalOrderBean;
import om.rrtx.mobile.functioncommon.dialog.CashierPayImpl;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 提现转账的实现
 */
public class CashierWithdrawalImpl implements CashierPayImpl {
    private AppCompatActivity mActivity;
    private CommonModel mCommonModel;
    private CashierOrderInfoBean bean;
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private boolean mIsRequestFinish = false;

    public CashierWithdrawalImpl(AppCompatActivity activity, CashierOrderInfoBean cashierOrderInfoBean, PayMethodLiveData payMethodLiveData) {
        mActivity = activity;
        mCommonModel = new CommonModel();
        bean = cashierOrderInfoBean;
        mPayMethodLiveData = payMethodLiveData;
    }

    @Override
    public void showInfo(View rootView) {
        TextView titleTv = rootView.findViewById(R.id.titleTv);
        TextView tab1 = rootView.findViewById(R.id.tab1);
        TextView tab1Tv = rootView.findViewById(R.id.tab1Tv);

        TextView paymentMethod = rootView.findViewById(R.id.paymentMethod);
        TextView paymentMethodTv = rootView.findViewById(R.id.paymentMethodTv);
        //View paymentMethodView = rootView.findViewById(R.id.paymentMethodView);


        //设置提现标题
//        titleTv.setText(R.string.withdrawal_label_withdrawal_details);

        //设置订单金额  外面，获取支付方式手动计算

         //设置商户名称
        tab1.setVisibility(View.VISIBLE);
        tab1Tv.setVisibility(View.VISIBLE);
        tab1.setText(R.string.order_info);
        tab1Tv.setText(bean.getOrderInfo());
//        String payFeeAmount = bean.getPayFeeAmount() + " " + CurrencyUtils.setCurrency(mActivity, bean.getCurrency());
//        tab1Tv.setText(payFeeAmount);

        // fee tax 在外面
        // 固定支付方式
        paymentMethod.setVisibility(View.VISIBLE);
        paymentMethodTv.setVisibility(View.VISIBLE);
        paymentMethodTv.setEnabled(false);
        //paymentMethodView.setVisibility(View.VISIBLE);


        TextView payTv = rootView.findViewById(R.id.payTv);
//        payTv.setText(R.string.withdrawal_btn_withdrawal);
        payTv.setEnabled(true);
        payTv.setBackgroundResource(R.drawable.common_usable_btn);

        mPayMethodLiveData.getPsdFinish().removeObservers(mActivity);
        mPayMethodLiveData.getPsdFinish().observe(mActivity, psd -> {
            if (mIsRequestFinish || psd == null) {
                return;
            }
            mIsRequestFinish = true;
            String pubLick = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, "");
            String userId = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.USERID, "");
            if (!TextUtils.isEmpty(pubLick)) {
                mCommonModel.requestWithdrawalOrder(bean.getRemark(),bean.getPayType(),bean.getBankNo(), bean.getCardNo(), bean.getOrderAmt(), psd, CommonConstants.PaymentPasswordType.PSD, bean.getPaymentToken(),
                        bean.getCurrency(), new BaseObserver<WithdrawalOrderBean>(mActivity) {
                            @Override
                            public void requestSuccess(WithdrawalOrderBean sResData) {
                                CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                                mIsRequestFinish = false;
                            }

                            @Override
                            public void requestFail(String sResMsg) {
                                if (mPayPsdBottomFragment != null) {
                                    mPayPsdBottomFragment.cancelPassword();
                                }
                                ToastUtil.show(mActivity, sResMsg);
                                mIsRequestFinish = false;
                            }

                            @Override
                            public void requestErrorBody(BaseBean<WithdrawalOrderBean> body) {
                                String status = body.getStatus();
                                String message = body.getMessage();
                                if (status.equals("RRB-01001007")||status.equals("RRB-01001010")) {
                                    // 密码错误
                                    if (mPayPsdBottomFragment != null) {
                                        mPayPsdBottomFragment.cancelPassword();
                                    }
                                    ToastUtil.show(mActivity, message);
                                } else {
                                    CashierCallBackManager.getInstance().getCashierCallBack().paymentFailed(message);
                                }
                                mIsRequestFinish = false;
                            }
                        });
            } else {
                mCommonModel.commonPub(new BaseObserver<PubBean>(mActivity) {
                    @Override
                    public void requestSuccess(PubBean sResData) {
                        SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                        mCommonModel.requestWithdrawalOrder(bean.getRemark(),bean.getPayType(),bean.getBankNo(), bean.getCardNo(), bean.getOrderAmt(), psd, CommonConstants.PaymentPasswordType.PSD, bean.getPaymentToken(),
                                bean.getCurrency(), new BaseObserver<WithdrawalOrderBean>(mActivity) {
                                    @Override
                                    public void requestSuccess(WithdrawalOrderBean sResData) {
                                        CashierCallBackManager.getInstance().getCashierCallBack().paymentSuccess(new Gson().toJson(sResData));
                                        mIsRequestFinish = false;
                                    }

                                    @Override
                                    public void requestFail(String sResMsg) {
                                        if (mPayPsdBottomFragment != null) {
                                            mPayPsdBottomFragment.cancelPassword();
                                        }
                                        ToastUtil.show(mActivity, sResMsg);
                                        mIsRequestFinish = false;
                                    }
                                });
                    }

                    @Override
                    public void requestFail(String sResMsg) {

                    }
                });
            }

            if (mPayPsdBottomFragment != null) {
                mPayPsdBottomFragment.cancelPassword();
            }
        });
    }

    @Override
    public void clickPayMethod(BaseBottomFragment baseBottomFragment, CashierOrderInfoBean cashierOrderInfoBean) {

    }

    @Override
    public void getPayMethod() {
        //这里可以有空实现因为不是所有的都可以选择支付方式
        String pubLick = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mActivity, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.requestPaymentType(userId, bean.getOrderNo(), bean.getOrderAmt(),
                    bean.getPaymentProduct(),
                    bean.getMerNo(), bean.getTransType(), bean.getOrderSource(), bean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                        @Override
                        public void requestSuccess(PaymentBean paymentBean) {
                            mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                            mPayMethodLiveData.getErrorLv().setValue(sResMsg);
                        }
                    });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mActivity) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mActivity, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.requestPaymentType(userId, bean.getOrderNo(), bean.getOrderAmt(),
                            bean.getPaymentProduct(),
                            bean.getMerNo(), bean.getTransType(), bean.getOrderSource(), bean.getCurrency(), new BaseObserver<PaymentBean>(mActivity) {
                                @Override
                                public void requestSuccess(PaymentBean paymentBean) {
                                    mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    mPayMethodLiveData.getPaymentTypeError().setValue(sResMsg);
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }

    @Override
    public void nextClick(String isFreePwd,BaseBottomFragment baseBottomFragment) {
        mPayPsdBottomFragment = PayPsdBottomFragment.newInstance(new Gson().toJson(bean));
        mPayPsdBottomFragment.show(baseBottomFragment.getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());
    }

    @Override
    public void onClose() {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
    }
}
