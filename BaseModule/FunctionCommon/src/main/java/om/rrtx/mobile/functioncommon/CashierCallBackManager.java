package om.rrtx.mobile.functioncommon;

import om.rrtx.mobile.functioncommon.callback.CashierCallBack;

/**
 * <AUTHOR>
 */
public class CashierCallBackManager {

    private static CashierCallBackManager sCallBackManager;

    public static CashierCallBackManager getInstance() {
        if (sCallBackManager == null) {
            synchronized (CashierCallBackManager.class) {
                if (sCallBackManager == null) {
                    sCallBackManager = new CashierCallBackManager();
                }
            }
        }
        return sCallBackManager;
    }

    private CashierCallBack mCashierCallBack;

    public void init(CashierCallBack cashierCallBack) {
        mCashierCallBack = cashierCallBack;
    }

    public CashierCallBack getCashierCallBack() {
        if (mCashierCallBack == null) {
            throw new RuntimeException("请先初始化CashierCallBack");
        }
        return mCashierCallBack;
    }
}
