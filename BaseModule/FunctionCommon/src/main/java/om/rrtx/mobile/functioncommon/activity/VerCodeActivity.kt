package om.rrtx.mobile.functioncommon.activity

import android.app.Activity
import android.content.Intent
import android.text.method.PasswordTransformationMethod
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.common_activity_ver_code.code
import kotlinx.android.synthetic.main.common_activity_ver_code.hint2_tv
import kotlinx.android.synthetic.main.common_activity_ver_code.mobile_tv
import kotlinx.android.synthetic.main.common_activity_ver_code.tv_down_time
import kotlinx.android.synthetic.main.common_activity_ver_code.tv_send
import kotlinx.android.synthetic.main.common_base_title.backIv
import kotlinx.android.synthetic.main.common_base_title.leftBg
import kotlinx.android.synthetic.main.common_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.R
import om.rrtx.mobile.functioncommon.databinding.CommonActivityVerCodeBinding
import om.rrtx.mobile.functioncommon.utils.CodeDownTimerUtils
import om.rrtx.mobile.functioncommon.vm.CommonViewModel
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.RxKeyPadFragment
import om.rrtx.mobile.rrtxcommon1.widget.RxKeyPadView
import om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText

class VerCodeActivity : BaseVVMActivity<CommonViewModel, CommonActivityVerCodeBinding>() ,
    VerificationCodeEditText.VerificationCallBack,
    RxKeyPadView.KeyPadCallBack,
    RxKeyPadFragment.KeyPadFragmentBack{

    private var mobile: String = ""
    private var mflag: String = ""
    private var verCode: String = ""
    private lateinit var mKeyPadFragment: RxKeyPadFragment

    override fun createContentView() = R.layout.common_activity_ver_code

    override fun doGetExtra() {
        mobile = intent.getStringExtra(BaseConstants.Transmit.MOBILE).toString()
        mflag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun initView() {
        when (mflag) {
            BaseConstants.JumpFlag.SET_JUNIOR_PIN -> {
                titleTv.setText(R.string.change_PIN)
                hint2_tv.setText(R.string.send_ver_code)
                code.isCursorVisible = true
                code.requestFocus()
            }

            BaseConstants.JumpFlag.Upgrade_junior_Account -> {
                titleTv.setText(R.string.upgrade_Regular_Account)
                hint2_tv.setText(R.string.send_ver_code)
                code.isCursorVisible = true
                code.requestFocus()
            }

            BaseConstants.JumpFlag.Delete_junior_Account -> {
                titleTv.setText(R.string.delete_Account)
                hint2_tv.setText(R.string.send_ver_code)
                code.isCursorVisible = true
                code.requestFocus()
            }

            BaseConstants.JumpFlag.RESETJUMP -> {
                titleTv.setText(R.string.reset_PIN)
                hint2_tv.setText(R.string.send_ver_code)
                code.isFocusable = false
            }

            else -> {
                titleTv.setText(R.string.forgot_PIN)
                hint2_tv.setText(R.string.send_ver_code)
                code.isFocusable = false
            }
        }
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)

        if (BaseConstants.JumpFlag.Register_junior_Account==mflag)
        {
            mobile_tv.text = getString(R.string.jia_263)+ " " + mobile
        }else{
            mobile_tv.text = StringUtils.stringMask(getString(R.string.nommal_263), mobile)
        }

        //让EditText失去焦点，然后获取点击事件
        //code.isFocusable = false
        code.setIsPassword(true)
        code.transformationMethod = PasswordTransformationMethod.getInstance()

        code.setVerificationCallBack(this)
    }

    override fun initData() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()

        sendCode()

        leftBg.setOnClickListener {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        code.setText("")
        code.showKeyBoard()
    }

    override fun initClickListener() {

        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    tv_send -> {
                        sendCode()
                        tv_down_time.visibility = View.VISIBLE
                        tv_send.visibility = View.GONE
                    }
                }
            }

        }.apply {
            tv_send.setOnClickListener(this)
        }
    }

    override fun initVMListener() {
        viewModel.mCodeBeanLD.observe(this) {
            if (it.smsCode != null) verCode = it.smsCode
        }
        viewModel.mCodeVerifyLD.observe(this) {
            if (BaseConstants.JumpFlag.LOGIN_FORGET_JUMP == mflag) {
                val hasSecretWord = SharedPreferencesUtils.getParam(
                    mContext,
                    BaseConstants.SaveParameter.HASECRETWORD,
                    "0"
                )

                if (hasSecretWord=="1") {
                    ARouter.getInstance()
                        .build(ARouterPath.SecurityPath.ChangePinActivity)
                        .withString(BaseConstants.Transmit.PINPAYMENT, BaseConstants.Check.SETFIRST)
                        .withString(BaseConstants.Transmit.JUMPFLAG, mflag)
                        .navigation()
                } else {
                    jumpSetPin()
                }
            } else {
                jumpSetPin()
            }

        }
    }

    private fun jumpSetPin() {
        val aRouter = ARouter.getInstance()
            .build(ARouterPath.SecurityPath.SetPinActivity)
            .withString(BaseConstants.Transmit.JUMPFLAG, mflag)

        when (mflag) {
            BaseConstants.JumpFlag.Upgrade_junior_Account,
            BaseConstants.JumpFlag.Delete_junior_Account,
            -> {
                aRouter.withString(
                    BaseConstants.Transmit.PINPAYMENT,
                    BaseConstants.Check.AUTHENTICATION
                )
            }

            else -> {
                aRouter.withString(
                    BaseConstants.Transmit.PINPAYMENT,
                    BaseConstants.Check.SETFIRST
                )

            }
        }
        aRouter.navigation()
    }

    private fun sendCode() {
        var type = when (mflag) {
            BaseConstants.JumpFlag.LOGIN_FORGET_JUMP, BaseConstants.JumpFlag.RESETJUMP -> {
                "ForgetPassword"
            }

            BaseConstants.JumpFlag.SET_JUNIOR_PIN,
            BaseConstants.JumpFlag.Upgrade_junior_Account,
            BaseConstants.JumpFlag.Delete_junior_Account,
            -> {
                ""
            }

            else -> "Registration"
        }
        viewModel.requestCode(mobile, type)
        startDownTime()
    }

    private fun startDownTime() {
        tv_send.visibility = View.GONE
        val countDownTimerUtils = CodeDownTimerUtils(tv_down_time,tv_send, 60000, 1000)
        countDownTimerUtils.start()
    }

    companion object {
        fun jump(context: Activity, mobile: String, flag: String) {
            val intent = Intent(context, VerCodeActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.MOBILE, mobile)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, flag)
            context.startActivity(intent)
        }
    }

    override fun inputCode(input: String?) {
        code.setText(input)
        if (input!!.length == 6) {
            mKeyPadFragment.dismiss()
                verCode = input
                if (input == verCode) {
                    viewModel.requestValidateSms(mobile, input)
                } else {
                    ToastUtil.show(mContext, ResourceHelper.getString(R.string.please_check))
                }
                code.text!!.clear()
        }
    }

    override fun onKeyPadPause() {
        if (code.length() < 6) {
            code.setText("")
        }
    }

    override fun showKeyBoard() {
        mKeyPadFragment = RxKeyPadFragment("1")
        mKeyPadFragment.setKeyPadFragmentBack(this)
        code.setText("")
        mKeyPadFragment.show(supportFragmentManager, "")
    }
}