package om.rrtx.mobile.functioncommon.activity;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.SelectBankBean;
import om.rrtx.mobile.functioncommon.presenter.AddBankPresenter;
import om.rrtx.mobile.functioncommon.view.AddBankView;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.BankCardTextWatcher;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

public class AddBankActivity extends BaseSuperActivity<AddBankView, AddBankPresenter>
        implements AddBankView {

    private static final int REQUEST = 0x1;
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.fullNameTie)
    TextInputEditText mFullNameTie;
    @BindView(R2.id.bankSelectTie)
    TextInputEditText mBankSelectTie;
    @BindView(R2.id.cardNoTie)
    TextInputEditText mCardNoTie;
    @BindView(R2.id.nextTv)
    TextView mNextTv;
    @BindView(R2.id.bankSelectTil)
    TextInputLayout mBankSelectTil;
    private boolean isSelect;
    private SelectBankBean mSelectBankBean;

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    public static void jumpAddBank(Context context) {
        Intent intent = new Intent(context, AddBankActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.common_activity_add_bank;
    }

    @Override
    protected AddBankPresenter createPresenter() {
        return new AddBankPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.common_ic_back_black);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bankcard_title_bankcard_bind);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
    }

    @Override
    public void initDate() {
        super.initDate();
        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
        mFullNameTie.setText(userName);

        BankCardTextWatcher.bind(mCardNoTie);

        mBankSelectTie.setFocusable(false);
    }

    @Override
    public void initListener() {
        super.initListener();
        mBankSelectTie.setOnClickListener(mClickListener);
        mCardNoTie.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String cardNoStr = mCardNoTie.getText().toString();
                if (cardNoStr.length() >= 14 && isSelect) {
                    mNextTv.setBackgroundResource(R.drawable.common_usable_btn);
                    mNextTv.setEnabled(true);
                } else {
                    mNextTv.setBackgroundResource(R.drawable.common_unusable_btn);
                    mNextTv.setEnabled(false);
                }
            }
        });
        mNextTv.setOnClickListener(mClickListener);
        mLeftBg.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.bankSelectTie) {
                Intent intent = new Intent(mContext, SelectBankCardActivity.class);
                if (mSelectBankBean != null) {
                    intent.putExtra(CommonConstants.Transmit.DETAILSBEAN, new Gson().toJson(mSelectBankBean));
                }
                startActivityForResult(intent, REQUEST);
            } else if (view.getId() == R.id.nextTv) {
                String cardNo = mCardNoTie.getText().toString().replace(" ", "");
                mPresenter.requestBindBank(cardNo, "0", mSelectBankBean.getBankNo());
            } else if (view.getId() == R.id.leftBg) {
                finish();
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST && resultCode == RESULT_OK) {
            if (data != null) {
                isSelect = true;
                String selectBeanJson = data.getStringExtra(CommonConstants.Transmit.DETAILSBEAN);
                mSelectBankBean = new Gson().fromJson(selectBeanJson, SelectBankBean.class);
                mBankSelectTie.setText(mSelectBankBean.getBankName());

                String cardNoStr = mCardNoTie.getText().toString();
                if (cardNoStr.length() >= 14 && isSelect) {
                    mNextTv.setBackgroundResource(R.drawable.common_usable_btn);
                    mNextTv.setEnabled(true);
                } else {
                    mNextTv.setBackgroundResource(R.drawable.common_unusable_btn);
                    mNextTv.setEnabled(false);
                }
            }
        }
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void bindBankSuccess(String cardNo) {
        AddBankSuccessActivity.jumpAddBankSuccess(mContext, new Gson().toJson(mSelectBankBean), mCardNoTie.getText().toString().replace(" ", ""));
        finish();
    }
}
