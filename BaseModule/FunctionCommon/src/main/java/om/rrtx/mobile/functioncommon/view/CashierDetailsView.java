package om.rrtx.mobile.functioncommon.view;

/**
 * <AUTHOR>
 */
public interface CashierDetailsView {
//    /**
//     * 请求失败
//     *
//     * @param sResMsg
//     */
//    void requestFail(String sResMsg);
//
//    /**
//     * 订单信息
//     *
//     * @param orderInfoBean
//     */
//    void getOrderInfoSuccess(OrderInfoBean orderInfoBean);
//
//    /**
//     * 支付类型
//     *
//     * @param paymentTypeBean 支付类型实体
//     */
//    void paymentTypeSuccess(PaymentTypeBean paymentTypeBean);
}
