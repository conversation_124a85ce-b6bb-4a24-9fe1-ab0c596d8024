package om.rrtx.mobile.functioncommon.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import om.rrtx.mobile.functioncommon.bean.PaymentBean;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 银行卡适配器
 */
public class BankListRvAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<PaymentBean.PaymentMethodListBean> mList;
    private RVAdapterItemClickListener<PaymentBean.PaymentMethodListBean> mListener;

    public BankListRvAdapter(Context context) {
        mContext = context;
        mList = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.common_item_bank_list, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        PaymentBean.PaymentMethodListBean cardListBean = mList.get(position);

        TextView bankNameTv = holder.getView(R.id.bankNameTv);
        ImageView selectIv = holder.getView(R.id.selectIv);
        ImageView iconIv = holder.getView(R.id.iconIv);
        TextView balanceHint = holder.getView(R.id.balanceHint);

        if (TextUtils.equals(cardListBean.getPaymentType(), "02")) {
            //余额支付
            String showBalanceStr = mContext.getResources().getString(R.string.oneMoney) +
                    "-" + CurrencyUtils.setCurrency(mContext, cardListBean.getCurrency()) + StringUtils.formatAmount(cardListBean.getBalance());
            bankNameTv.setText(showBalanceStr);
        } else if (TextUtils.equals(cardListBean.getPaymentType(), "03")) {
            //银行卡支付
            bankNameTv.setText(cardListBean.getBankName() + " (" + cardListBean.getCardNo() + ")");
        } else {
            holder.itemView.setVisibility(View.GONE);
        }

        String isBalanceEnough = cardListBean.getIsBalanceEnough();
        String isAvailable = cardListBean.getIsAvailable();
        //设置余额是否充足
        if (TextUtils.equals(isBalanceEnough, "1") && TextUtils.equals(isAvailable, "1")) {
            //余额充足
            balanceHint.setVisibility(View.GONE);
            bankNameTv.setTextColor(mContext.getResources().getColor(R.color.common_text_1d2129));
            //iconIv.setImageResource(R.drawable.common_icon_e_wallet);
            setIcon(iconIv, cardListBean.getBankName(), true);
        } else if (TextUtils.equals(isAvailable, "0")) {
            //币种不同不可用
            balanceHint.setVisibility(View.GONE);
            bankNameTv.setTextColor(mContext.getResources().getColor(R.color.common_text_86909C));
            //iconIv.setImageResource(R.drawable.common_icon_e_wallet_unsleect);
            setIcon(iconIv, cardListBean.getBankName(), false);
        } else if (TextUtils.equals(isBalanceEnough, "0")) {
            //余额不足
            balanceHint.setVisibility(View.VISIBLE);
            balanceHint.setText(mContext.getResources().getString(R.string.insufficient_balance));
            bankNameTv.setTextColor(mContext.getResources().getColor(R.color.common_text_86909C));
            //iconIv.setImageResource(R.drawable.common_icon_e_wallet_unsleect);
            setIcon(iconIv, cardListBean.getBankName(), false);
        }

        if (TextUtils.equals(isBalanceEnough, "1") && TextUtils.equals(isAvailable, "1") && TextUtils.equals(cardListBean.getIsSelected(), "1")) {
            selectIv.setVisibility(View.VISIBLE);
        } else {
            selectIv.setVisibility(View.GONE);
        }

            /*if (TextUtils.equals(cardListBean.getIsOpen(), "1")) {
                bankNameTv.setTextColor(mContext.getResources().getColor(R.color.color_212121));
                //iconIv.setImageResource(R.drawable.ic_bank);
                setIcon(iconIv,cardListBean.getBankName(),true);
            } else {
                bankNameTv.setTextColor(mContext.getResources().getColor(R.color.color_999999));
                //iconIv.setImageResource(R.drawable.common_ic_bankcard_un_open);
                setIcon(iconIv,cardListBean.getBankName(),false);
            }*/

        if (isClick(cardListBean)) {
            holder.itemView.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    LogUtil.e("done", "itemClickListener: " + cardListBean.getPaymentType());
                    if (!TextUtils.equals(cardListBean.getPaymentType(), "99")) {
                        handleListShow(position);
                        notifyDataSetChanged();
                    }
                    if (mListener != null) {
                        mListener.itemClickListener(cardListBean, position);
                    }
                }
            });
        }
    }

    private void setIcon(ImageView iconIv, String bankName, boolean isSelect) {
        int iconId = R.drawable.bank_default_selected;
        if (bankName.contains("CABS")) {
            iconId = isSelect ? R.drawable.cabs_selected : R.drawable.cabs_unselect;
        } else if (bankName.contains("CBZ")) {
            iconId = isSelect ? R.drawable.cbz_selected : R.drawable.cbz_unselect;
        } else if (bankName.contains("FBC")) {
            iconId = isSelect ? R.drawable.fbc_selected : R.drawable.fbc_unselect;
        } else if (bankName.contains("POSB")) {
            iconId = isSelect ? R.drawable.posb_selected : R.drawable.posb_unselect;
        } else if (bankName.contains("NBS")) {
            iconId = isSelect ? R.drawable.nbs_selected : R.drawable.nbs_unselect;
        } else {
            iconId = isSelect ? R.drawable.bank_default_selected : R.drawable.bank_default_unselect;
        }
        iconIv.setImageResource(iconId);

    }

    /**
     * 条目是否可以点击
     *
     * @param cardListBean 实体类
     * @return
     */
    private boolean isClick(PaymentBean.PaymentMethodListBean cardListBean) {
        if (TextUtils.equals(cardListBean.getIsBalanceEnough(), "1") && TextUtils.equals(cardListBean.getIsAvailable(), "1")) {
            return true;
        } else {
            return false;
        }
    }

    public void handleListShow(int selectIndex) {
        if (mList == null) {
            return;
        }

        for (int i = 0; i < mList.size(); i++) {
            PaymentBean.PaymentMethodListBean cardListBean = mList.get(i);
            if (selectIndex == i) {
                cardListBean.setIsSelected("1");
            } else {
                cardListBean.setIsSelected("0");
            }
        }
    }

    private String bankLastFour(String cardNo) {
        if (TextUtils.isEmpty(cardNo)) {
            return "";
        }

        if (cardNo.length() <= 4) {
            return cardNo;
        }

        StringBuilder sb = new StringBuilder(cardNo);
        return sb.substring(sb.length() - 4);
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    public void setDataList(List<PaymentBean.PaymentMethodListBean> cardList) {
        for (int i=0;i<cardList.size();i++){
            if ("99".equals(cardList.get(i).getPaymentType())){
                cardList.remove(i);
            }
        }
        mList = cardList;
        notifyDataSetChanged();
    }

    public void setListener(RVAdapterItemClickListener<PaymentBean.PaymentMethodListBean> listener) {
        mListener = listener;
    }
}
