package om.rrtx.mobile.functioncommon.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import om.rrtx.mobile.functioncommon.R;


/**
 * <AUTHOR>
 * 输入Pin的View
 */
public class PayPasswordView extends LinearLayout
        implements View.OnClickListener, PasswordEditText.PasswordFullListener {
    private PasswordEditText mPasswordEditText;
    private Password mPassword;

    public PayPasswordView(Context context) {
        this(context, null);
    }

    public PayPasswordView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PayPasswordView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //这里初始化的应该是密码框和键盘
        View rootView = inflate(context, R.layout.common_pay_password_layout, this);
        ConstraintLayout keyBoardView = rootView.findViewById(R.id.keyboard);
        mPasswordEditText = rootView.findViewById(R.id.pet_passed);
        TextView forgotTv = rootView.findViewById(R.id.forgotTv);
        //设置相应的监听
        mPasswordEditText.setPasswordFullListener(this);
        //取消相应的点击事件
        mPasswordEditText.setEnabled(false);
        forgotTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPassword != null) {
                    mPassword.forgotCallBack();
                }
            }
        });
        setItemClickListener(keyBoardView);
    }

    /**
     * 给每一个自定义数字键盘上的View 设置点击事件
     *
     * @param view 相应的父View
     */
    private void setItemClickListener(View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                //不断的给里面所有的View设置setOnClickListener
                View childView = ((ViewGroup) view).getChildAt(i);
                setItemClickListener(childView);
            }
        } else {
            view.setOnClickListener(this);
        }
    }

    @Override
    public void onClick(View v) {
        if (v instanceof TextView) {
            String number = ((TextView) v).getText().toString().trim();
            mPasswordEditText.addPassword(number);
        }
        if (v instanceof ImageView) {
            mPasswordEditText.deletePassword();
        }
    }


    @Override
    public void passwordFull(String password) {
        if (mPassword != null) {
            mPassword.passwordCallBack(password);
        }
    }

    @Override
    public void passwordClear() {
        cancelPassword();
    }

    public interface Password {
        /**
         * 当填充满的时候回调
         *
         * @param password 相应的密码
         */
        void passwordCallBack(String password);

        void forgotCallBack();
    }

    public void setPassword(Password password) {
        mPassword = password;
    }

    public void cancelPassword() {
        mPasswordEditText.cancelPassword();
    }
}

