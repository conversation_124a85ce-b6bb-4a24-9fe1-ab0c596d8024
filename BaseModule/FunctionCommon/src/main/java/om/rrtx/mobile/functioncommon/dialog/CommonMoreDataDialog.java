package om.rrtx.mobile.functioncommon.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.adapter.CurrencyListRvAdapter;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;


/**
 * <AUTHOR> zfw
 * @time : 2023/9/8 10:47
 * @desc :
 */
public class CommonMoreDataDialog extends Dialog implements RVAdapterItemClickListener<String> {

    private int selectPositiom = 0;
    private String selectValue = "";
    TextView mConfirmTv;
    TextView mCancelTv;
    RecyclerView mListRv;
    RelativeLayout viewRoot;
    private String mType;
    private Context mContext;
    private List<String> mList = new ArrayList<>();
    private CurrencyListRvAdapter mAdapter;
    OnCurrencyClickListener listener;

    public CommonMoreDataDialog(@NonNull Context context, String type, List<String> list) {
        super(context, R.style.transparentBottomDialog);
        this.mContext = context;
        this.mType = type;
        this.mList = list;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.common_dialog_currency_list);
        setCancelable(true);
        setCanceledOnTouchOutside(true);
        setLocation();
        initView();
        initListener();
        initData();
    }

    private void initData() {
        if (mList.size() > 2) {
            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = viewRoot.getLayoutParams();
            layoutParams.height = ExtensionsKt.pt2px(458);
            viewRoot.setLayoutParams(layoutParams);
        } else {
            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = viewRoot.getLayoutParams();
            layoutParams.height = ExtensionsKt.pt2px(378);
            viewRoot.setLayoutParams(layoutParams);
        }
        mAdapter.setDataList(mList);
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.BOTTOM);
            window.setAttributes(WL);
        }
    }

    /**
     * 初始化控件
     */
    private void initView() {
        viewRoot = findViewById(R.id.view);
        mCancelTv = findViewById(R.id.tv_cancel);
        mConfirmTv = findViewById(R.id.tv_confirm);
        mListRv = findViewById(R.id.selectRv);
    }

    private void initListener() {
        mListRv.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new CurrencyListRvAdapter(mContext);
        mAdapter.setListener(this);
        mListRv.setAdapter(mAdapter);
        mCancelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mConfirmTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                dismiss();
                if (listener != null) {
                    if (TextUtils.isEmpty(selectValue)) {
                        selectValue = mList.get(0);
                    }
                    listener.onCurrencyClick(selectPositiom, selectValue);
                }
            }
        });
    }

    @Override
    public void itemClickListener(String s, int position) {
        selectValue = s;
        selectPositiom = position;
        /*dismiss();
        if (listener != null) {
            listener.onCurrencyClick(position, s);
        }*/
    }

    public void setOnCurrencyClickListener(OnCurrencyClickListener listener) {
        this.listener = listener;
    }

    public interface OnCurrencyClickListener {
        void onCurrencyClick(int position, String value);
    }
}
