package om.rrtx.mobile.functioncommon.dialog;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.R2;
import om.rrtx.mobile.functioncommon.activity.AddBankActivity;
import om.rrtx.mobile.functioncommon.adapter.BankListRvAdapter;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.presenter.CashierBankListPresenter;
import om.rrtx.mobile.functioncommon.utils.LogUtil;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.functioncommon.view.CashierBankListView;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;

/**
 * <AUTHOR>
 */
public class CashierBankListFragment extends BaseBottomFragment<CashierBankListView, CashierBankListPresenter>
        implements CashierBankListView, RVAdapterItemClickListener<PaymentBean.PaymentMethodListBean> {
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.bankListRv)
    RecyclerView mBankListRv;
    private PayMethodLiveData mPayMethodLiveData;
    private BankListRvAdapter mBankListRvAdapter;
    private PaymentBean mPaymentBean;
    private CashierOrderInfoBean mCashierOrderInfoBean;
    private RefreshBanksReceive mRefreshBanksReceive;
    private LocalBroadcastManager mLocalBroadcastManager;
    private static final int REQUESTCODE = 0x1;

    public static CashierBankListFragment newInstance(String xWallerPayBeanJson) {
        Bundle args = new Bundle();
        args.putString(CommonConstants.Transmit.CASHIERORDERINFO, xWallerPayBeanJson);
        CashierBankListFragment fragment = new CashierBankListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.common_dialog_fragment_bank_list;
    }

    @Override
    public CashierBankListPresenter createPresenter() {
        return new CashierBankListPresenter(mContext);
    }

    @Override
    protected void initView(View rootView) {
        setCancelable(false);
        mTitleTv.setText(R.string.payment_method);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_212121));
        //mBackIv.setImageResource(R.drawable.common_ic_close);
        mTitleTv.setBackgroundResource(R.drawable.common_white_corner_16_bg);
        mBackIv.setImageResource(R.drawable.common_ic_close);
    }

    @Override
    public void initDate() {
        super.initDate();
        Bundle bundle = getArguments();
        if (bundle != null) {
            String cashierOrderInfo = bundle.getString(CommonConstants.Transmit.CASHIERORDERINFO);
            mCashierOrderInfoBean = new Gson().fromJson(cashierOrderInfo, CashierOrderInfoBean.class);
        }

        mBankListRv.setLayoutManager(new LinearLayoutManager(mContext));
        mBankListRvAdapter = new BankListRvAdapter(mContext);
        mBankListRvAdapter.setListener(this);
        mBankListRv.setAdapter(mBankListRvAdapter);

        //接收一条广播,刷新银行列表
        mRefreshBanksReceive = new RefreshBanksReceive();
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(mContext);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(CommonConstants.REFRESHBANKSACTION);
        mLocalBroadcastManager.registerReceiver(mRefreshBanksReceive, intentFilter);

        //注册相相应的数据
        if (getActivity() != null) {
            mPayMethodLiveData = new ViewModelProvider(getActivity()).get(PayMethodLiveData.class);
            mPayMethodLiveData.getPaymentTypeLV().observe(getActivity(), paymentTypeBean -> {
                mPaymentBean = paymentTypeBean;
                setShowInfo(mPaymentBean);
            });
        }
    }

    /**
     * 接收的广播
     */
    private class RefreshBanksReceive extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            //这里处理接收的信息
            if (intent != null) {
                if (TextUtils.equals(intent.getAction(), CommonConstants.REFRESHBANKSACTION)) {
                    if (mCashierOrderInfoBean != null) {
                        //这里是添加银行卡之后的广播
                        mPresenter.requestPaymentType(mPayMethodLiveData, mCashierOrderInfoBean.getOrderAmt(),
                                mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getMerNo(), mCashierOrderInfoBean.getTransType(),
                                mCashierOrderInfoBean.getOrderSource(),mCashierOrderInfoBean.getCurrency());
                    }
                }
            }
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mCustomClickListener);
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                dismiss();
            }
        }
    };

    private void setShowInfo(PaymentBean paymentBean) {
        //处理一下集合,如果余额不足放到最下面
        List<PaymentBean.PaymentMethodListBean> paymentMethodList = paymentBean.getPaymentMethodList();
        boolean isOpenBank = PaymentTypeHelper.isOpenBank(paymentBean.getPaymentTypeList());
        PaymentTypeHelper.handlerBalanceList(paymentMethodList, isOpenBank);
        //这里要要处理一下之前的数据
        mBankListRvAdapter.setDataList(paymentMethodList);
    }

    @Override
    public void itemClickListener(PaymentBean.PaymentMethodListBean cardListBean, int position) {
        LogUtil.e("done", "点击了itemClickListener: "+cardListBean);
        Log.e("CashierBankListFragment>>>", "zfw itemClickListener>>> :"+cardListBean );
        if (TextUtils.equals(cardListBean.getPaymentType(), "99")) {
            //添加按钮
            AddBankActivity.jumpAddBank(mContext);
        } else {
            //其他的类型
            //根据类型处理数据
            mPaymentBean.setSelectPosition(position);
            PaymentBean paymentBean = PaymentTypeHelper.handlerData(mPaymentBean, cardListBean);
            if (mPayMethodLiveData != null) {
                mPayMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
            }
            dismiss();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mLocalBroadcastManager != null) {
            mLocalBroadcastManager.unregisterReceiver(mRefreshBanksReceive);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUESTCODE && resultCode == Activity.RESULT_OK) {
            LogUtil.e("TAG", "onActivityResult: ");
            if (mCashierOrderInfoBean != null) {
                //这里是添加银行卡之后的广播
                mPresenter.requestPaymentType(mPayMethodLiveData, mCashierOrderInfoBean.getOrderAmt(), mCashierOrderInfoBean.getPaymentProduct(), mCashierOrderInfoBean.getMerNo(), mCashierOrderInfoBean.getTransType(), mCashierOrderInfoBean.getOrderSource(),mCashierOrderInfoBean.getCurrency());
            }
        }
    }
}
