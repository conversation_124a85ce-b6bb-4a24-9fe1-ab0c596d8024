package om.rrtx.mobile.functioncommon.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.bean.PubBean;
import om.rrtx.mobile.functioncommon.dialog.CashierDetailsBottomFragment;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.functioncommon.view.CashierBankListView;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 */
public class CashierBankListPresenter extends BasePresenter<CashierBankListView> {
    private CommonModel mCommonModel;
    private Context mContext;

    public CashierBankListPresenter(Context context) {
        mCommonModel = new CommonModel();
        mContext = context;
    }

    public void requestPaymentType(PayMethodLiveData payMethodLiveData, String orderAmt, String product, String merNo, String transType, String orderSource,String currency) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mCommonModel.requestPaymentType(userId, "", orderAmt, product, merNo, transType, orderSource,currency, new BaseObserver<PaymentBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    CashierDetailsBottomFragment.mIsFirst = true;
                    payMethodLiveData.getErrorLv().setValue(sResMsg);
                }

                @Override
                public void requestSuccess(PaymentBean paymentBean) {
                    payMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                }
            });
        } else {
            mCommonModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mCommonModel.requestPaymentType(userId, "", orderAmt, product, merNo, transType, orderSource,currency, new BaseObserver<PaymentBean>(mContext) {
                        @Override
                        public void requestFail(String sResMsg) {
                            CashierDetailsBottomFragment.mIsFirst = true;
                            payMethodLiveData.getErrorLv().setValue(sResMsg);
                        }

                        @Override
                        public void requestSuccess(PaymentBean paymentBean) {
                            payMethodLiveData.getPaymentTypeLV().setValue(paymentBean);
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }

    }
}
