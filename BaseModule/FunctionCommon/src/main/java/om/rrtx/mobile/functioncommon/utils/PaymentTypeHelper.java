package om.rrtx.mobile.functioncommon.utils;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.R;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;

/**
 * <AUTHOR>
 */
public class PaymentTypeHelper {

    private static PaymentBean.PaymentMethodListBean sAddBean;

    /**
     * 处理paymentType数据
     *
     * @param typeBean 选择的数据
     * @return 处理后的数据
     */
    public static PaymentBean handlerData(PaymentBean typeBean, PaymentBean.PaymentMethodListBean selectPaymentMethodListBean) {

        List<PaymentBean.PaymentMethodListBean> paymentMethodList = typeBean.getPaymentMethodList();

        if (TextUtils.equals(selectPaymentMethodListBean.getPaymentType(), "02")) {
            //余额支付
            for (int i = 0; i < paymentMethodList.size(); i++) {
                PaymentBean.PaymentMethodListBean paymentMethodListBean = paymentMethodList.get(i);
                if (TextUtils.equals(paymentMethodListBean.getPaymentType(), selectPaymentMethodListBean.getPaymentType())) {
                    paymentMethodListBean.setIsSelected("1");
                } else {
                    paymentMethodListBean.setIsSelected("0");
                }

                if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "99")) {
                    paymentMethodList.remove(i);
                    i--;
                    break;
                }
            }
        } else if (TextUtils.equals(selectPaymentMethodListBean.getPaymentType(), "03")) {
            //银行卡支付
            for (int i = 0; i < paymentMethodList.size(); i++) {
                PaymentBean.PaymentMethodListBean paymentMethodListBean = paymentMethodList.get(i);
                if (TextUtils.equals(paymentMethodListBean.getPaymentType(), selectPaymentMethodListBean.getPaymentType())
                        && TextUtils.equals(paymentMethodListBean.getCardNo(), selectPaymentMethodListBean.getCardNo())) {
                    paymentMethodListBean.setIsSelected("1");
                } else {
                    paymentMethodListBean.setIsSelected("0");
                }

                if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "99")) {
                    paymentMethodList.remove(i);
                    i--;
                    break;
                }
            }
        }

        return typeBean;
    }

    /**
     * 获取选择的支付方式
     * 这里默认选择应该是本地的,但是需要考虑本地的不能使用的情况
     *
     * @param paymentMethodListBean 选择的支付方式
     * @return 返回支付方式
     */
    public static PaymentBean.PaymentMethodListBean getSelectPaymentMethod(List<PaymentBean.PaymentMethodListBean> paymentMethodListBean) {

        PaymentBean.PaymentMethodListBean resultPaymentMethod = null;

        if (paymentMethodListBean == null || paymentMethodListBean.size() == 0) {
            return resultPaymentMethod;
        }

        for (int i = 0; i < paymentMethodListBean.size(); i++) {
            PaymentBean.PaymentMethodListBean paymentMethodListBeanCurrent = paymentMethodListBean.get(i);
            if (TextUtils.equals(paymentMethodListBeanCurrent.getIsBalanceEnough(), "1") && TextUtils.equals(paymentMethodListBeanCurrent.getIsAvailable(), "1") && TextUtils.equals(paymentMethodListBeanCurrent.getIsSelected(), "1")) {
                //选择的支付方式
                resultPaymentMethod = paymentMethodListBeanCurrent;
                break;
            }
        }

        return resultPaymentMethod;
    }

    public static PaymentBean.PaymentMethodListBean getSelectPayMethod(List<PaymentBean.PaymentMethodListBean> paymentMethodListBean) {
        PaymentBean.PaymentMethodListBean resultPaymentMethod = paymentMethodListBean.get(0);
        for (int i = 0; i < paymentMethodListBean.size(); i++) {
            PaymentBean.PaymentMethodListBean paymentMethodListBeanCurrent = paymentMethodListBean.get(i);
            if (TextUtils.equals(paymentMethodListBeanCurrent.getIsAvailable(), "1") && TextUtils.equals(paymentMethodListBeanCurrent.getIsSelected(), "1")) {
                //选择的支付方式
                resultPaymentMethod = paymentMethodListBeanCurrent;
                break;
            }
        }
        return resultPaymentMethod;
    }


    /**
     * 没有支付方式走流程？手动整一个
     */
    public static PaymentBean packingPayMethod(String currency, String isBalanceEnough, String feeAmt, String taxAmt) {
        PaymentBean paymentBean = new PaymentBean();
        List<PaymentBean.PaymentMethodListBean> paymentMethodListBeans = new ArrayList<>();

        PaymentBean.PaymentMethodListBean paymentMethodListBean = new PaymentBean.PaymentMethodListBean();
        paymentMethodListBean.setIsBalanceEnough(isBalanceEnough);
        paymentMethodListBean.setIsAvailable("1");
        paymentMethodListBean.setIsSelected("1");
        paymentMethodListBean.setPaymentType("02");
        paymentMethodListBean.setCurrency(currency);
        paymentMethodListBeans.add(paymentMethodListBean);
        paymentBean.setPaymentMethodList(paymentMethodListBeans);

        List<PaymentBean.PaymentTypeListBean> typeList = new ArrayList<>();
        PaymentBean.PaymentTypeListBean paymentTypeBean = new PaymentBean.PaymentTypeListBean();
        paymentTypeBean.setFeeAmt(feeAmt);
        paymentTypeBean.setTaxAmt(taxAmt);
        paymentTypeBean.setPaymentType("02");
        paymentMethodListBean.setCurrency(currency);
        typeList.add(paymentTypeBean);
        paymentBean.setPaymentTypeList(typeList);
        return paymentBean;
    }

    /**
     * 获取支付名称
     *
     * @param context
     * @param paymentMethodListBean 支付方式实体类
     * @return 支付名称
     */
    public static String getPaymentMethodName(Context context, PaymentBean.PaymentMethodListBean paymentMethodListBean) {
        if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "02")) {
            //余额支付
            return context.getString(R.string.oneMoney) + " - " + CurrencyUtils.setCurrency(context, paymentMethodListBean.getCurrency());
        } else if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "03")) {
            //银行支付
            return paymentMethodListBean.getBankName() + " (" + paymentMethodListBean.getCardNo() + ")";
        } else {
            return "";
        }
    }

    /**
     * 处理余额的显示位置
     *
     * @param paymentMethodList
     */
    public static List<PaymentBean.PaymentMethodListBean> handlerBalanceList(List<PaymentBean.PaymentMethodListBean> paymentMethodList, boolean isOpenBank) {
        int index = -1, indexUnAvailable = -1;
        PaymentBean.PaymentMethodListBean balanceBean = null;
        if (!paymentMethodList.contains(sAddBean) && isOpenBank) {
            sAddBean = new PaymentBean.PaymentMethodListBean();
            sAddBean.setPaymentType("99");
            sAddBean.setIsOpen("1");
            paymentMethodList.add(sAddBean);
        }

        //余额不足的放在后面
        for (int i = 0; i < paymentMethodList.size(); i++) {
            PaymentBean.PaymentMethodListBean paymentMethodListBean = paymentMethodList.get(i);
            if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "02") && TextUtils.equals(paymentMethodListBean.getIsBalanceEnough(), "0")) {
                index = i;
                balanceBean = paymentMethodListBean;
                break;
            }
        }

        if (index != -1) {
            paymentMethodList.remove(index);
            paymentMethodList.add(balanceBean);
        }

        //不可用的放在后面
        for (int i = 0; i < paymentMethodList.size(); i++) {
            PaymentBean.PaymentMethodListBean paymentMethodListBean = paymentMethodList.get(i);
            if (TextUtils.equals(paymentMethodListBean.getPaymentType(), "02") && TextUtils.equals(paymentMethodListBean.getIsAvailable(), "0")) {
                indexUnAvailable = i;
                balanceBean = paymentMethodListBean;
                break;
            }
        }

        if (indexUnAvailable != -1) {
            paymentMethodList.remove(indexUnAvailable);
            paymentMethodList.add(balanceBean);
        }

        return paymentMethodList;
    }

    /**
     * 获取费率
     *
     * @param paymentType     支付类型
     * @param paymentTypeList 费率的集合
     */
    public static String getFeeAmt(String paymentType, List<PaymentBean.PaymentTypeListBean> paymentTypeList) {

        if (TextUtils.isEmpty(paymentType) || paymentTypeList.isEmpty()) {
            return "";
        }

        for (int i = 0; i < paymentTypeList.size(); i++) {
            PaymentBean.PaymentTypeListBean paymentTypeListBean = paymentTypeList.get(i);
            if (TextUtils.equals(paymentTypeListBean.getPaymentType(), paymentType) && TextUtils.equals(paymentTypeListBean.getDeductType(), "0")) {
                return paymentTypeListBean.getFeeAmt();
            }
        }

        return "";
    }

    public static PaymentBean.PaymentTypeListBean getPaymentType(PaymentBean.PaymentMethodListBean mSelectPaymentMethod, List<PaymentBean.PaymentTypeListBean> paymentTypeList) {
        if (TextUtils.isEmpty(mSelectPaymentMethod.getPaymentType()) || paymentTypeList.isEmpty()) {
            return new PaymentBean.PaymentTypeListBean();
        }

        for (int i = 0; i < paymentTypeList.size(); i++) {
            PaymentBean.PaymentTypeListBean paymentTypeListBean = paymentTypeList.get(i);
            if (TextUtils.equals(paymentTypeListBean.getPaymentType(), mSelectPaymentMethod.getPaymentType())) {
                if (!TextUtils.isEmpty(paymentTypeListBean.getCurrency())) {
                    paymentTypeListBean.setCurrency(mSelectPaymentMethod.getCurrency());
                }
                return paymentTypeListBean;
            }
        }

        return new PaymentBean.PaymentTypeListBean();
    }

    /**
     * 替换数据源 因为有本地数据,所以这里替换一下数据源
     * 这样处理的好处就是,之后的数据就不用全部更改了
     *
     * @param paymentBean       支付实体类
     * @param selectPaymentType 选择的类型
     * @return
     */
    public static PaymentBean replaceSource(PaymentBean paymentBean, String selectPaymentType) {

        List<PaymentBean.PaymentMethodListBean> paymentMethodList = paymentBean.getPaymentMethodList();

        if (paymentMethodList == null || paymentMethodList.size() == 0) {
            return paymentBean;
        }

        //获取选择的支付类型
        PaymentBean.PaymentMethodListBean paymentMethodListBean = new Gson().fromJson(selectPaymentType, PaymentBean.PaymentMethodListBean.class);

        for (int i = 0; i < paymentMethodList.size(); i++) {
            //获取支付的方式
            PaymentBean.PaymentMethodListBean currentPaymentMethod = paymentMethodList.get(i);

            if ((TextUtils.equals(paymentMethodListBean.getPaymentType(), currentPaymentMethod.getPaymentType()) &&
                    TextUtils.equals(paymentMethodListBean.getPaymentType(), "02")) ||
                    (TextUtils.equals(paymentMethodListBean.getPaymentType(), currentPaymentMethod.getPaymentType()) &&
                            TextUtils.equals(paymentMethodListBean.getPaymentType(), "03") &&
                            TextUtils.equals(paymentMethodListBean.getCardNo(), currentPaymentMethod.getCardNo()))) {

                paymentMethodListBean.setIsSelected("1");
                paymentMethodList.set(i, paymentMethodListBean);
            } else {
                currentPaymentMethod.setIsSelected("0");
            }
        }

        return paymentBean;
    }


    /**
     * 是否开通银行卡支付
     *
     * @param paymentTypeList 支付类型
     */
    public static boolean isOpenBank(List<PaymentBean.PaymentTypeListBean> paymentTypeList) {
        if (paymentTypeList == null || paymentTypeList.size() == 0) {
            return false;
        }

        for (PaymentBean.PaymentTypeListBean paymentTypeListBean : paymentTypeList) {
            if (TextUtils.equals(paymentTypeListBean.getIsOpen(), "1") && TextUtils.equals(paymentTypeListBean.getPaymentType(), "03")) {
                return true;
            }
        }

        return false;
    }
}
