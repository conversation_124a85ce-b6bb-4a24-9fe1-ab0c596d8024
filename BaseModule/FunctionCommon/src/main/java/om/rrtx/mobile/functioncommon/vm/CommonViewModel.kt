package om.rrtx.mobile.functioncommon.vm

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import om.rrtx.mobile.functioncommon.bean.PubBean
import om.rrtx.mobile.functioncommon.model.CommonModel
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.bean.CodeBean
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils

class CommonViewModel(application: Application) : AndroidViewModel(application) {

    private val mModel = CommonModel()
    val mCodeBeanLD = MutableLiveData<CodeBean>()
    val mCodeVerifyLD = MutableLiveData<Any>()
    val mChangeMasterLD = MutableLiveData<Any>()
    fun requestCode(mobile: String, messageTemplateType: String, mobileAreaCode: String = "") {
        val activity = ActivityController.getInstance().currentActivity()
        mModel.requestCode(mobile,
            messageTemplateType,
            mobileAreaCode,
            object : BaseObserverNoError<CodeBean>(activity) {
                override fun requestSuccess(bean: CodeBean) {
                    mCodeBeanLD.value = bean
                }
            })
    }

    fun requestValidateSms(mobile: String, smsCode: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mModel.requestValidateSms(mobile, smsCode, object : BaseObserverNoError<Any>(activity) {
            override fun requestSuccess(bean: Any) {
                mCodeVerifyLD.value = bean
            }
        })
    }

    /**
     * 切换主设备
     *
     * @param tempDeviceId
     */
    fun requestChangeMasterDevice(tempDeviceId: String) {
        val activity = ActivityController.getInstance().currentActivity()
        val pubLick = SharedPreferencesUtils.getParam(
            activity,
            BaseConstants.SaveParameter.PUBLICKEY,
            ""
        ) as String
        val userId = SharedPreferencesUtils.getParam(
            activity,
            BaseConstants.SaveParameter.USERID,
            ""
        ) as String
        if (!TextUtils.isEmpty(pubLick)) {
            mModel.requestChangeMasterDevice(
                userId,
                tempDeviceId,
                object : BaseObserverNoError<Any>(activity) {
                    override fun requestSuccess(sResData: Any) {
                        mChangeMasterLD.value = sResData
                    }
                })
        } else {
            mModel.commonPub(object :
                BaseObserver<PubBean>(activity) {
                override fun requestSuccess(sResData: PubBean) {
                    SharedPreferencesUtils.setParam(
                        activity,
                        BaseConstants.SaveParameter.PUBLICKEY,
                        sResData.pubKeyBase64
                    )
                    mModel.requestChangeMasterDevice(
                        userId,
                        tempDeviceId,
                        object : BaseObserverNoError<Any>(activity) {
                            override fun requestSuccess(sResData: Any) {
                                mChangeMasterLD.value = sResData
                            }
                        })
                }

                override fun requestFail(sResMsg: String) {}
            })
        }
    }
}