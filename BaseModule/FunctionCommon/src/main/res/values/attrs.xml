<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--密码控件-->
    <declare-styleable name="Common_PasswordEditText">
        <!-- 密码的个数 -->
        <attr name="common_passwordNumber" format="integer" />
        <!-- 密码圆点的半径 -->
        <attr name="common_passwordRadius" format="dimension" />
        <!-- 密码圆点的颜色 -->
        <attr name="common_passwordColor" format="color" />
        <!-- 分割线的颜色 -->
        <attr name="common_divisionLineColor" format="color" />
        <!-- 分割线的大小 -->
        <attr name="common_divisionLineSize" format="color" />
        <!-- 背景边框的颜色 -->
        <attr name="common_bgColor" format="color" />
        <!-- 背景边框的大小 -->
        <attr name="common_bgSize" format="dimension" />
        <!-- 背景边框的圆角大小 -->
        <attr name="common_bgCorner" format="dimension" />
    </declare-styleable>

    <declare-styleable name="Lock9View">
        <attr name="lock9_nodeSrc" format="color|reference" />
        <attr name="lock9_nodeOnSrc" format="color|reference" />
        <attr name="lock9_nodeSize" format="dimension" />
        <attr name="lock9_nodeAreaExpand" format="dimension" />
        <attr name="lock9_nodeOnAnim" format="reference" />
        <attr name="lock9_lineColor" format="color" />
        <attr name="lock9_lineWidth" format="dimension" />
        <attr name="lock9_padding" format="dimension" />
        <attr name="lock9_spacing" format="dimension" />
        <attr name="lock9_autoLink" format="boolean" />
        <attr name="lock9_enableVibrate" format="boolean" />
        <attr name="lock9_vibrateTime" format="integer" />
    </declare-styleable>
</resources>