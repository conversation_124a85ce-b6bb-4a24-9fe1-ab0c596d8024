<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.AddBankSuccessActivity">


    <include
        android:id="@+id/include_title"
        layout="@layout/common_base_title" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="88pt"
        android:layout_height="88pt"
        android:layout_marginTop="120pt"
        android:contentDescription="@string/common_app_name"
        android:src="@drawable/common_ic_success_ye"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <TextView
        android:id="@+id/statusTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60pt"
        android:text="@string/success"
        android:textColor="@color/color_00AB4D"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cvBg"
        android:layout_width="690pt"
        android:layout_height="wrap_content"
        android:layout_marginTop="40pt"
        app:cardBackgroundColor="@color/color_FFFFFF"
        app:cardCornerRadius="10pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusTv">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/text1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="40pt"
                android:text="@string/common_label_fullname"
                android:textColor="@color/color_9B9B9B"
                android:textSize="22pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/text1Tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="4pt"
                android:layout_marginRight="30pt"
                android:textColor="@color/color_131313"
                android:textSize="32pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text1"
                tools:text="Payment Method" />


            <TextView
                android:id="@+id/text2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="20pt"
                android:text="@string/bankcard_label_bankname"
                android:textColor="@color/color_9B9B9B"
                android:textSize="22pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text1Tv" />

            <TextView
                android:id="@+id/text2Tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="4pt"
                android:layout_marginRight="30pt"
                android:textColor="@color/color_131313"
                android:textSize="32pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text2"
                tools:text="Internet Banking Top Up" />


            <TextView
                android:id="@+id/text3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="20pt"
                android:text="@string/bankcard_label_cardno"
                android:textColor="@color/color_9B9B9B"
                android:textSize="22pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text2Tv" />

            <TextView
                android:id="@+id/text3Tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginTop="4pt"
                android:layout_marginBottom="30pt"
                android:textColor="@color/color_131313"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text3"
                tools:text="2020.02.09 19:02:28" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/doneTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="60pt"
        android:background="@drawable/common_usable_btn"
        android:gravity="center"
        android:text="@string/done"
        android:textColor="@color/color_FFFFFF"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cvBg" />
</androidx.constraintlayout.widget.ConstraintLayout>