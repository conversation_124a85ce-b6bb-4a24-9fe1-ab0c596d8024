<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/common_white_corner_16_bg"
        android:layout_width="match_parent"
        android:layout_height="900pt">

        <View
            android:id="@+id/titleBg"
            android:layout_width="match_parent"
            android:layout_height="90pt"
            android:background="@drawable/common_white_corner_16_bg"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iconIv"
            android:layout_width="44pt"
            android:layout_height="44pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/common_icon_logo"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/titleBg"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="@id/titleBg"
            app:layout_constraintRight_toLeftOf="@id/titleTv"
            app:layout_constraintTop_toTopOf="@id/titleBg" />

        <View
            android:id="@+id/leftBg"
            android:layout_width="90pt"
            android:layout_height="90pt"
            app:layout_constraintBottom_toBottomOf="@id/titleBg"
            app:layout_constraintLeft_toLeftOf="@id/titleBg"
            app:layout_constraintTop_toTopOf="@id/titleBg" />

        <ImageView
            android:id="@+id/backIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/common_ic_close"
            app:layout_constraintBottom_toBottomOf="@id/leftBg"
            app:layout_constraintLeft_toLeftOf="@id/leftBg"
            app:layout_constraintRight_toRightOf="@id/leftBg"
            app:layout_constraintTop_toTopOf="@id/leftBg" />

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12pt"
            android:text="@string/Payment_Details"
            android:textColor="@color/color_212121"
            android:textSize="36pt"
            app:layout_constraintBottom_toBottomOf="@id/titleBg"
            app:layout_constraintLeft_toRightOf="@id/iconIv"
            app:layout_constraintRight_toRightOf="@id/titleBg"
            app:layout_constraintTop_toTopOf="@id/titleBg" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintTop_toBottomOf="@id/titleBg"
            />

        <TextView
            android:id="@+id/amtTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20pt"
            android:textColor="@color/common_ye_F3881E"
            android:textSize="48pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleBg"
            tools:text="90.00" />


        <TextView
            android:id="@+id/tab1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="20pt"
            android:text="@string/checkout_label_mer_name"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/amtTv" />

        <TextView
            android:id="@+id/tab1Tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab1" />
        <TextView
            android:id="@+id/tab1Tv_1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/common_text_86909C"
            android:textSize="28pt"
            android:gravity="right"
            app:layout_constraintTop_toTopOf="@id/tab1Tv"
            app:layout_constraintBottom_toBottomOf="@id/tab1Tv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@id/tab1Tv"
            android:layout_marginLeft="10pt"
            android:layout_marginRight="30pt"
            />

        <TextView
            android:id="@+id/tab2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="10pt"
            android:text="@string/checkout_label_order_no"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab1Tv" />

        <TextView
            android:id="@+id/tab2Tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:layout_marginRight="30pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab2" />


<!--        //Order Info-->
        <TextView
            android:id="@+id/tab3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="10pt"
            android:text="@string/order_info"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab2Tv" />

        <TextView
            android:id="@+id/tab3Tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:layout_marginRight="30pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab3" />

        <TextView
            android:id="@+id/order_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10pt"
            android:textColor="@color/common_text_86909C"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="32pt"
            app:layout_constraintTop_toBottomOf="@id/tab3Tv"
            android:text="@string/order_Amount" />

        <TextView
            android:id="@+id/orderTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            android:layout_marginTop="10pt"
            app:layout_constraintTop_toBottomOf="@+id/order_tab"
            app:layout_constraintLeft_toLeftOf="@id/order_tab"
            tools:text="USD dsasdsa" />

        <TextView
            android:id="@+id/fee_tab"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10pt"
            android:textColor="@color/common_text_86909C"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="32pt"
            app:layout_constraintTop_toBottomOf="@id/orderTv"
            android:text="@string/fee" />

        <TextView
            android:id="@+id/feeTv"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            android:layout_marginTop="10pt"
            app:layout_constraintTop_toBottomOf="@+id/fee_tab"
            app:layout_constraintLeft_toLeftOf="@id/fee_tab"
            tools:text="USD dsasdsa" />


        <TextView
            android:id="@+id/tax_tab"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10pt"
            android:textColor="@color/common_text_86909C"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="32pt"
            app:layout_constraintTop_toBottomOf="@id/feeTv"
            android:text="@string/tax" />

        <TextView
            android:id="@+id/taxTv"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            android:layout_marginTop="10pt"
            app:layout_constraintTop_toBottomOf="@+id/tax_tab"
            app:layout_constraintLeft_toLeftOf="@id/tax_tab"
            tools:text="USD tax" />

        <TextView
            android:id="@+id/paymentMethod"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="24pt"
            android:text="@string/payment_method"
            android:textColor="@color/color_999999"
            android:textSize="22pt"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/taxTv" />

        <TextView
            android:id="@+id/paymentMethodTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32pt"
            android:layout_marginTop="6pt"
            android:layout_marginRight="56pt"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="@string/checkout_btn_please"
            android:textColor="@color/color_999999"
            android:textSize="32pt"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/paymentMethod" />

        <TextView
            android:id="@+id/payHint_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/insufficient_balance"
            android:visibility="gone"
            android:textColor="@color/common_red_F23D1D"
            android:textSize="22pt"
            android:layout_marginRight="30pt"
            app:layout_constraintRight_toLeftOf="@id/paymentMethodIv"
            app:layout_goneMarginRight="4pt"
            app:layout_constraintTop_toTopOf="@id/paymentMethodTv"
            app:layout_constraintBottom_toBottomOf="@id/paymentMethodTv"
            />

        <ImageView
            android:id="@+id/paymentMethodIv"
            android:layout_width="16pt"
            android:layout_height="16pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/common_ic_sanjiao_right"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/paymentMethodTv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/paymentMethodTv"
            app:tint="@color/common_ye_F3881E" />

        <View
            android:id="@+id/paymentMethodView"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="10pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/paymentMethodTv" />

        <TextView
            android:id="@+id/payTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginBottom="60pt"
            android:background="@drawable/common_btn_bg"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/pay"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
