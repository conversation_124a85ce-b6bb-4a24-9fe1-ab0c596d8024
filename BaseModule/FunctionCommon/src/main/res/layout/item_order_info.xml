<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginRight="30pt">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_text_86909C"
            android:textSize="22pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv" />

        <TextView
            android:id="@+id/hint_tv"
            android:textSize="22pt"
            android:gravity="right"
            android:textColor="@color/common_red_F23D1D"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="20pt"
            android:layout_marginLeft="160pt"
            app:layout_constraintLeft_toRightOf="@id/content_tv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/content_tv"
            app:layout_constraintBottom_toBottomOf="@id/content_tv"
            android:text="@string/insufficient_balance"
            />

        <ImageView
            android:id="@+id/more_iv"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@id/content_tv"
            app:layout_constraintBottom_toBottomOf="@id/content_tv"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="16pt"
            android:layout_height="16pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/common_ic_sanjiao_right"
            app:tint="@color/common_ye_F3881E"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>