<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="MissingDefaultResource">

        <include
            android:id="@+id/include_title"
            layout="@layout/common_base_title" />

        <ImageView
            android:id="@+id/icon"
            android:layout_width="200pt"
            android:layout_height="200pt"
            android:layout_marginTop="160pt"
            android:background="@drawable/common_ic_success_ye"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />


        <TextView
            android:id="@+id/text1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16pt"
            android:fontFamily="sans-serif-condensed-medium"
            android:textColor="@color/common_text_1d2129"
            android:textSize="36pt"
            android:text="@string/successful"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/icon" />

        <TextView
            android:id="@+id/text3"
            android:layout_width="0pt"
            android:layout_height="wrap_content"
            android:layout_marginTop="16pt"
            android:textColor="@color/common_text_4E5969"
            android:textSize="30pt"
            android:layout_marginLeft="50pt"
            android:layout_marginRight="50pt"
            android:gravity="center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text1" />


        <TextView
            android:id="@+id/nextTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="589pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:text="@string/done"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/icon" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>