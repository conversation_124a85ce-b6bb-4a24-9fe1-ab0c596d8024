<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_white_corner_16_bg">

        <!--标题-->
        <include
            android:background="@drawable/common_white_corner_16_bg"
            android:id="@+id/include_title"
            layout="@layout/common_base_title" />
        <View
            android:id="@+id/view_line"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintTop_toBottomOf="@id/include_title"
            />

        <TextView
            android:id="@+id/hint_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="28pt"
            android:gravity="center"
            android:textColor="@color/common_text_1d2129"
            android:layout_marginTop="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginLeft="135pt"
            android:layout_marginRight="135pt"
            app:layout_constraintTop_toBottomOf="@id/view_line"
            />

        <om.rrtx.mobile.functioncommon.widget.PayPasswordView
            android:id="@+id/ppv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_line" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
