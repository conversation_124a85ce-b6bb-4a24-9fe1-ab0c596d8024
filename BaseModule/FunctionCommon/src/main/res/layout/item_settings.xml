<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="98pt"
        cornerBackgroundRadius="@{10}"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        >

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="0pt"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:textSize="28pt"
            android:textColor="@color/common_text_1d2129"
            />

        <ImageView
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:src="@drawable/security_ic_more_arr"
            android:layout_marginRight="24pt"
            />
    </LinearLayout>
</layout>