<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:clickable="true"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="800pt"
        android:background="@drawable/common_white_corner_16_bg">

        <View
            android:id="@+id/titleBg"
            android:layout_width="match_parent"
            android:layout_height="90pt"
            android:background="@drawable/common_white_corner_16_bg"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12pt"
            android:text="@string/Payment_Details"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            app:layout_constraintBottom_toBottomOf="@id/titleBg"
            app:layout_constraintLeft_toLeftOf="@id/titleBg"
            app:layout_constraintRight_toRightOf="@id/titleBg"
            app:layout_constraintTop_toTopOf="@id/titleBg" />


        <ImageView
            android:id="@+id/backIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:contentDescription="@string/base_app_name"
            android:src="@drawable/common_ic_close"
            app:layout_constraintBottom_toBottomOf="@id/leftBg"
            app:layout_constraintLeft_toLeftOf="@id/leftBg"
            app:layout_constraintRight_toRightOf="@id/leftBg"
            app:layout_constraintTop_toTopOf="@id/leftBg" />

        <View
            android:id="@+id/leftBg"
            android:layout_width="90pt"
            android:layout_height="90pt"
            app:layout_constraintBottom_toBottomOf="@id/titleBg"
            app:layout_constraintLeft_toLeftOf="@id/titleBg"
            app:layout_constraintTop_toTopOf="@id/titleBg" />

        <View
            android:id="@+id/lin"
            app:layout_constraintTop_toBottomOf="@id/titleBg"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/bankListRv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            android:scrollbars="none"
            app:layout_constraintTop_toBottomOf="@id/lin"
            tools:itemCount="10"
            tools:listitem="@layout/common_item_bank_list" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>