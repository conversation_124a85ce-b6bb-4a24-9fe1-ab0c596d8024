<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="98pt"
    android:layout_marginLeft="30pt"
    android:layout_marginTop="20pt"
    android:layout_marginRight="30pt"
    android:orientation="vertical"
    app:cardCornerRadius="10pt"
    app:cardElevation="5pt">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="98pt">

        <TextView
            android:id="@+id/accountTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="adsklajsdlkajdlk" />

        <ImageView
            android:id="@+id/selectIv"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_marginLeft="20pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/common_ic_selected_bank"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>