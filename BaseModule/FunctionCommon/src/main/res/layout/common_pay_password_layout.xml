<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_FFFFFF"
    android:orientation="vertical">

    <om.rrtx.mobile.functioncommon.widget.PasswordEditText
        android:id="@+id/pet_passed"
        android:layout_width="match_parent"
        android:layout_height="221pt"
        android:background="@null"
        app:layout_constraintTop_toTopOf="parent"
        app:common_bgColor="@color/color_C9CDD4"
        app:common_divisionLineColor="@color/color_C9CDD4"
        app:common_passwordColor="@color/color_131313"
        app:common_passwordNumber="4"
        app:common_passwordRadius="9pt" />

<!--  忘记PIN码，隐藏-->
    <TextView
        android:id="@+id/forgotTv"
        android:layout_width="wrap_content"
        android:layout_height="70pt"
        android:layout_gravity="right"
        android:layout_marginRight="30pt"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/common_btn_forgot_pay_pin"
        android:textColor="@color/color_358cff"
        android:textSize="28pt" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="60pt"
        android:background="@color/color_D6D9DE">

        <ImageView
            android:id="@+id/lockIv"
            android:layout_width="36pt"
            android:layout_height="36pt"
            android:layout_marginLeft="30pt"
            android:src="@drawable/common_ic_lock"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="20pt"
            android:gravity="center_vertical"
            android:text="@string/common_keyboard_tip"
            android:textColor="@color/color_131313"
            android:textSize="22pt"
            app:layout_constraintLeft_toRightOf="@id/lockIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/common_custom_keyboard" />
</LinearLayout>