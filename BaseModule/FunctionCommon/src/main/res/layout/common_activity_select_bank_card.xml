<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.SelectBankCardActivity">


    <include
        android:id="@+id/include_title"
        layout="@layout/common_base_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/contentRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />
</androidx.constraintlayout.widget.ConstraintLayout>