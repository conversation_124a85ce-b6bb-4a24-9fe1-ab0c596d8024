<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg_f7f8fa">

        <include
            android:id="@+id/include_title"
            layout="@layout/common_base_title" />

        <TextView
            android:id="@+id/hint2_tv"
            android:layout_width="0pt"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40pt"
            android:layout_marginRight="40pt"
            android:gravity="center"
            android:text="@string/send_ver_code"
            android:textColor="@color/common_text_1d2129"
            android:fontFamily="sans-serif-condensed-medium"
            android:textSize="34pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title"
            android:layout_marginTop="100pt"
            />

        <TextView
            android:id="@+id/mobile_tv"
            android:layout_width="0pt"
            android:layout_height="wrap_content"
            android:layout_marginLeft="40pt"
            android:layout_marginTop="16pt"
            android:layout_marginRight="40pt"
            android:gravity="center"
            android:textColor="@color/common_text_4E5969"
            android:textSize="34pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint2_tv" />


        <om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText
            android:id="@+id/code"
            android:layout_width="match_parent"
            android:layout_height="100pt"
            android:layout_marginLeft="60pt"
            android:layout_marginTop="346pt"
            android:layout_marginRight="60pt"
            android:inputType="number"
            android:textSize="48pt"
            app:bottomLineHeight="2pt"
            app:bottomLineNormalColor="@color/color_C9CDD4"
            app:bottomLineSelectedColor="@color/color_C9CDD4"
            app:cursorColor="@color/common_ye_F3881E"
            app:cursorWidth="2pt"
            app:figures="6"
            app:layout_constraintTop_toBottomOf="@+id/include_title"
            app:selectedBackgroundColor="@android:color/transparent"
            app:unselectedBackgroundColor="@android:color/transparent"
            app:verCodeMargin="18pt" />

        <TextView
            android:id="@+id/tv_down_time"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="116pt"
            android:gravity="center"
            android:textColor="@color/common_text_86909C"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/code" />

        <TextView
            android:id="@+id/tv_send"
            android:layout_width="match_parent"
            android:layout_height="80pt"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="116pt"
            android:layout_marginRight="30pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:text="@string/resend"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintTop_toBottomOf="@id/code" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>