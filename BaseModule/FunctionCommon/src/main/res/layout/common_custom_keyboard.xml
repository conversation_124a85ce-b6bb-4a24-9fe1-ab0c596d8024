<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/keyboard"
    android:layout_width="match_parent"
    android:layout_height="450pt"
    android:background="@color/color_d2d5db"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_1"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_1"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_2"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_2"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_2"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_5"
        app:layout_constraintLeft_toRightOf="@+id/tv_1"
        app:layout_constraintRight_toLeftOf="@+id/tv_3"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_3"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_3"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_6"
        app:layout_constraintLeft_toRightOf="@+id/tv_2"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_4"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_4"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_5"
        app:layout_constraintTop_toBottomOf="@+id/tv_1" />

    <TextView
        android:id="@+id/tv_5"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_5"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_8"
        app:layout_constraintLeft_toRightOf="@+id/tv_4"
        app:layout_constraintRight_toLeftOf="@+id/tv_6"
        app:layout_constraintTop_toBottomOf="@+id/tv_2" />

    <TextView
        android:id="@+id/tv_6"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_6"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_9"
        app:layout_constraintLeft_toRightOf="@+id/tv_5"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_3" />

    <TextView
        android:id="@+id/tv_7"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_7"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_empty"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_8"
        app:layout_constraintTop_toBottomOf="@+id/tv_4" />

    <TextView
        android:id="@+id/tv_8"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_8"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_0"
        app:layout_constraintLeft_toRightOf="@+id/tv_7"
        app:layout_constraintRight_toLeftOf="@+id/tv_9"
        app:layout_constraintTop_toBottomOf="@+id/tv_5" />

    <TextView
        android:id="@+id/tv_9"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_9"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toTopOf="@+id/tv_delete"
        app:layout_constraintLeft_toRightOf="@+id/tv_8"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_6" />

    <TextView
        android:id="@+id/tv_empty"
        android:layout_width="235pt"
        android:layout_height="99pt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_0"
        app:layout_constraintTop_toBottomOf="@+id/tv_7" />

    <TextView
        android:id="@+id/tv_0"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:background="@drawable/common_drawable_keyboard_bg"
        android:gravity="center"
        android:text="@string/common_label_0"
        android:textColor="@color/color_131313"
        android:textSize="50pt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_empty"
        app:layout_constraintRight_toLeftOf="@+id/tv_delete"
        app:layout_constraintTop_toBottomOf="@+id/tv_8" />

    <ImageView
        android:id="@+id/tv_delete"
        android:layout_width="235pt"
        android:layout_height="99pt"
        android:contentDescription="@string/base_app_name"
        android:paddingLeft="80pt"
        android:paddingTop="20pt"
        android:paddingRight="80pt"
        android:paddingBottom="20pt"
        android:src="@drawable/common_ic_delete"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_0"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_9" />

</androidx.constraintlayout.widget.ConstraintLayout>