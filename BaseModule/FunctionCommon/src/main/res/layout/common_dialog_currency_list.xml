<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="458pt"
    android:id="@+id/view"
    android:background="@drawable/common_white_corner_16_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="32pt"
        android:layout_marginTop="36pt"
        android:text="@string/common_alert_cancel"
        android:textColor="@color/common_text_86909C"
        android:textSize="28pt"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="36pt"
        android:textColor="@color/color_000000"
        android:textSize="28pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="36pt"
        android:layout_marginRight="32pt"
        android:text="@string/common_btn_confirm"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="28pt"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:layout_below="@id/tv_title"
        android:layout_marginTop="24pt"
        android:background="@color/color_E5E6EB"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <View
        android:id="@+id/line2"
        android:layout_width="match_parent"
        android:layout_height="60pt"
        android:layout_alignParentBottom="true"
        android:background="@color/color_FFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/selectRv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/line2"
        android:layout_below="@id/line"
        android:layout_marginLeft="32pt"
        android:layout_marginTop="8pt"
        android:layout_marginRight="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

</RelativeLayout>