<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.AddBankActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/common_base_title" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/fullNameTil"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="50pt"
        android:layout_marginRight="30pt"
        android:hint="@string/common_label_fullname"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/fullNameTie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:enabled="false"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:inputType="text"
            android:maxLength="50"
            android:paddingRight="84pt"
            android:textColor="@color/color_131313" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/bankSelectTil"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="20pt"
        android:layout_marginRight="30pt"
        android:hint="@string/bankcard_label_bankname"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fullNameTil">

        <om.rrtx.mobile.rrtxcommon1.widget.MyTextInputEditText
            android:id="@+id/bankSelectTie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:inputType="text"
            android:maxLength="50"
            android:paddingRight="84pt"
            android:textColor="@color/color_131313" />
    </com.google.android.material.textfield.TextInputLayout>

    <ImageView
        android:id="@+id/bankSelectIv"
        android:layout_width="24pt"
        android:layout_height="24pt"
        android:layout_marginBottom="30pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/common_ic_sanjiao_right"
        app:layout_constraintBottom_toBottomOf="@id/bankSelectTil"
        app:layout_constraintRight_toRightOf="@id/bankSelectTil" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/cardNoTil"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="20pt"
        android:layout_marginRight="30pt"
        android:hint="@string/bankcard_label_cardno"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bankSelectTil">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/cardNoTie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="23"
            android:paddingRight="84pt"
            android:textColor="@color/color_131313" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/nextTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="60pt"
        android:background="@drawable/common_unusable_btn"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/bankcard_btn_bind"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardNoTil" />
</androidx.constraintlayout.widget.ConstraintLayout>