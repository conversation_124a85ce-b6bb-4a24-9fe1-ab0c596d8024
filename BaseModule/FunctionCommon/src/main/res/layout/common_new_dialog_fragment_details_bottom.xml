<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_white_corner_16_bg"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="53pt"
            android:background="@drawable/common_white_corner_16_bg"
            app:layout_constraintBottom_toTopOf="@id/payTv">

            <View
                android:id="@+id/titleBg"
                android:layout_width="match_parent"
                android:layout_height="90pt"
                android:background="@drawable/common_white_corner_16_bg"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/titleTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12pt"
                android:text="@string/Payment_Details"
                android:textColor="@color/common_text_1d2129"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/titleBg"
                app:layout_constraintLeft_toLeftOf="@id/titleBg"
                app:layout_constraintRight_toRightOf="@id/titleBg"
                app:layout_constraintTop_toTopOf="@id/titleBg" />


            <ImageView
                android:id="@+id/backIv"
                android:layout_width="30pt"
                android:layout_height="30pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/common_ic_close"
                app:layout_constraintBottom_toBottomOf="@id/leftBg"
                app:layout_constraintLeft_toLeftOf="@id/leftBg"
                app:layout_constraintRight_toRightOf="@id/leftBg"
                app:layout_constraintTop_toTopOf="@id/leftBg" />

            <View
                android:id="@+id/leftBg"
                android:layout_width="90pt"
                android:layout_height="90pt"
                app:layout_constraintBottom_toBottomOf="@id/titleBg"
                app:layout_constraintLeft_toLeftOf="@id/titleBg"
                app:layout_constraintTop_toTopOf="@id/titleBg" />


            <View
                android:layout_width="match_parent"
                android:layout_height="1pt"
                android:background="@color/color_E5E6EB"
                app:layout_constraintTop_toBottomOf="@id/titleBg" />

            <TextView
                android:id="@+id/amtTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="25pt"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="48pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/titleBg"
                tools:text="usd" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/orderInfo_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/amtTv" />

            <View
                android:id="@+id/line"
                android:background="@color/color_E5E6EB"
                android:layout_marginLeft="30pt"
                android:layout_marginRight="30pt"
                app:layout_constraintTop_toBottomOf="@id/orderInfo_rv"
                android:layout_width="match_parent"
                android:layout_height="2pt"/>
             </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/payTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:background="@drawable/common_btn_bg"
            android:gravity="center"
            android:text="@string/pay"
            android:textColor="@color/common_text_FFFFFF"
            android:textSize="34pt"
            android:layout_marginTop="53pt"
            android:layout_marginBottom="64pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

