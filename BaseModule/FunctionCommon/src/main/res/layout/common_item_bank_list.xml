<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iconIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="24pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/bank_default_selected" />

    <TextView
        android:id="@+id/bankNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:layout_marginTop="29pt"
        android:layout_toRightOf="@id/iconIv"
        android:textColor="@color/color_212121"
        android:textSize="32pt"
        tools:text="Citibank（0123）" />

    <TextView
        android:id="@+id/balanceHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/iconIv"
        android:layout_below="@id/bankNameTv"
        android:layout_marginRight="24pt"
        android:layout_marginLeft="20pt"
        android:text="@string/insufficient_balance"
        android:textColor="@color/color_999999"
        android:textSize="24pt"/>

    <ImageView
        android:layout_alignParentRight="true"
        android:id="@+id/selectIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginRight="30pt"
        android:layout_marginTop="24pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/common_ic_selected_bank"
        app:tint="@color/common_ye_F3881E" />

    <View
        android:layout_below="@id/balanceHint"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="29pt"
        android:layout_marginRight="30pt"
        android:background="@color/color_E5E5E5" />

</RelativeLayout>