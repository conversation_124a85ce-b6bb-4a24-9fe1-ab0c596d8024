<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_FFFFFF"
    >
    <include
        android:id="@+id/include_title"
        layout="@layout/common_base_title" />
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:paddingBottom="60pt"
        app:layout_constraintTop_toBottomOf="@+id/include_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="10pt"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_weight="1"
        >
        <TextView
            android:id="@+id/contentTv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="30pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>