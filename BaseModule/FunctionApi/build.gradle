apply plugin: 'com.android.library'

//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name
        // 控制日志Log 输出打印
        buildConfigField("boolean", "enableLog", "true")
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            // 控制日志Log 输出打印
            minifyEnabled true
            buildConfigField("boolean", "enableLog", "false")
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"
}