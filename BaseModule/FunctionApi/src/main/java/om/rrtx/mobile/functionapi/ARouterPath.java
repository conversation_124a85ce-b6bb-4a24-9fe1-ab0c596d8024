package om.rrtx.mobile.functionapi;

/**
 * <AUTHOR>
 * ARouter的跳转路径
 */
public class ARouterPath {

    /**
     * home模块的路径
     */
    public interface HomePath {
        String HomeActivity = "/home/<USER>";
        String MobileSuccessActivity = "/home/<USER>";
        String MoreFunctionActivity = "/home/<USER>";
        String JuniorAccountActivity = "/home/<USER>";
        String JuniorAccountManagementActivity = "/home/<USER>";
        String ShowInviteActivity = "/home/<USER>";
    }

    /**
     * login模块的路径
     */
    public interface LoginPath {
        /**
         * 登录页面
         */
        String LoginActivity = "/login/LoginActivity";
        String NewUserInfoActivity = "/login/NewUserInfoActivity";
        String ReguarUserInfoActivity = "/login/ReguarUserInfoActivity";
        String RegisterResultActivity = "/login/RegisterResultActivity";

        /**
         * 设置登录密码
         */
        String SetLoginPsdActivity = "/login/SetLoginPsdActivity";
        String ForgetPinActivity = "/login/ForgetPinActivity";
        String ResetPinActivity = "/login/ResetPinActivity";
        /**
         * 引导页
         */
        String SplashActivity = "/login/SplashActivity";
        /**
         * 临时设备登录
         */
        String TemporaryActivity = "/login/TemporaryActivity";

        String UpgradeAccountActivity = "/login/UpgradeAccountActivity";
    }

    /**
     * 安全中心模块的路径
     */
    public interface SecurityPath {
        String SecurityCenterActivity = "/security/SecurityCenterActivity";
        String SetPinActivity = "/security/PinPaymentActivity";
        String SetRegisterPinActivity = "/security/SetRegisterPinActivity";
        String PayPinSendActivity = "/security/PayPinSendActivity";
        String ChangePinActivity = "/security/ChangePinActivity";

        /**
         * 指纹登录页面
         */
        String LoginFingerLockActivity = "/security/LoginFingerLockActivity";
        String NoPinPayActivity = "/security/NoPinPayActivity";
        String AutoDebitListActivity = "/security/AutoDebitListActivity";
        String AutoDebitActivity = "/security/AutoDebitActivity";
        String LoginGestureActivity = "/security/GestureLoginActivity";
        /**
         * 密码登录页面
         */
        String LoginPasswordLockActivity = "/security/LoginPasswordLockActivity";
        String PinInputActivity = "/security/PinInputActivity";
    }

    /**
     * 转账页面的路径
     */
    public interface TransferPath {
        /**
         * 转账页面
         */
        String TransferActivity = "/transfer/TransferActivity";
        /**
         * 转账详情页面
         */
        String TransferDetailsActivity = "/transfer/TransferDetailsActivity";
        /**
         * 转账详情页面(固定金额)
         */
        String TransferFixedActivity = "/transfer/TransferFixedActivity";


        String TransferSuccessActivity = "/transfer/TransferSuccessActivity";
        String TransferSuccessNoStatusActivity = "/transfer/TransferSuccessNoStatusActivity";
        /**
         * 通用交易成功页
         */
        String ComTransferSuccessActivity = "/transfer/ComTransferSuccessActivity";



        /**
         * 历史页面
         */
        String HistoryActivity = "/transfer/HistoryActivity";
        String JuniorHistoryActivity = "/transfer/JuniorHistoryActivity";
        String QueryHistoryActivity = "/transfer/QueryHistoryActivity";
        /**
         * 历史页面
         */
        String HistoryTypeActivity = "/transfer/HistoryTypeActivity";
        /**
         * 搜索联系人
         */
        String SearchContactsActivity = "/transfer/SearchContactsActivity";

        /**
         * 添加联系人
         */
        String AddContactsActivity = "/transfer/AddContactsActivity";

        /**
         * 联系人列表
         */
        String ContactListActivity = "/transfer/ContactListActivity";
        /**
         * 提现页面
         */
        String WithdrawalActivity = "/transfer/WithdrawalActivity";
        /**
         * zipit交易
         * **/
        String ZipitTransActivity = "/transfer/ZipitTransActivity";
        String CrashOutActivity = "/transfer/CrashOutActivity";
        /**
         * 银行卡列表页面
         */
        String BankCardListActivity = "/transfer/BankCardListActivity";
        String CheckJuniorDealActivity = "/transfer/CheckJuniorDealActivity";
        /**
         * 联系人详情页
         */
        String ContactDetailsActivity = "/transfer/ContactDetailsActivity";
        /**
         * 转账详情页面
         */
        String HistoryDetailsActivity = "/transfer/HistoryDetailsActivity";
        /**
         * 支付订单页面
         */
        String HistoryPaymentActivity = "/transfer/HistoryPaymentActivity";
        /**
         * 提现订单页面
         */
        String WithdrawalTypeActivity = "/transfer/WithdrawalTypeActivity";
        /**
         * 历史账户页面
         */
        String HistoryAccountActivity = "/transfer/HistoryAccountActivity";
        /**
         * 亲子历史账户页面
         */
        String JuniorHistoryAccountActivity = "/transfer/JuniorHistoryAccountActivity";
        /**
         * 代客充值/提现页面
         */
        String AgentTopUpActivity = "/transfer/AgentTopUpActivity";
        /**
         * 代客转账页面
         */
        String HistoryAgentTranActivity = "/transfer/HistoryAgentTranActivity";
        /**
         * 代客话费充值页面
         */
        String HistoryAgentMobileActivity = "/transfer/HistoryAgentMobileActivity";
    }

    /**
     * 充值中心
     */
    public interface TopUp {
        String TopUpActivity = "/topUp/TopUpActivity";
    }

    /**
     * 收付款
     */
    public interface Payment {
        /**
         * 转账
         */
        String ReceivePaymentActiity = "/payment/ReceivePaymentActivity";
        /**
         * 扫码
         */
        String ScanCoceActiity = "/payment/ScanCodeActivity";
        /**
         * AA转账页面
         */
        String AASpiltBillActivity = "/payment/AASpiltBillActivity";
        /**
         * 缴费类型
         */
        String BillPayTypeActivity = "/payment/BillPayTypeActivity";
        /**
         * 话费
         */
        String BuyAirtimeActivity = "/payment/BuyAirtimeActivity";
        /**
         * AA付款
         */
        String SpiltBillActivity = "/payment/SpiltBillActivity";
        /**
         * 付款码
         */
        String MakePaymentAllActivity = "/payment/MakePaymentAllActivity";
        /**
         * 扫码固定金额页面
         */
        String BeSweptPaymentActivity = "/payment/BeSweptPaymentActivity";
        /**
         * 扫码非固定金额页面
         */
        String BeSweptUnfixedPaymentActivity = "/payment/BeSweptUnfixedPaymentActivity";
    }

    /**
     * 收银台模块
     */
    public interface Cashier {
        /**
         * 收银台页面
         */
        String CashierActivity = "/cashier/CashierActivity";
        String CashierPayActivity = "/cashier/CashierPayActivity";
        /**
         * 扫码调出收银台页面
         */
        String ScanCashierActivity = "/cashier/ScanCashierActivity";
    }

    /**
     * 收银台模块
     */
    public interface Promotion {
        /**
         * 商户推广首页
         */
        String MerListActivity = "/promotion/MerListActivity";
        /**
         * 商户推广首页
         */
        String ShowWebActivity = "/promotion/ShowWebActivity";
    }

    public interface Face {
        /**
         * 人脸识别
         */
        String AuthenticationHintActivity = "/face/AuthenticationHintActivity";
    }
}
