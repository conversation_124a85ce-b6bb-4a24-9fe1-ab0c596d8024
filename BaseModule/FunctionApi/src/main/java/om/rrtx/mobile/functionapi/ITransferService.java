package om.rrtx.mobile.functionapi;

import android.content.Context;

import java.net.URLDecoder;

/**
 * <AUTHOR>
 * 转账页面对外提供的服务
 */
public interface ITransferService {


    /**
     * <AUTHOR>
     * 展示底部对话框
     */
    public void showRegion(Context context, String[] aar, RegionCallBack callBack);

    public void setSelectLanguage(int current);
    /**
     * <AUTHOR>
     * 底部弹出选择对话框的回调
     */
    public interface RegionCallBack {

        void confirmCallBack(int currentPosition);

        void cancelCallBack();
    }
}
