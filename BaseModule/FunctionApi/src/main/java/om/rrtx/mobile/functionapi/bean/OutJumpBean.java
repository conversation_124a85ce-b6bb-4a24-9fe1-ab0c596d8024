package om.rrtx.mobile.functionapi.bean;

/**
 * <AUTHOR>
 * 外部跳转的标识
 */
public class OutJumpBean {

    /**
     * 判断类型
     */
    private String source;
    /**
     * 支付的token
     */
    private String payToken;
    private String transOrderNo;
    /**
     * 地址
     */
    private String address;
    /**
     * 是否是外部跳转
     */
    private String isOutJump;
    /**
     * 货币符号
     */
    private String currency;


    public void setTransOrderNo(String transOrderNo) {
        this.transOrderNo = transOrderNo;
    }

    public String getTransOrderNo() {
        return transOrderNo;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIsOutJump() {
        return isOutJump;
    }

    public void setIsOutJump(String isOutJump) {
        this.isOutJump = isOutJump;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
