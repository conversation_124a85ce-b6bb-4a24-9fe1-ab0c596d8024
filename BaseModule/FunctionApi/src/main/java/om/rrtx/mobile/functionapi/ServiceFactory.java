package om.rrtx.mobile.functionapi;

/**
 * <AUTHOR>
 * 对外暴露服务的组件
 */
public class ServiceFactory {
    private ServiceFactory mServiceFactory;

    private ServiceFactory() {
    }

    public static ServiceFactory getInstance() {
        return Inner.mServiceFactory;
    }

    private static class Inner {
        private static ServiceFactory mServiceFactory = new ServiceFactory();
    }

    /**
     * Payment的Service
     */
    private IPaymentService mPaymentService;

    public IPaymentService getPaymentService() {
        return mPaymentService;
    }

    public void setPaymentService(IPaymentService paymentService) {
        mPaymentService = paymentService;
    }

    /**
     * transfer的Service
     */
    private ITransferService mTransferService;

    public ITransferService getTransferService() {
        return mTransferService;
    }

    public void setTransferService(ITransferService transferService) {
        mTransferService = transferService;
    }

    /**
     * 充值的Service
     */
    private ITopUpService mTopUpService;

    public ITopUpService getTopUpService() {
        return mTopUpService;
    }

    public void setTopUpService(ITopUpService topUpService) {
        mTopUpService = topUpService;
    }

    /**
     * XStore的Service
     */
    private XStoreLibService mXStoreLibService;

    public XStoreLibService getXStoreLibService() {
        return mXStoreLibService;
    }

    public void setXStoreLibService(XStoreLibService XStoreLibService) {
        mXStoreLibService = XStoreLibService;
    }
}
