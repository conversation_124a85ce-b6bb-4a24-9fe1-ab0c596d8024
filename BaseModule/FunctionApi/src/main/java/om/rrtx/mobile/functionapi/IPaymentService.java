package om.rrtx.mobile.functionapi;

import android.app.Activity;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

/**
 * <AUTHOR>
 * 付款对外提供的服务
 */
public interface IPaymentService {

    /**
     * <AUTHOR>
     * 对外暴露相应的发起的付款Fragment
     */
    Fragment LssuedFragment();

    /**
     * <AUTHOR>
     * 对外暴露相应的收到的付款Fragment
     */
    Fragment ReceiveFragment();

}
